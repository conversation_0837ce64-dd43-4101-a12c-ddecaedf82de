"""
Tests for circular reference handling in cache manager.
"""

import pytest

from cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cache_manager.core.size_calculator import calculateSize


def test_circular_reference_in_cache():
    """Test that circular references can be cached without issues."""
    # Create a cache with larger size limit
    cache = CacheManager.createCache("circular_test", cacheType="sizeConstrained", maxSize=10 * 1024)  # 10KB

    # Create circular reference
    circular_dict = {"name": "test", "data": [1, 2, 3]}
    circular_dict["self"] = circular_dict

    # Check size first
    size = calculateSize(circular_dict)
    print(f"Circular dict size: {size} bytes")

    # Should be able to cache without errors
    cache.put("circular_key", circular_dict)

    # Should be able to retrieve
    retrieved = cache.get("circular_key")
    assert retrieved is not None
    assert retrieved["name"] == "test"
    assert retrieved["self"] is retrieved  # Verify circular reference is preserved

    # C<PERSON> should report some memory usage
    assert cache.memoryUsage() > 0


def test_circular_reference_size_calculation():
    """Test that circular references don't cause infinite recursion in size calculation."""
    # Simple circular list
    circular_list = [1, 2, 3]
    circular_list.append(circular_list)

    # Should not raise RecursionError
    size = calculateSize(circular_list)
    assert size > 0

    # Simple circular dict
    circular_dict = {"a": 1, "b": 2}
    circular_dict["self"] = circular_dict

    # Should not raise RecursionError
    size = calculateSize(circular_dict)
    assert size > 0


def test_complex_circular_structure():
    """Test complex circular structures."""
    # Create two objects that reference each other
    obj1 = {"name": "obj1", "data": [1, 2, 3]}
    obj2 = {"name": "obj2", "data": [4, 5, 6]}
    obj1["ref"] = obj2
    obj2["ref"] = obj1

    # Should be able to calculate size
    size1 = calculateSize(obj1)
    size2 = calculateSize(obj2)

    assert size1 > 0
    assert size2 > 0

    # Should be able to cache
    cache = CacheManager.createCache("complex_circular", cacheType="sizeConstrained", maxSize=10 * 1024)  # 10KB
    cache.put("obj1", obj1)
    cache.put("obj2", obj2)

    # Verify retrieval
    retrieved1 = cache.get("obj1")
    retrieved2 = cache.get("obj2")

    assert retrieved1["name"] == "obj1"
    assert retrieved2["name"] == "obj2"
    assert retrieved1["ref"] is retrieved2
    assert retrieved2["ref"] is retrieved1


def test_shared_objects_in_cache():
    """Test that shared objects are handled correctly."""
    # Create shared object
    shared_data = {"important": "data", "values": [1, 2, 3, 4, 5]}

    # Create container with shared references
    container = {"ref1": shared_data, "ref2": shared_data, "ref3": shared_data}

    # Should be able to calculate size and cache
    size = calculateSize(container)
    assert size > 0

    cache = CacheManager.createCache("shared_test", cacheType="sizeConstrained", maxSize=10 * 1024)  # 10KB
    cache.put("container", container)

    # Verify retrieval and shared references
    retrieved = cache.get("container")
    assert retrieved["ref1"] is retrieved["ref2"]
    assert retrieved["ref2"] is retrieved["ref3"]


def test_custom_object_with_circular_reference():
    """Test custom objects with circular references."""

    class Node:
        def __init__(self, value):
            self.value = value
            self.children = []
            self.parent = None

        def add_child(self, child):
            child.parent = self
            self.children.append(child)

    # Create a tree with circular references (parent-child)
    root = Node("root")
    child1 = Node("child1")
    child2 = Node("child2")

    root.add_child(child1)
    root.add_child(child2)

    # Should be able to calculate size
    size = calculateSize(root)
    assert size > 0

    # Should be able to cache
    cache = CacheManager.createCache("custom_circular", cacheType="sizeConstrained", maxSize=10 * 1024)  # 10KB
    cache.put("tree", root)

    # Verify retrieval
    retrieved = cache.get("tree")
    assert retrieved.value == "root"
    assert len(retrieved.children) == 2
    assert retrieved.children[0].parent is retrieved


def test_memory_usage_with_circular_references():
    """Test that memory usage is calculated reasonably for circular references."""
    cache = CacheManager.createCache("memory_test", cacheType="sizeConstrained", maxSize=1024 * 1024)  # 1MB

    # Create various circular structures
    circular_list = list(range(100))
    circular_list.append(circular_list)

    circular_dict = {f"key_{i}": f"value_{i}" for i in range(50)}
    circular_dict["self"] = circular_dict

    # Cache them
    cache.put("list", circular_list)
    cache.put("dict", circular_dict)

    # Memory usage should be reasonable (not infinite)
    memory_usage = cache.memoryUsage()
    assert 0 < memory_usage < 1024 * 1024  # Should be positive but less than max size

    # Should be able to retrieve
    assert cache.get("list") is not None
    assert cache.get("dict") is not None


def test_nested_circular_references():
    """Test deeply nested structures with circular references."""
    # Create a complex nested structure
    data = {"level1": {"level2": {"level3": {"level4": {"data": list(range(10))}}}}}

    # Add circular reference at different levels
    data["level1"]["level2"]["level3"]["level4"]["back_to_root"] = data
    data["level1"]["back_to_level2"] = data["level1"]["level2"]

    # Should handle without issues
    size = calculateSize(data)
    assert size > 0

    cache = CacheManager.createCache("nested_circular", cacheType="sizeConstrained", maxSize=10 * 1024)  # 10KB
    cache.put("nested", data)

    retrieved = cache.get("nested")
    assert retrieved["level1"]["level2"]["level3"]["level4"]["back_to_root"] is retrieved


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
