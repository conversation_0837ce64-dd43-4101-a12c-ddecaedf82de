"""
Comprehensive tests for the fixed size calculator.
"""

import sys
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import pytest

from cache_manager.core.size_calculator import calculateSize


class TestBasicTypes:
    """Test size calculation for basic Python types."""

    def test_none(self):
        """Test None value."""
        size = calculateSize(None)
        expected = sys.getsizeof(None)
        assert size == expected

    def test_integers(self):
        """Test integer values."""
        test_cases = [0, 1, -1, 42, 2**31, 2**63, 2**100]
        for value in test_cases:
            size = calculateSize(value)
            expected = sys.getsizeof(value)
            assert size == expected, f"Failed for integer {value}"

    def test_floats(self):
        """Test float values."""
        test_cases = [0.0, 1.0, -1.0, 3.14159, 1e10, 1e-10]
        for value in test_cases:
            size = calculateSize(value)
            expected = sys.getsizeof(value)
            assert size == expected, f"Failed for float {value}"

    def test_booleans(self):
        """Test boolean values."""
        for value in [True, False]:
            size = calculateSize(value)
            expected = sys.getsizeof(value)
            assert size == expected, f"Failed for boolean {value}"

    def test_strings(self):
        """Test string values."""
        test_cases = ["", "hello", "Hello, World!", "x" * 1000, "Unicode: world 🌍"]
        for value in test_cases:
            size = calculateSize(value)
            expected = sys.getsizeof(value)
            assert size == expected, f"Failed for string '{value[:20]}...'"

    def test_bytes(self):
        """Test bytes values."""
        test_cases = [b"", b"hello", b"x" * 1000]
        for value in test_cases:
            size = calculateSize(value)
            expected = sys.getsizeof(value)
            assert size == expected, f"Failed for bytes {len(value)} bytes"


class TestContainers:
    """Test size calculation for container types."""

    def test_empty_containers(self):
        """Test empty containers."""
        containers = [[], {}, set(), ()]
        for container in containers:
            size = calculateSize(container)
            expected = sys.getsizeof(container)
            assert size == expected, f"Failed for empty {type(container).__name__}"

    def test_simple_lists(self):
        """Test simple lists."""
        test_cases = [
            [1, 2, 3],
            ["a", "b", "c"],
            [1, "hello", 3.14],
        ]
        for lst in test_cases:
            size = calculateSize(lst)
            expected = sys.getsizeof(lst) + sum(sys.getsizeof(item) for item in lst)
            assert size == expected, f"Failed for list {lst}"

    def test_simple_dicts(self):
        """Test simple dictionaries."""
        test_cases = [
            {"a": 1, "b": 2},
            {1: "one", 2: "two"},
        ]
        for dct in test_cases:
            size = calculateSize(dct)
            expected = sys.getsizeof(dct) + sum(sys.getsizeof(k) + sys.getsizeof(v) for k, v in dct.items())
            assert size == expected, f"Failed for dict {dct}"

    def test_dict_with_nested_values(self):
        """Test dictionary with nested values."""
        dct = {"mixed": [1, 2, 3], "number": 42}
        size = calculateSize(dct)

        # Calculate expected size manually
        expected = sys.getsizeof(dct)
        expected += sys.getsizeof("mixed") + sys.getsizeof("number")
        expected += sys.getsizeof(42)
        # For the list value
        list_val = dct["mixed"]
        expected += sys.getsizeof(list_val) + sum(sys.getsizeof(item) for item in list_val)

        assert size == expected, f"Failed for dict with nested values: got {size}, expected {expected}"

    def test_nested_containers(self):
        """Test nested containers."""
        nested_list = [[1, 2], [3, 4], [5, 6]]
        size = calculateSize(nested_list)

        # Calculate expected size manually
        expected = sys.getsizeof(nested_list)
        for sublist in nested_list:
            expected += sys.getsizeof(sublist)
            for item in sublist:
                expected += sys.getsizeof(item)

        assert size == expected


class TestSharedObjects:
    """Test handling of shared objects."""

    def test_shared_string(self):
        """Test shared string references."""
        shared_string = "shared_data"
        container = [shared_string, shared_string, shared_string]

        size = calculateSize(container)

        # Expected: container overhead + one copy of the string
        expected = sys.getsizeof(container) + sys.getsizeof(shared_string)
        assert size == expected, f"Shared string: got {size}, expected {expected}"

    def test_shared_list(self):
        """Test shared list references."""
        shared_list = [1, 2, 3]
        container = [shared_list, shared_list]

        size = calculateSize(container)

        # Expected: container overhead + one copy of the shared list and its contents
        expected = (
            sys.getsizeof(container) + sys.getsizeof(shared_list) + sum(sys.getsizeof(item) for item in shared_list)
        )
        assert size == expected, f"Shared list: got {size}, expected {expected}"

    def test_shared_dict(self):
        """Test shared dictionary references."""
        shared_dict = {"key": "value", "number": 42}
        container = {"ref1": shared_dict, "ref2": shared_dict}

        size = calculateSize(container)

        # Expected: container overhead + keys + one copy of shared dict
        expected = (
            sys.getsizeof(container)
            + sys.getsizeof("ref1")
            + sys.getsizeof("ref2")
            + sys.getsizeof(shared_dict)
            + sys.getsizeof("key")
            + sys.getsizeof("value")
            + sys.getsizeof("number")
            + sys.getsizeof(42)
        )
        assert size == expected, f"Shared dict: got {size}, expected {expected}"


class TestCircularReferences:
    """Test handling of circular references."""

    def test_circular_list(self):
        """Test circular list reference."""
        circular_list = [1, 2, 3]
        circular_list.append(circular_list)

        # Should not raise RecursionError
        size = calculateSize(circular_list)

        # Expected: list overhead + items + no additional counting of self-reference
        expected = sys.getsizeof(circular_list) + sum(sys.getsizeof(item) for item in circular_list[:-1])
        assert size == expected

    def test_circular_dict(self):
        """Test circular dictionary reference."""
        circular_dict = {"a": 1, "b": 2}
        circular_dict["self"] = circular_dict

        # Should not raise RecursionError
        size = calculateSize(circular_dict)

        # Expected: dict overhead + keys and values (excluding self-reference)
        expected = (
            sys.getsizeof(circular_dict)
            + sys.getsizeof("a")
            + sys.getsizeof(1)
            + sys.getsizeof("b")
            + sys.getsizeof(2)
            + sys.getsizeof("self")
        )
        assert size == expected

    def test_mutual_references(self):
        """Test mutual references between objects."""
        obj1 = {"name": "obj1", "data": [1, 2, 3]}
        obj2 = {"name": "obj2", "data": [4, 5, 6]}
        obj1["ref"] = obj2
        obj2["ref"] = obj1

        # Should not raise RecursionError
        size1 = calculateSize(obj1)
        size2 = calculateSize(obj2)

        assert size1 > 0
        assert size2 > 0


@dataclass
class SampleDataClass:
    """Sample dataclass for size calculation tests."""

    name: str
    value: int
    data: List[str]
    metadata: Optional[Dict[str, Any]] = None


class TestDataclassObjects:
    """Test size calculation for dataclass objects."""

    def test_simple_dataclass(self):
        """Test simple dataclass object."""
        obj = SampleDataClass(name="test", value=42, data=["a", "b", "c"], metadata={"key": "value"})

        size = calculateSize(obj)

        # Expected: object overhead + all field values
        expected = (
            sys.getsizeof(obj)
            + sys.getsizeof("test")
            + sys.getsizeof(42)
            + sys.getsizeof(obj.data)
            + sum(sys.getsizeof(item) for item in obj.data)
            + sys.getsizeof(obj.metadata)
            + sys.getsizeof("key")
            + sys.getsizeof("value")
        )

        assert size == expected


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
