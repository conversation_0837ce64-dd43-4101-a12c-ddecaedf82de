"""
Tests for the optimized cache manager.
"""

from cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, LRUCache, SizeConstrained<PERSON>ache, TLR<PERSON><PERSON>


def test_create_cache_basic():
    """Test basic cache creation"""
    # Create a cache
    cache = CacheManager.createCache(name="test_cache", cacheType="lru", maxSize=100)

    # Check that it's the right type
    assert isinstance(cache, LRUCache)

    # Check that it's registered
    assert CacheManager.getCache("test_cache") is cache

    # Test basic operations
    cache.put("key1", "value1")
    assert cache.get("key1") == "value1"
    assert cache.contains("key1")

    # Test removal
    cache.remove("key1")
    assert not cache.contains("key1")

    # Clean up
    CacheManager.clearCache("test_cache")


def test_create_different_cache_types():
    """Test creating different types of caches"""
    # Create LRU cache
    lru_cache = CacheManager.createCache(name="test_lru", cacheType="lru", maxSize=100)
    assert isinstance(lru_cache, LRUCache)

    # Create TLRU cache (with minimal TTL for fast testing)
    tlru_cache = CacheManager.createCache(name="test_tlru", cacheType="tlru", maxSize=100, ttl=1)
    assert isinstance(tlru_cache, TLRUCache)

    # Create size-constrained cache (reduced size)
    size_cache = CacheManager.createCache(
        name="test_size",
        cacheType="sizeConstrained",
        maxSize=64 * 1024,  # 64KB instead of 1MB
    )
    assert isinstance(size_cache, SizeConstrainedCache)

    # Clean up
    CacheManager.clearAllCaches()


def test_memory_limit_enforcement():
    """Test that memory limits are enforced (minimal version)"""
    # Set a reasonable memory limit
    original_limit = CacheManager.getTotalMemoryLimit()
    CacheManager.setTotalMemoryLimit(16 * 1024)  # 16KB

    try:
        # Create a cache
        cache = CacheManager.createCache(
            name="test_limit",
            cacheType="sizeConstrained",
            maxSize=8 * 1024,  # 8KB
        )

        # Add minimal amount of data (reduced from 20 to 5)
        for i in range(5):
            # Each item is about 1KB
            cache.put(f"key_{i}", "x" * 1024)

            # Early break if approaching limit
            if CacheManager.getMemoryUsagePercentage() > 80:
                break

        # Check that cache has items
        initial_size = cache.size()
        assert initial_size > 0

        # Force memory limit enforcement
        CacheManager.enforceMemoryLimit()

        # Check that memory usage is under control
        final_percentage = CacheManager.getMemoryUsagePercentage()
        assert final_percentage <= 100

    finally:
        # Restore original limit and clean up
        CacheManager.setTotalMemoryLimit(original_limit)
        CacheManager.clearAllCaches()


def test_cache_stats():
    """Test cache statistics"""
    # Create a cache
    cache = CacheManager.createCache(name="test_stats", cacheType="lru", maxSize=100)

    # Add minimal data (reduced from 10 to 3)
    for i in range(3):
        cache.put(f"key_{i}", f"value_{i}")

    # Get some items (hits) - reduced from 5 to 2
    for i in range(2):
        cache.get(f"key_{i}")

    # Try to get non-existent items (misses) - reduced from 5 to 2
    for i in range(3, 5):
        cache.get(f"key_{i}")

    # Generate a report
    CacheManager.generateReport()

    # Clean up
    CacheManager.clearAllCaches()


def test_remove_cache():
    """Test removing a cache"""
    # Create a cache
    cache = CacheManager.createCache(name="test_remove", cacheType="lru", maxSize=100)

    # Check that it exists
    assert CacheManager.getCache("test_remove") is cache

    # Remove it
    assert CacheManager.removeCache("test_remove")

    # Check that it's gone
    assert CacheManager.getCache("test_remove") is None

    # Try to remove a non-existent cache
    assert not CacheManager.removeCache("non_existent_cache")


def test_configuration():
    """Test configuration system (simplified version)"""
    # Test basic configuration without monitor restart
    # Get current limit to restore later
    original_limit = CacheManager.getTotalMemoryLimit()

    try:
        # Test setting memory limit directly (avoid full reconfiguration)
        CacheManager.setTotalMemoryLimit(256 * 1024 * 1024)  # 256MB
        assert CacheManager.getTotalMemoryLimit() == 256 * 1024 * 1024

        # Test creating cache with specific type (use minimal TTL)
        cache = CacheManager.createCache(
            name="test_config",
            cacheType="tlru",
            maxSize=200,
            ttl=1.0,  # Minimal TTL instead of 1800.0
        )

        # Check that it uses the specified parameters
        assert isinstance(cache, TLRUCache)

    finally:
        # Restore original limit and clean up
        CacheManager.setTotalMemoryLimit(original_limit)
        CacheManager.clearAllCaches()
