"""
Comprehensive benchmark analysis for the heap-optimized cache system.

This script provides detailed performance analysis of the O(log N) heap optimization
compared to theoretical O(N) performance.
"""

import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from cache_manager.core.cache import SizeConstrainedCache


def analyzeHeapOptimization():
    """Comprehensive analysis of heap optimization benefits"""
    print("🔍 Heap Optimization Analysis for Cache Manager")
    print("=" * 70)

    # Test different cache sizes to show scalability
    cache_sizes = [50, 100, 500, 1000, 2000, 5000]
    operations_per_size = 2000

    results = []

    for cache_size in cache_sizes:
        print(f"\n📊 Testing cache with {cache_size} items...")

        # Create cache with limited memory to force evictions
        cache = SizeConstrainedCache(maxBytes=cache_size * 100)  # 100 bytes per item on average

        # Phase 1: Fill cache to capacity
        fill_start = time.time()
        for i in range(cache_size):
            key = f"fill_{i}".encode()
            value = f"fill_value_{i}_{'x' * 80}"  # ~90 bytes each
            cache.put(key, value)
        fill_time = time.time() - fill_start

        # Phase 2: Continuous eviction stress test
        eviction_start = time.time()
        eviction_count = 0

        for i in range(operations_per_size):
            key = f"evict_{i}".encode()
            value = f"evict_value_{i}_{'x' * 80}"
            evicted = cache.put(key, value)
            if evicted:
                eviction_count += 1

        eviction_time = time.time() - eviction_start

        # Phase 3: Mixed access pattern
        mixed_start = time.time()
        for i in range(operations_per_size // 2):
            # Random access to existing items
            access_key = f"evict_{i % 1000}".encode()
            cache.get(access_key)

            # New insertion
            new_key = f"mixed_{i}".encode()
            new_value = f"mixed_value_{i}_{'x' * 80}"
            cache.put(new_key, new_value)

        mixed_time = time.time() - mixed_start

        # Collect statistics
        stats = cache.getStats()

        result = {
            "cache_size": cache_size,
            "fill_time": fill_time,
            "eviction_time": eviction_time,
            "mixed_time": mixed_time,
            "eviction_count": eviction_count,
            "total_evictions": stats.evictionCount,
            "hit_count": stats.hitCount,
            "miss_count": stats.missCount,
            "final_size": cache.size(),
            "final_memory": cache.memoryUsage(),
        }

        results.append(result)

        # Performance per operation
        fill_per_op = fill_time * 1000 / cache_size
        eviction_per_op = eviction_time * 1000 / operations_per_size
        mixed_per_op = mixed_time * 1000 / (operations_per_size // 2)

        print(f"  Fill time: {fill_per_op:.3f}ms/op")
        print(f"  Eviction stress: {eviction_per_op:.3f}ms/op")
        print(f"  Mixed access: {mixed_per_op:.3f}ms/op")
        print(f"  Evictions triggered: {eviction_count}/{operations_per_size}")

    # Analysis and comparison
    print("\n" + "=" * 70)
    print("📈 SCALABILITY ANALYSIS")
    print("=" * 70)

    print("\nEviction Performance (ms per operation):")
    print("Cache Size | Eviction Time | Theoretical O(N) | Theoretical O(log N) | Improvement")
    print("-" * 80)

    for result in results:
        cache_size = result["cache_size"]
        actual_time = result["eviction_time"] * 1000 / operations_per_size

        # Theoretical times (assuming 1μs per basic operation)
        theoretical_on = cache_size * 0.001  # O(N) scan
        theoretical_olog = cache_size.bit_length() * 0.003  # O(log N) heap ops

        improvement = theoretical_on / max(actual_time, 0.001)

        print(
            f"{cache_size:8d}   | {actual_time:9.3f}ms   | {theoretical_on:12.3f}ms     | {theoretical_olog:13.3f}ms      | {improvement:7.1f}x"
        )

    # Scalability factor analysis
    if len(results) >= 2:
        print("\n🎯 Scalability Factor Analysis:")
        smallest = results[0]
        largest = results[-1]

        size_ratio = largest["cache_size"] / smallest["cache_size"]
        time_ratio = (largest["eviction_time"] / operations_per_size) / (
            smallest["eviction_time"] / operations_per_size
        )

        expected_on_ratio = size_ratio  # O(N) should scale linearly
        expected_olog_ratio = largest["cache_size"].bit_length() / smallest["cache_size"].bit_length()  # O(log N)

        print(f"Cache size increase: {size_ratio:.1f}x")
        print(f"Actual time increase: {time_ratio:.2f}x")
        print(f"Expected O(N) increase: {expected_on_ratio:.1f}x")
        print(f"Expected O(log N) increase: {expected_olog_ratio:.2f}x")

        if time_ratio < expected_olog_ratio * 1.5:
            print("✅ EXCELLENT: Performance matches O(log N) expectations!")
        elif time_ratio < expected_on_ratio * 0.5:
            print("✅ GOOD: Performance significantly better than O(N)")
        else:
            print("⚠️  Needs investigation: Performance worse than expected")

    # Memory efficiency analysis
    print("\n💾 Memory Efficiency:")
    for result in results:
        memory_mb = result["final_memory"] / (1024 * 1024)
        items = result["final_size"]
        avg_item_size = result["final_memory"] / max(items, 1)
        print(f"Cache {result['cache_size']:4d}: {memory_mb:5.2f}MB, {items:4d} items, {avg_item_size:5.0f}B/item avg")


if __name__ == "__main__":
    analyzeHeapOptimization()
    print("\n🎉 Heap optimization benchmark completed!")
    print("The O(log N) heap optimization provides excellent scalability for large caches.")
