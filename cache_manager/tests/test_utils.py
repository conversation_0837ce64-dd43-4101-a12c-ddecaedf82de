"""
Shared test utilities for cache_manager tests.

This module contains common test utilities to reduce code duplication across test files.
"""

import time
from typing import Any, Callable, Tuple


def measureTime(func: Callable, *args: Any, **kwargs: Any) -> Tuple[Any, float]:
    """Measure execution time of a function"""
    start = time.time()
    result = func(*args, **kwargs)
    end = time.time()
    return result, end - start


def createSimpleTestData(count: int) -> list:
    """Create simple test data for cache testing"""
    return [f"test_data_{i}" for i in range(count)]


def createComplexTestData(count: int) -> list:
    """Create complex test data structures"""
    return [
        {
            "id": i,
            "data": f"complex_data_{i}",
            "nested": {"value": i * 2, "text": f"nested_{i}"},
            "list": [j for j in range(i % 5)],
        }
        for i in range(count)
    ]
