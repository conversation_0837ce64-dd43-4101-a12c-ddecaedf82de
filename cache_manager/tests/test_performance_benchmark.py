"""
Simple performance test to measure cache_manager optimization effects.
"""

import sys
from pathlib import Path

import pytest

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheManagerConfig, TLRUCache
from cache_manager.core.size_calculator import calculateSize
from cache_manager.tests.test_utils import createComplexTestData


@pytest.mark.benchmark
def test_size_calculation_performance(benchmark):
    """Test the size calculation performance after optimization"""
    print("Testing size calculation performance...")

    # Create simple test objects instead of complex blockchain objects
    test_objects = createComplexTestData(100)

    def calculate_sizes():
        """Function to benchmark"""
        total_size = 0
        for obj in test_objects:
            size = calculateSize(obj)
            total_size += size
        return total_size

    # Use benchmark fixture to measure performance
    total_size = benchmark(calculate_sizes)

    print(f"Calculated size for {len(test_objects)} objects")
    print(f"Average size per object: {total_size // len(test_objects)} bytes")

    # Basic validation
    assert total_size > 0, "Size calculation should return positive value"


@pytest.mark.benchmark
def test_cache_creation_performance(benchmark):
    """Test cache creation and basic operation performance"""
    print("Testing cache creation performance...")

    def create_and_operate_caches():
        """Function to benchmark"""
        cache_count = 50

        # Create multiple caches
        caches = []
        for i in range(cache_count):
            cache = CacheManager.createCache(name=f"test_cache_{i}", cacheType="lru", maxSize=1000)
            caches.append(cache)

        # Test basic operations
        test_data = createComplexTestData(100)
        for cache in caches[:10]:  # Use first 10 caches
            for i, data in enumerate(test_data):
                cache.put(f"key_{i}", data)

        # Cleanup
        CacheManager.clearAllCaches()

        return len(caches)

    # Use benchmark fixture
    cache_count = benchmark(create_and_operate_caches)

    print(f"Created and operated on {cache_count} caches")
    assert cache_count > 0, "Should create caches successfully"


@pytest.mark.benchmark
def test_memory_calculation_performance(benchmark):
    """Test memory usage calculation performance"""
    print("Testing memory calculation performance...")

    def memory_operations():
        """Function to benchmark"""
        # Create cache with various data types
        cache = CacheManager.createCache(
            name="memory_test_cache",
            cacheType="sizeConstrained",
            maxSize=10 * 1024 * 1024,  # 10MB
        )

        # Add different types of data
        test_data = [
            "simple string",
            {"dict": "with", "multiple": "keys", "and": [1, 2, 3]},
            [i for i in range(100)],
            b"binary data" * 100,
            {"nested": {"deeply": {"nested": "structure", "with": [4, 5, 6]}}},
        ]

        for i, data in enumerate(test_data * 20):  # 100 total items
            cache.put(f"key_{i}", data)

        memory_usage = cache.memoryUsage()

        # Cleanup
        CacheManager.clearAllCaches()

        return memory_usage

    # Use benchmark fixture
    memory_usage = benchmark(memory_operations)

    print(f"Total memory usage: {memory_usage / 1024:.2f} KB")
    print(f"Average memory per item: {memory_usage // 100} bytes")

    # Verify memory usage
    assert memory_usage > 0, "Memory calculation should return positive value"


@pytest.mark.benchmark
def test_memory_limit_enforcement_benchmark(benchmark):
    """Benchmark test for memory limit enforcement (heavy test)"""
    print("Testing memory limit enforcement performance...")

    def memory_limit_operations():
        """Function to benchmark"""
        # Set a small memory limit
        original_limit = CacheManager.getTotalMemoryLimit()
        CacheManager.setTotalMemoryLimit(1024 * 1024)  # 1MB

        try:
            # Create a cache
            cache = CacheManager.createCache(
                name="benchmark_limit",
                cacheType="sizeConstrained",
                maxSize=512 * 1024,  # 512KB
            )

            # Add some data
            items_added = 0
            for i in range(100):
                # Each item is about 10KB
                cache.put(f"key_{i}", "x" * 10240)
                items_added += 1

                # Check if we're approaching the limit
                if CacheManager.getMemoryUsagePercentage() > 90:
                    break

            # Force memory limit enforcement
            CacheManager.enforceMemoryLimit()

            # Check that memory usage is below the limit
            final_percentage = CacheManager.getMemoryUsagePercentage()
            assert final_percentage < 100

            return items_added

        finally:
            # Restore original limit and clean up
            CacheManager.setTotalMemoryLimit(original_limit)
            CacheManager.clearAllCaches()

    # Use benchmark fixture
    items_added = benchmark(memory_limit_operations)

    print(f"Added {items_added} items (10KB each)")
    print(f"Final memory usage: {CacheManager.getMemoryUsagePercentage():.1f}%")

    assert items_added > 0, "Should add some items successfully"


@pytest.mark.benchmark
def test_configuration_benchmark(benchmark):
    """Benchmark test for configuration system (includes monitor restart)"""
    print("Testing configuration system with monitor restart...")

    def configuration_operations():
        """Function to benchmark"""
        # Create a custom configuration that will restart monitoring
        config = CacheManagerConfig(
            totalMemoryLimit=128 * 1024 * 1024,  # 128MB
            defaultCacheType="tlru",
            defaultMaxSize=200,
            defaultTtl=1800.0,  # 30 minutes
            monitorEnabled=False,  # Disable to avoid long delays
            monitorInterval=1.0,  # Short interval for testing
        )

        # Configure the cache manager (this may cause delays due to monitor restart)
        CacheManager.configure(config)

        # Create a cache using defaults
        cache = CacheManager.createCache(name="benchmark_config")

        # Check that it uses the configured defaults
        assert isinstance(cache, TLRUCache)
        assert CacheManager.getTotalMemoryLimit() == 128 * 1024 * 1024

        # Clean up
        CacheManager.clearAllCaches()

        return 1  # Success indicator

    # Use benchmark fixture
    result = benchmark(configuration_operations)

    print("Configuration test completed successfully")
    assert result == 1, "Configuration should complete successfully"


if __name__ == "__main__":
    print("=" * 60)
    print("Cache Manager Performance Tests")
    print("=" * 60)

    test_size_calculation_performance()
    print("-" * 60)

    test_cache_creation_performance()
    print("-" * 60)

    test_memory_calculation_performance()
    print("-" * 60)

    test_memory_limit_enforcement_benchmark()
    print("-" * 60)

    test_configuration_benchmark()
    print("-" * 60)

    print("All performance tests completed!")
