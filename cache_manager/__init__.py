"""Cache Manager Package

A simplified cache management system for vgraph that focuses on cache memory monitoring.

This package provides:
- Simple cache creation and management through CacheManager
- Various cache implementations (LRU, TLRU, SizeConstrainedCache)
- Automatic memory limit enforcement
- Memory usage monitoring and reporting
"""

# Direct import of CacheManager - the main interface
from cache_manager.cache_manager import CacheManager

# Configuration
from cache_manager.config import CacheManagerConfig, getConfig, setConfig, updateConfig

# Core cache types for type annotations
from cache_manager.core.cache import Abstract<PERSON>ache, LRUCache, SizeConstrainedCache, TLRUCache

__all__ = [
    # Main interface
    "CacheManager",
    # Cache types (for type annotations)
    "AbstractCache",
    "LRUCache",
    "TLRUCache",
    "SizeConstrainedCache",
    # Configuration
    "CacheManagerConfig",
    "getConfig",
    "setConfig",
    "updateConfig",
]
