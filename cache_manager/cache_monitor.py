"""
Simplified cache memory monitoring system.

This module provides a simplified memory monitoring system that focuses on
cache memory usage rather than system-wide memory monitoring.
"""

import json
import threading
import time
from typing import Dict, List, Optional

from kivy.logger import Logger

from cache_manager.core.registry import CacheRegistry


class CacheMemoryMonitor:
    """
    Simplified cache memory monitor.

    Monitors cache memory usage and enforces memory limits.
    """

    def __init__(
        self,
        registry: CacheRegistry,
        checkInterval: float = 10.0,
        warningThreshold: float = 0.8,  # 80% of limit
        criticalThreshold: float = 0.95,  # 95% of limit
        autoEnforce: bool = True,
    ):
        """
        Initialize cache memory monitor

        Args:
            registry: Cache registry to monitor
            checkInterval: Check interval in seconds
            warningThreshold: Threshold for warning (as fraction of limit)
            criticalThreshold: Threshold for critical warning (as fraction of limit)
            autoEnforce: Automatically enforce memory limits when exceeded
        """
        self.registry = registry
        self.checkInterval = checkInterval
        self.warningThreshold = warningThreshold
        self.criticalThreshold = criticalThreshold
        self.autoEnforce = autoEnforce

        self.running = False
        self.monitorThread: Optional[threading.Thread] = None
        self.lock = threading.RLock()

        # Memory usage history
        self.memoryHistory: List[Dict] = []
        self.maxHistorySize = 100  # Keep last 100 measurements

    def start(self) -> None:
        """Start monitoring"""
        with self.lock:
            if self.running:
                return

            self.running = True
            self.monitorThread = threading.Thread(target=self._monitorLoop, daemon=True)
            self.monitorThread.start()
            Logger.info("Memory Manager: 🚀 Cache memory monitor started")

    def stop(self) -> None:
        """Stop monitoring"""
        with self.lock:
            if not self.running:
                return

            self.running = False
            if self.monitorThread:
                self.monitorThread.join(timeout=self.checkInterval * 2)
                self.monitorThread = None
                Logger.info("Memory Manager: 🛑 Cache memory monitor stopped")

    def _monitorLoop(self) -> None:
        """Monitoring loop"""
        while self.running:
            try:
                # Get current memory usage
                totalUsage = self.registry.getTotalMemoryUsage()
                totalLimit = self.registry.totalMemoryLimit
                usagePercent = self.registry.getMemoryUsagePercentage()

                # Record memory usage
                timestamp = time.time()
                memoryInfo = {
                    "timestamp": timestamp,
                    "totalUsageBytes": totalUsage,
                    "totalUsageMB": totalUsage / (1024 * 1024),
                    "limitBytes": totalLimit,
                    "limitMB": totalLimit / (1024 * 1024),
                    "usagePercent": usagePercent,
                }

                with self.lock:
                    self.memoryHistory.append(memoryInfo)
                    # Keep history size limited
                    if len(self.memoryHistory) > self.maxHistorySize:
                        self.memoryHistory = self.memoryHistory[-self.maxHistorySize :]

                # Check thresholds and take action if needed
                if usagePercent >= self.criticalThreshold * 100:
                    Logger.warning(f"Memory Manager: 🚨 CRITICAL: Cache memory usage at {usagePercent:.1f}% of limit")
                    if self.autoEnforce:
                        self.registry.enforceMemoryLimit()
                elif usagePercent >= self.warningThreshold * 100:
                    Logger.warning(f"Memory Manager: ⚠️ WARNING: Cache memory usage at {usagePercent:.1f}% of limit")

            except Exception as e:
                Logger.error(f"Memory Manager: ❌ Error in cache memory monitor: {e}")

            # Wait for next check
            time.sleep(self.checkInterval)

    def getMemoryHistory(self) -> List[Dict]:
        """
        Get memory usage history

        Returns:
            List of memory usage records
        """
        with self.lock:
            return list(self.memoryHistory)

    def generateReport(self) -> str:
        """
        Generate a detailed memory usage report

        Returns:
            Formatted report string
        """
        report = self.registry.getMemoryReport()

        # Log header
        Logger.info("Memory Manager: === Cache Memory Report ===")

        # Log summary
        Logger.info(
            f"Memory Manager: Total Cache Memory: {report['totalMemoryMB']:.2f} MB / {report['limitMB']:.2f} MB ({report['usagePercentage']:.1f}%)"
        )
        Logger.info(f"Memory Manager: Number of Caches: {report['cacheCount']}")

        # Log details for each cache
        if report["caches"]:
            Logger.info("Memory Manager: === Cache Details ===")

            for cache in report["caches"]:
                Logger.info(f"Memory Manager: Cache: {cache['name']}")
                Logger.info(f"Memory Manager:   Size: {cache['items']} items")
                Logger.info(
                    f"Memory Manager:   Memory: {cache['memoryMB']:.2f} MB ({cache['percentage']:.1f}% of total)"
                )

                # Get hit ratio if available
                stats = cache["stats"]
                hitCount = stats.get("hitCount", 0)
                missCount = stats.get("missCount", 0)
                if hitCount + missCount > 0:
                    hitRatio = hitCount / (hitCount + missCount) * 100
                    Logger.info(f"Memory Manager:   Hit Ratio: {hitRatio:.1f}%")

                Logger.info(f"Memory Manager:   Evictions: {stats.get('evictionCount', 0)}")

        # Log footer
        Logger.info("Memory Manager: === End of Cache Memory Report ===")

        return json.dumps(report, indent=2)

    def enforceMemoryLimit(self) -> bool:
        """
        Manually enforce memory limit

        Returns:
            True if enforcement was needed, False otherwise
        """
        return self.registry.enforceMemoryLimit()
