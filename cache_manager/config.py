"""
Configuration management for cache_manager.

This module provides a simple configuration system for the cache manager.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class CacheManagerConfig:
    """Configuration for the cache manager system."""

    # Memory limits
    totalMemoryLimit: int = 8 * 1024 * 1024 * 1024  # 8GB default
    warningThreshold: float = 0.8  # 80%
    criticalThreshold: float = 0.95  # 95%

    # Memory management strategy parameters
    # - These control the behavior of memory cleanup and cache sizing
    # - Based on industry best practices from Redis, Memcached, and cloud providers
    memoryTargetRatio: float = 0.9  # Target memory usage after cleanup (90% of limit)
    cacheReductionRatio: float = 0.3  # Maximum cache size reduction per cleanup cycle (30%)

    # Monitoring settings
    monitorEnabled: bool = True
    monitorInterval: float = 10.0  # seconds
    autoEnforce: bool = True

    # Cache defaults
    defaultCacheType: str = "lru"
    defaultMaxSize: int = 1000
    defaultTtl: float = 3600.0  # 1 hour

    # Performance settings
    maxHistorySize: int = 100

    @classmethod
    def fromDict(cls, configDict: Dict[str, Any]) -> "CacheManagerConfig":
        """Create config from dictionary."""
        return cls(**{k: v for k, v in configDict.items() if hasattr(cls, k)})

    def toDict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "totalMemoryLimit": self.totalMemoryLimit,
            "warningThreshold": self.warningThreshold,
            "criticalThreshold": self.criticalThreshold,
            "memoryTargetRatio": self.memoryTargetRatio,
            "cacheReductionRatio": self.cacheReductionRatio,
            "monitorEnabled": self.monitorEnabled,
            "monitorInterval": self.monitorInterval,
            "autoEnforce": self.autoEnforce,
            "defaultCacheType": self.defaultCacheType,
            "defaultMaxSize": self.defaultMaxSize,
            "defaultTtl": self.defaultTtl,
            "maxHistorySize": self.maxHistorySize,
        }


# Global configuration instance
_config: Optional[CacheManagerConfig] = None


def getConfig() -> CacheManagerConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = CacheManagerConfig()
    return _config


def setConfig(config: CacheManagerConfig) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config


def updateConfig(**kwargs) -> None:
    """Update the global configuration with new values."""
    config = getConfig()
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown configuration key: {key}")
