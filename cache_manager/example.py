"""
Comprehensive usage example for the cache management system.

This example demonstrates:
1. Basic cache creation and usage
2. Different cache types (LRU, TLRU, Size-Constrained)
3. Memory monitoring and reporting
4. Best practices and configuration
5. Real-world usage patterns
"""

import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, List, Optional

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from cache_manager import CacheManager, CacheManagerConfig


def generate_random_data(size: int) -> bytes:
    """Generate random data of specified size efficiently"""
    import os

    return os.urandom(size)


@dataclass
class Transaction:
    """Sample transaction class for demonstration"""

    transaction_hash: Optional[str] = None
    from_address: str = ""
    contract_address: str = ""
    parameters: List[Any] = None
    publickeys: List[str] = None
    signatures: List[str] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = []
        if self.publickeys is None:
            self.publickeys = []
        if self.signatures is None:
            self.signatures = []


def create_sample_transaction() -> Transaction:
    """Create a sample transaction for testing"""
    return Transaction(
        transaction_hash="0x1234567890abcdef" * 4,
        from_address="0xabcdef1234567890" * 2 + "12345678",
        contract_address="0x9876543210fedcba" * 2 + "87654321",
        parameters=["param1", "param2", {"nested": "value"}],
        publickeys=["0xpubkey1" * 8, "0xpubkey2" * 8],
        signatures=["0xsig1" * 16, "0xsig2" * 16],
    )


def demonstrate_cache_types():
    """Demonstrate different cache types and their characteristics"""
    print("=== Cache Types Demonstration ===\n")

    transaction = create_sample_transaction()

    # Test different cache types with consistent configurations
    cache_configs = [
        ("lru", "LRU Cache - Least Recently Used", {"maxSize": 100}),
        ("tlru", "TLRU Cache - Time-aware LRU with TTL", {"maxSize": 100, "ttl": 60}),
        ("sizeConstrained", "Size-Constrained Cache - Memory-based eviction", {"maxSize": 1024 * 1024}),  # 1MB
    ]

    results = {}

    print(f"{'Cache Type':<16} | {'Size(bytes)':<12} | {'Description'}")
    print("-" * 70)

    for cache_type, description, config in cache_configs:
        try:
            # Create cache with specific configuration
            cache = CacheManager.createCache(f"demo_{cache_type}", cacheType=cache_type, **config)

            # Add sample data
            cache.put("transaction", transaction)
            cache.put("string_data", "Sample string data" * 10)
            cache.put("numeric_data", list(range(100)))

            # Get memory usage with error handling
            try:
                size = cache.memoryUsage()
            except Exception as e:
                print(f"Warning: Could not get memory usage for {cache_type}: {e}")
                size = 0

            results[cache_type] = size
            print(f"{cache_type:<16} | {size:<12} | {description}")

        except Exception as e:
            print(f"Error creating/testing cache {cache_type}: {e}")
            results[cache_type] = 0
        finally:
            # Clean up
            try:
                CacheManager.removeCache(f"demo_{cache_type}")
            except Exception:
                pass

    print("\nMemory usage varies by cache type and configuration.")
    return results


def demonstrate_memory_management():
    """Demonstrate memory management and monitoring features"""
    print("\n=== Memory Management Demonstration ===")

    # Configure the cache manager with smaller limits for demo
    config = CacheManagerConfig(
        totalMemoryLimit=100 * 1024 * 1024,  # 100MB
        warningThreshold=0.7,  # 70%
        criticalThreshold=0.9,  # 90%
        monitorInterval=5.0,  # 5 seconds
        defaultCacheType="lru",
        defaultMaxSize=500,
    )
    CacheManager.configure(config)

    print(f"Total memory limit: {CacheManager.getTotalMemoryLimit() / (1024 * 1024):.2f} MB")

    # Create different types of caches with consistent sizing
    lru_cache = CacheManager.createCache(name="memory_lru")
    size_cache = CacheManager.createCache(
        name="memory_size",
        cacheType="sizeConstrained",
        maxSize=5 * 1024 * 1024,  # 5MB - reasonable for testing
    )

    print("\n=== Adding data to demonstrate memory tracking ===")

    # Add to LRU cache
    for i in range(100):
        key = f"lru_key_{i}"
        value = f"lru_value_{i}" * 50
        lru_cache.put(key, value)

    # Add to size-constrained cache with reasonable, gradually increasing sizes
    print("Adding data to size-constrained cache...")
    for i in range(20):  # Reduced to 20 items with better size progression
        key = f"size_key_{i}"
        # More reasonable size progression: 5KB base + 2KB increment
        size = 5 * 1024 + (i * 2 * 1024)  # 5KB, 7KB, 9KB, ..., up to 43KB
        try:
            value = generate_random_data(size)
            success = size_cache.put(key, value)

            if success:
                print(f"✓ Cached item {i} (size: {size / 1024:.1f}KB)")
            else:
                print(f"✗ Could not cache item {i} (size: {size / 1024:.1f}KB) - cache full")

        except Exception as e:
            print(f"Error generating/caching item {i}: {e}")
            continue

        # Print progress every 5 items
        if (i + 1) % 5 == 0:
            try:
                current_usage = CacheManager.getTotalMemoryUsage()
                usage_percent = CacheManager.getMemoryUsagePercentage()
                cache_items = size_cache.size()
                print(f"  → Progress: {i + 1}/20 items processed, {cache_items} items cached")
                print(f"  → Memory usage: {current_usage / (1024 * 1024):.2f} MB ({usage_percent:.1f}%)")
            except Exception as e:
                print(f"Error getting memory stats: {e}")

    # Generate a memory report
    print("\n=== Memory Report ===")
    CacheManager.generateReport()

    return lru_cache, size_cache


def demonstrate_memory_limit_enforcement():
    """Demonstrate memory limit enforcement"""
    print("\n=== Testing Memory Limit Enforcement ===")

    try:
        # Get the size cache from previous demo
        size_cache = CacheManager.getCache("memory_size")
        if not size_cache:
            print("Creating new size-constrained cache for memory limit testing...")
            size_cache = CacheManager.createCache(
                name="memory_size",
                cacheType="sizeConstrained",
                maxSize=5 * 1024 * 1024,  # 5MB for consistent testing
            )
    except Exception as e:
        print(f"Error setting up cache for memory limit testing: {e}")
        return

    # Add moderately large objects to demonstrate cache eviction behavior
    print("Adding larger objects to demonstrate cache eviction...")
    for i in range(6):  # 6 objects of 1MB each for 5MB cache = clear eviction demo
        key = f"large_key_{i}"
        # 1MB objects - perfect size to show eviction in 5MB cache
        object_size = 1024 * 1024  # 1MB each

        try:
            print(f"\n📦 Adding {object_size / (1024 * 1024):.0f}MB object {i + 1}/6 (key: {key})...")
            value = generate_random_data(object_size)
            success = size_cache.put(key, value)

            if success:
                print(f"✓ Successfully cached object {i + 1}")
            else:
                print(f"✗ Object {i + 1} was rejected (too large for cache)")

            # Show current cache state
            try:
                cache_items = size_cache.size()
                cache_memory = size_cache.memoryUsage()
                total_usage = CacheManager.getTotalMemoryUsage()
                usage_percent = CacheManager.getMemoryUsagePercentage()

                print(f"  → Cache state: {cache_items} items, {cache_memory / (1024 * 1024):.1f}MB")
                print(f"  → Total memory: {total_usage / (1024 * 1024):.1f}MB ({usage_percent:.1f}%)")

                # Show which keys are currently in cache
                current_keys = size_cache.getAllKeys()
                print(f"  → Current keys: {', '.join(current_keys)}")

            except Exception as e:
                print(f"Error getting cache stats: {e}")

        except Exception as e:
            print(f"Error creating/caching large object {i + 1}: {e}")
            continue

    print("\n=== Final Memory Report ===")
    try:
        CacheManager.generateReport()
    except Exception as e:
        print(f"Error generating final memory report: {e}")


def demonstrate_best_practices():
    """Demonstrate best practices"""
    print("\n=== Best Practices ===\n")

    print("🎯 Recommended usage:")
    print()

    print("1. Choose appropriate cache type for your use case")
    print("   # LRU for general purpose caching")
    print("   cache = CacheManager.createCache('my_cache', cacheType='lru', maxSize=1000)")
    print()

    print("2. Use TLRU for time-sensitive data")
    print("   # TLRU with TTL for data that expires")
    print("   cache = CacheManager.createCache('session_cache', cacheType='tlru', maxSize=500, ttl=3600)")
    print()

    print("3. Use size-constrained cache for memory control")
    print("   # Size-constrained for large objects")
    print("   cache = CacheManager.createCache('file_cache', cacheType='sizeConstrained', maxSize=64*1024*1024)")
    print()

    print("4. Monitor memory usage")
    print("   # Check total memory usage")
    print("   print(f'Memory usage: {CacheManager.getTotalMemoryUsage() / (1024*1024):.2f} MB')")
    print("   CacheManager.generateReport()  # Generate detailed report")
    print()

    print("5. Configure appropriate limits")
    print("   config = CacheManagerConfig(")
    print("       totalMemoryLimit=1024*1024*1024,  # 1GB")
    print("       warningThreshold=0.8,")
    print("       criticalThreshold=0.95")
    print("   )")
    print("   CacheManager.configure(config)")


def main():
    """Main demonstration function"""
    print("🔗 Comprehensive Cache Manager Demo\n")

    try:
        # Demonstrate different cache types
        print("Starting cache types demonstration...")
        demonstrate_cache_types()

        # Demonstrate memory management
        print("\nStarting memory management demonstration...")
        demonstrate_memory_management()

        # Demonstrate memory limit enforcement
        print("\nStarting memory limit enforcement demonstration...")
        demonstrate_memory_limit_enforcement()

        # Show best practices
        print("\nShowing best practices...")
        demonstrate_best_practices()

        print("\n" + "=" * 60)
        print("✅ Demo complete!")
        print("\n💡 Summary:")
        print("   • Multiple cache types: LRU, TLRU, Size-Constrained")
        print("   • Automatic memory management and monitoring")
        print("   • Memory limit enforcement with eviction")
        print("   • Comprehensive reporting and statistics")
        print("   • Simple API for cache creation and management")

    except Exception as e:
        print(f"Error during demo execution: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Clean up
        print("\n=== Cleaning Up ===")
        try:
            CacheManager.clearAllCaches()
            current_usage = CacheManager.getTotalMemoryUsage()
            print("After clearing all caches: {:.2f} MB".format(current_usage / (1024 * 1024)))
            CacheManager.shutdown()
            print("Memory management system shutdown complete")
        except Exception as e:
            print(f"Error during cleanup: {e}")


if __name__ == "__main__":
    main()
