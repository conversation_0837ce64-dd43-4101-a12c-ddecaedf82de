"""
Simplified cache management system.

This module provides a simple interface for creating and managing caches with automatic
memory usage control. It is the main entry point for the cache_manager package.

Example usage:
    ```python
    from cache_manager import CacheManager

    # Create a cache
    cache = CacheManager.createCache(name="my_cache", cacheType="lru", maxSize=1000)

    # Use the cache
    cache.put("key", "value")
    value = cache.get("key")

    # Get memory usage information
    totalUsage = CacheManager.getTotalMemoryUsage()
    percentUsed = CacheManager.getMemoryUsagePercentage()

    # Generate a memory report
    CacheManager.generateReport()
    ```
"""

import threading
from typing import Any, Callable, Optional, TypeVar

from kivy.logger import Logger

from cache_manager.cache_monitor import CacheMemoryMonitor
from cache_manager.config import CacheManagerConfig, getConfig, setConfig
from cache_manager.core.cache import AbstractCache, LRUCache, SizeConstrainedCache, TLRUCache
from cache_manager.core.registry import CacheRegistry
from cache_manager.core.size_calculator import calculateSize

K = TypeVar("K")  # Key type
V = TypeVar("V")  # Value type


class CacheManager:
    """
    Simplified cache manager for creating and managing caches.

    This class provides a singleton-like interface for managing caches with
    configurable settings and automatic monitoring.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        config = getConfig()
        self.registry = CacheRegistry(totalMemoryLimit=config.totalMemoryLimit)
        self.monitor = None

        if config.monitorEnabled:
            self.monitor = CacheMemoryMonitor(
                self.registry,
                checkInterval=config.monitorInterval,
                warningThreshold=config.warningThreshold,
                criticalThreshold=config.criticalThreshold,
                autoEnforce=config.autoEnforce,
            )
            self.monitor.start()

        self._initialized = True

    @classmethod
    def getInstance(cls) -> "CacheManager":
        """Get the singleton instance."""
        return cls()

    @classmethod
    def configure(cls, config: CacheManagerConfig) -> None:
        """Configure the cache manager with new settings."""
        setConfig(config)

        # Reset instance to apply new configuration
        with cls._lock:
            if cls._instance is not None:
                if cls._instance.monitor:
                    cls._instance.monitor.stop()
                cls._instance = None

    @classmethod
    def createCache(
        cls,
        name: str,
        cacheType: Optional[str] = None,
        maxSize: Optional[int] = None,
        ttl: Optional[float] = None,
        getSizeOf: Optional[Callable[[Any], int]] = None,
    ) -> AbstractCache:
        """
        Create a new cache with the specified parameters

        Args:
            name: Unique name for the cache
            cacheType: Type of cache ("lru", "tlru", or "sizeConstrained")
            maxSize: Maximum size of the cache
            ttl: Time-to-live in seconds (only for TLRU cache)
            getSizeOf: Function to calculate size of values

        Returns:
            The created cache instance
        """
        instance = cls.getInstance()
        config = getConfig()

        # Use config defaults if not specified
        if cacheType is None:
            cacheType = config.defaultCacheType
        if maxSize is None:
            maxSize = config.defaultMaxSize
        if ttl is None and cacheType == "tlru":
            ttl = config.defaultTtl

        # Check if cache already exists
        existingCache = instance.registry.getCache(name)
        if existingCache:
            Logger.warning(f"Cache Manager: ⚠️ Cache '{name}' already exists, returning existing instance")
            return existingCache

        # Check available memory
        totalLimit = instance.registry.totalMemoryLimit
        currentUsage = instance.registry.getTotalMemoryUsage()
        availableMemory = totalLimit - currentUsage

        # Adjust size if needed for size-constrained cache
        # Use warningThreshold from config to prevent single cache from consuming all available memory
        # Reserves portion of available memory for other operations and system safety margin
        config = getConfig()
        if cacheType == "sizeConstrained" and maxSize > availableMemory * config.warningThreshold:
            oldMaxSize = maxSize
            maxSize = int(availableMemory * config.warningThreshold)
            Logger.warning(
                f"Memory Manager: ⚠️ Adjusted cache '{name}' size from {oldMaxSize / (1024 * 1024):.2f} MB to {maxSize / (1024 * 1024):.2f} MB due to memory limit"
            )

        # Create cache based on type
        sizeFunc = getSizeOf or calculateSize

        if cacheType == "tlru":
            cache = TLRUCache(maxSize, ttl=ttl, getSizeOf=sizeFunc)
            Logger.debug(f"Memory Manager: Created TLRU cache '{name}' with {maxSize} items limit and {ttl}s TTL")
        elif cacheType == "sizeConstrained":
            cache = SizeConstrainedCache(maxSize, getSizeOf=sizeFunc)
            Logger.debug(
                f"Memory Manager: Created size-constrained cache '{name}' with {maxSize / (1024 * 1024):.2f} MB limit"
            )
        else:  # Default to LRU
            cache = LRUCache(maxSize, getSizeOf=sizeFunc)
            Logger.debug(f"Memory Manager: Created LRU cache '{name}' with {maxSize} items limit")

        # Register cache
        instance.registry.register(name, cache)

        return cache

    @classmethod
    def getCache(cls, name: str) -> Optional[AbstractCache]:
        """
        Get a cache by name

        Args:
            name: Name of the cache

        Returns:
            The cache instance or None if not found
        """
        return cls.getInstance().registry.getCache(name)

    @classmethod
    def removeCache(cls, name: str) -> bool:
        """
        Remove a cache by name

        Args:
            name: Name of the cache

        Returns:
            True if the cache was removed, False otherwise
        """
        instance = cls.getInstance()
        cache = instance.registry.getCache(name)
        if cache:
            instance.registry.unregister(name)
            return True
        return False

    @classmethod
    def clearCache(cls, name: str) -> bool:
        """
        Clear a cache by name

        Args:
            name: Name of the cache

        Returns:
            True if the cache was cleared, False if not found
        """
        instance = cls.getInstance()
        cache = instance.registry.getCache(name)
        if cache:
            cache.clear()
            Logger.info(f"Memory Manager: 🧹 Cleared cache '{name}'")
            return True
        return False

    @classmethod
    def clearAllCaches(cls) -> None:
        """Clear all caches"""
        instance = cls.getInstance()
        instance.registry.clearAll()
        Logger.info("Memory Manager: 🧹 Cleared all caches")

    @classmethod
    def getTotalMemoryUsage(cls) -> int:
        """
        Get total memory usage of all caches

        Returns:
            Total memory usage in bytes
        """
        return cls.getInstance().registry.getTotalMemoryUsage()

    @classmethod
    def getTotalMemoryLimit(cls) -> int:
        """
        Get total memory limit

        Returns:
            Total memory limit in bytes
        """
        return cls.getInstance().registry.totalMemoryLimit

    @classmethod
    def setTotalMemoryLimit(cls, newLimit: int) -> None:
        """
        Set a new total memory limit

        Args:
            newLimit: New memory limit in bytes
        """
        cls.getInstance().registry.setTotalMemoryLimit(newLimit)

    @classmethod
    def getMemoryUsagePercentage(cls) -> float:
        """
        Get memory usage as a percentage of the total limit

        Returns:
            Percentage of memory limit used (0-100)
        """
        return cls.getInstance().registry.getMemoryUsagePercentage()

    @classmethod
    def generateReport(cls) -> str:
        """
        Generate a detailed memory usage report

        Returns:
            Formatted report string
        """
        instance = cls.getInstance()
        if instance.monitor:
            return instance.monitor.generateReport()
        else:
            Logger.warning("Memory Manager: ⚠️ Monitor not enabled, cannot generate report")
            return ""

    @classmethod
    def enforceMemoryLimit(cls) -> bool:
        """
        Manually enforce memory limit

        Returns:
            True if enforcement was needed, False otherwise
        """
        return cls.getInstance().registry.enforceMemoryLimit()

    @classmethod
    def shutdown(cls) -> None:
        """Shutdown the cache system and release resources"""
        instance = cls.getInstance()
        if instance.monitor:
            instance.monitor.stop()
        instance.registry.clearAll()
        Logger.info("Memory Manager: 🛑 Cache system shutdown complete")
