"""
Size calculator for memory management.

Simplified size calculation with recursion depth limit for VGraph blockchain objects.
"""

import sys
from functools import lru_cache
from typing import Any, Callable, Dict, Optional, Set, Type

# Size calculation constants for memory management optimization

# Maximum recursion depth to prevent stack overflow
# - Limits recursive size calculation to prevent infinite loops
# - When exceeded, falls back to conservative size estimation
# - Value of 15 provides balance between accuracy and safety
MAX_RECURSION_DEPTH = 15

# Size calculators registry for custom types
# - Allows registration of specialized size calculators for specific object types
# - Enables accurate memory measurement for blockchain-specific data structures
_sizeCalculators: Dict[Type, Callable[[Any, Optional[Set]], int]] = {}

# LRU cache configuration for size calculation optimization
# - Caches size calculations for frequently accessed immutable objects
# - Reduces repeated sys.getsizeof() calls for better performance
# - Size of 1000 entries provides good hit rate without excessive memory overhead
_SIZE_CACHE_SIZE = 1000

# Small object caching threshold
# - Only cache size calculations for objects smaller than 1KB
# - Larger objects are calculated directly to avoid cache pollution
# - Only applies to ASCII strings and bytes objects for accuracy
# - 1KB threshold balances caching benefits vs memory usage
_SMALL_OBJECT_CACHE_THRESHOLD = 1000


@lru_cache(maxsize=_SIZE_CACHE_SIZE)
def _cachedBasicSize(objTypeName: str, objHash: int, objLen: int = -1) -> int:
    """
    Cache size calculations for basic immutable objects.
    """
    if objTypeName == "str":
        return sys.getsizeof("") + objLen
    elif objTypeName == "bytes":
        return sys.getsizeof(b"") + objLen
    elif objTypeName in ["int", "float", "bool"]:
        return 28 if objTypeName == "int" else 24
    else:
        return -1  # Disable caching for unknown types


def registerSizeCalculator(typeToRegister: Type, calculator: Callable[[Any, Optional[Set]], int]) -> None:
    """Register a size calculator for a specific type."""
    _sizeCalculators[typeToRegister] = calculator


def conservativeEstimate(obj: Any) -> int:
    """
    Conservative size estimation for objects that exceed recursion depth.
    """
    baseSize = sys.getsizeof(obj)

    if isinstance(obj, (list, tuple)):
        return baseSize + len(obj) * 100  # Assume 100 bytes per item
    elif isinstance(obj, dict):
        return baseSize + len(obj) * 200  # Assume 200 bytes per key-value pair
    elif hasattr(obj, "__dataclass_fields__"):
        return baseSize * 3  # Conservative 3x multiplier for dataclass objects
    else:
        return baseSize * 2  # Conservative 2x multiplier for other objects


def calculateSize(obj: Any, visited: Optional[Set] = None, depth: int = 0) -> int:
    """
    Calculate the size of an object with recursion depth limit.

    Simplified algorithm that combines accuracy with performance for VGraph blockchain objects.

    Args:
        obj: The object to calculate the size of
        visited: Set of object IDs already visited (for cycle detection)
        depth: Current recursion depth

    Returns:
        Size in bytes
    """
    # Initialize visited set on first call
    if visited is None:
        visited = set()

    # Recursion depth limit - use conservative estimation
    if depth > MAX_RECURSION_DEPTH:
        return conservativeEstimate(obj)

    # Handle None
    if obj is None:
        return sys.getsizeof(None)

    # Check for circular references using object ID
    objId = id(obj)
    if objId in visited:
        return 0  # Already counted, avoid double counting

    # Add current object to visited set
    visited.add(objId)

    # Get object type
    objType = type(obj)

    # Check if we have a registered calculator for this type
    if objType in _sizeCalculators:
        return _sizeCalculators[objType](obj, visited)

    # Handle basic immutable types
    if isinstance(obj, (int, float, bool, str, bytes, bytearray, type(None))):
        # Simple caching for small ASCII strings and bytes only
        if isinstance(obj, str) and len(obj) < _SMALL_OBJECT_CACHE_THRESHOLD and obj.isascii():
            try:
                cachedSize = _cachedBasicSize(type(obj).__name__, hash(obj), len(obj))
                if cachedSize > 0:
                    return cachedSize
            except (TypeError, ValueError):
                pass  # Fall back to direct calculation
        elif isinstance(obj, bytes) and len(obj) < _SMALL_OBJECT_CACHE_THRESHOLD:
            try:
                cachedSize = _cachedBasicSize(type(obj).__name__, hash(obj), len(obj))
                if cachedSize > 0:
                    return cachedSize
            except (TypeError, ValueError):
                pass  # Fall back to direct calculation

        return sys.getsizeof(obj)

    # Start with the object's own size
    size = sys.getsizeof(obj)

    # Handle different container types
    if isinstance(obj, dict):
        for k, v in obj.items():
            size += calculateSize(k, visited, depth + 1)
            size += calculateSize(v, visited, depth + 1)
    elif isinstance(obj, (list, tuple, set, frozenset)):
        for item in obj:
            size += calculateSize(item, visited, depth + 1)
    elif hasattr(obj, "__dataclass_fields__"):
        # Handle dataclass objects efficiently
        for fieldName in obj.__dataclass_fields__:
            try:
                fieldValue = getattr(obj, fieldName)
                size += calculateSize(fieldValue, visited, depth + 1)
            except AttributeError:
                pass
    elif hasattr(obj, "__dict__"):
        # Handle custom objects by examining their attributes
        for _attrName, attrValue in obj.__dict__.items():
            if not callable(attrValue):
                size += calculateSize(attrValue, visited, depth + 1)
    elif hasattr(obj, "__slots__"):
        # Handle objects with __slots__
        for slot in obj.__slots__:
            try:
                slotValue = getattr(obj, slot)
                if not callable(slotValue):
                    size += calculateSize(slotValue, visited, depth + 1)
            except AttributeError:
                pass

    return size
