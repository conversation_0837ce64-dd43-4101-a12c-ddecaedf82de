"""
Cache implementations for the memory management system.
"""

import sys
import threading
import time
from abc import ABC, abstractmethod
from collections import OrderedDict
from typing import Callable, Dict, Generic, List, Optional, Tuple, TypeVar

# Type variables for generic cache implementations
K = TypeVar("K")  # Key type
V = TypeVar("V")  # Value type


class CacheStats:
    """Cache statistics for monitoring and analysis"""

    def __init__(self):
        self.hitCount = 0
        self.missCount = 0
        self.evictionCount = 0
        self.expirationCount = 0
        self.totalLoadTime = 0.0
        self.lastAccessTime = 0.0
        self.lastEvictionTime = 0.0

    def __repr__(self) -> str:
        return (
            f"CacheStats(hitCount={self.hitCount}, missCount={self.missCount}, "
            f"evictionCount={self.evictionCount}, expirationCount={self.expirationCount})"
        )


class AbstractCache(Generic[K, V], ABC):
    """Abstract base class for all cache implementations"""

    @abstractmethod
    def get(self, key: K) -> Optional[V]:
        """
        Retrieve a cached item by key

        Args:
            key: The cache key

        Returns:
            The cached value or None if not found
        """
        pass

    @abstractmethod
    def put(self, key: K, value: V) -> bool:
        """
        Add an item to the cache

        Args:
            key: The cache key
            value: The value to cache

        Returns:
            True if an eviction occurred, False otherwise
        """
        pass

    @abstractmethod
    def calculateSize(self, value: V) -> int:
        """
        Calculate the size of a value

        Args:
            value: The value to get the size of

        Returns:
            Size in bytes
        """
        pass

    @abstractmethod
    def remove(self, key: K) -> bool:
        """
        Remove an item from the cache

        Args:
            key: The cache key

        Returns:
            True if the key was found and removed, False otherwise
        """
        pass

    @abstractmethod
    def clear(self) -> None:
        """Clear all items from the cache"""
        pass

    @abstractmethod
    def contains(self, key: K) -> bool:
        """
        Check if a key exists in the cache

        Args:
            key: The cache key

        Returns:
            True if the key exists, False otherwise
        """
        pass

    @abstractmethod
    def size(self) -> int:
        """
        Get the number of items in the cache

        Returns:
            The number of items in the cache
        """
        pass

    @abstractmethod
    def memoryUsage(self) -> int:
        """
        Get the estimated memory usage of the cache in bytes

        Returns:
            The estimated memory usage in bytes
        """
        pass

    @abstractmethod
    def getStats(self) -> CacheStats:
        """
        Get cache statistics

        Returns:
            Cache statistics object
        """
        pass

    @abstractmethod
    def resize(self, newSize: int) -> None:
        """
        Resize the cache

        Args:
            newSize: The new maximum size of the cache
        """
        pass

    @abstractmethod
    def getAllKeys(self) -> List[K]:
        """
        Get all keys in the cache

        Returns:
            List of all keys in the cache
        """
        pass


class LRUCache(AbstractCache[K, V]):
    """
    Least Recently Used (LRU) cache implementation.

    This cache evicts the least recently used items when it reaches capacity.
    """

    def __init__(self, maxSize: int, getSizeOf: Callable[[V], int] = sys.getsizeof):
        """
        Initialize an LRU cache

        Args:
            maxSize: Maximum cache size (if getSizeOf returns bytes, this is in bytes; otherwise item count)
            getSizeOf: Function to calculate the size of a value, defaults to sys.getsizeof
        """
        self.maxSize = maxSize
        self.getSizeOf = getSizeOf
        self.cache: OrderedDict[K, V] = OrderedDict()
        self.sizeMap: Dict[K, int] = {}  # Store the size of each value
        self.currentSize = 0
        self.stats = CacheStats()
        self.lock = threading.RLock()

    def calculateSize(self, value: V) -> int:
        """Calculate the size of a value using the provided getSizeOf function"""
        return self.getSizeOf(value)

    def get(self, key: K) -> Optional[V]:
        with self.lock:
            self.stats.lastAccessTime = time.time()
            if key in self.cache:
                # Move to the end (most recently used) - Optimized
                # Use move_to_end instead of pop+insert for better performance
                self.cache.move_to_end(key)
                self.stats.hitCount += 1
                return self.cache[key]
            self.stats.missCount += 1
            return None

    def put(self, key: K, value: V) -> bool:
        with self.lock:
            self.stats.lastAccessTime = time.time()

            # If key already exists, remove it first
            if key in self.cache:
                self.remove(key)

            # Calculate size of new value
            valueSize = self.calculateSize(value)

            # If single value exceeds max size, don't cache
            if valueSize > self.maxSize:
                return False

            # Evict old items until there's enough space
            evictionOccurred = False
            while self.currentSize + valueSize > self.maxSize and self.cache:
                self._evictOldest()
                evictionOccurred = True

            # Add new item
            self.cache[key] = value
            self.sizeMap[key] = valueSize
            self.currentSize += valueSize

            return evictionOccurred

    def _evictOldest(self) -> None:
        """Evict the oldest (least recently used) item"""
        if not self.cache:
            return

        oldestKey, oldestValue = next(iter(self.cache.items()))
        self.remove(oldestKey)
        self.stats.evictionCount += 1
        self.stats.lastEvictionTime = time.time()

    def remove(self, key: K) -> bool:
        with self.lock:
            if key in self.cache:
                valueSize = self.sizeMap.pop(key)
                self.cache.pop(key)
                self.currentSize -= valueSize
                return True
            return False

    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            self.sizeMap.clear()
            self.currentSize = 0

    def contains(self, key: K) -> bool:
        with self.lock:
            return key in self.cache

    def size(self) -> int:
        with self.lock:
            return len(self.cache)

    def memoryUsage(self) -> int:
        with self.lock:
            return self.currentSize

    def getStats(self) -> CacheStats:
        with self.lock:
            return self.stats

    def resize(self, newSize: int) -> None:
        with self.lock:
            if newSize < self.maxSize:
                # If shrinking, may need to evict some items
                while self.currentSize > newSize and self.cache:
                    self._evictOldest()

            self.maxSize = newSize

    def getAllKeys(self) -> List[K]:
        with self.lock:
            return list(self.cache.keys())


class TLRUCache(LRUCache[K, V]):
    """
    Time-aware Least Recently Used (TLRU) cache implementation.

    Combines features of LRU and TTL caches. Items are evicted when they expire
    or when the cache is full (using LRU policy).
    """

    def __init__(
        self,
        maxSize: int,
        ttl: float,
        getSizeOf: Callable[[V], int] = sys.getsizeof,
        timer: Callable[[], float] = time.time,
    ):
        """
        Initialize a TLRU cache

        Args:
            maxSize: Maximum cache size
            ttl: Time-to-live in seconds
            getSizeOf: Function to calculate the size of a value
            timer: Function to get current time
        """
        super().__init__(maxSize, getSizeOf)
        self.ttl = ttl
        self.timer = timer
        self.expiryTimes: Dict[K, float] = {}  # Store expiration time for each key

    # Inherit calculateSize from parent class

    def get(self, key: K) -> Optional[V]:
        with self.lock:
            # Check if expired
            if key in self.expiryTimes and self.timer() > self.expiryTimes[key]:
                # Expired, remove and return None
                self.remove(key)
                self.stats.expirationCount += 1
                self.stats.missCount += 1
                return None

            return super().get(key)

    def put(self, key: K, value: V) -> bool:
        with self.lock:
            eviction = super().put(key, value)

            # Set expiration time
            if key in self.cache:
                self.expiryTimes[key] = self.timer() + self.ttl

            return eviction

    def remove(self, key: K) -> bool:
        with self.lock:
            if super().remove(key):
                if key in self.expiryTimes:
                    del self.expiryTimes[key]
                return True
            return False

    def clear(self) -> None:
        with self.lock:
            super().clear()
            self.expiryTimes.clear()

    def expire(self, currentTime: Optional[float] = None) -> List[Tuple[K, V]]:
        """
        Remove all expired items

        Args:
            currentTime: Current time, if None uses timer()

        Returns:
            List of removed (key, value) pairs
        """
        with self.lock:
            if currentTime is None:
                currentTime = self.timer()

            expiredItems = []
            # Find all expired keys
            expiredKeys = [k for k, expTime in self.expiryTimes.items() if expTime <= currentTime]

            # Remove expired items
            for key in expiredKeys:
                if key in self.cache:
                    value = self.cache[key]
                    self.remove(key)
                    expiredItems.append((key, value))
                    self.stats.expirationCount += 1

            return expiredItems


class SizeConstrainedCache(AbstractCache[K, V]):
    """
    Size-constrained cache implementation with OrderedDict-based LRU tracking.

    Cache size limit is based on actual item sizes rather than count.
    Uses OrderedDict for efficient O(1) LRU operations.
    """

    def __init__(self, maxBytes: int, getSizeOf: Callable[[V], int] = sys.getsizeof):
        """
        Initialize a size-constrained cache

        Args:
            maxBytes: Maximum cache size in bytes
            getSizeOf: Function to calculate the size of a value
        """
        self.maxBytes = maxBytes
        self.getSizeOf = getSizeOf
        self.cache: OrderedDict[K, V] = OrderedDict()  # LRU cache with O(1) operations
        self.sizeMap: Dict[K, int] = {}  # Store the size of each value
        self.currentBytes = 0
        self.stats = CacheStats()
        self.lock = threading.RLock()

    def calculateSize(self, value: V) -> int:
        """Calculate the size of a value using the provided getSizeOf function"""
        return self.getSizeOf(value)

    def get(self, key: K) -> Optional[V]:
        with self.lock:
            self.stats.lastAccessTime = time.time()
            if key in self.cache:
                # Move to end (most recently used) - O(1) operation
                self.cache.move_to_end(key)
                self.stats.hitCount += 1
                return self.cache[key]
            self.stats.missCount += 1
            return None

    def put(self, key: K, value: V) -> bool:
        with self.lock:
            self.stats.lastAccessTime = time.time()

            # If key already exists, remove it first
            if key in self.cache:
                self.remove(key)

            # Calculate size of new value
            valueSize = self.calculateSize(value)

            # If single value exceeds max size, don't cache
            if valueSize > self.maxBytes:
                return False

            # Evict old items until there's enough space
            evictionOccurred = False
            while self.currentBytes + valueSize > self.maxBytes and self.cache:
                self._evictOldest()
                evictionOccurred = True

            # Add new item (OrderedDict automatically maintains order)
            self.cache[key] = value
            self.sizeMap[key] = valueSize
            self.currentBytes += valueSize

            return evictionOccurred

    def _evictOldest(self) -> None:
        """Evict the oldest (least recently used) item - O(1) operation"""
        if not self.cache:
            return

        # Remove the first item (oldest) - O(1) operation
        oldestKey, _ = self.cache.popitem(last=False)
        if oldestKey in self.sizeMap:
            valueSize = self.sizeMap.pop(oldestKey)
            self.currentBytes -= valueSize

        self.stats.evictionCount += 1
        self.stats.lastEvictionTime = time.time()

    def remove(self, key: K) -> bool:
        with self.lock:
            if key in self.cache:
                # Remove from cache and size tracking
                del self.cache[key]
                if key in self.sizeMap:
                    valueSize = self.sizeMap.pop(key)
                    self.currentBytes -= valueSize
                return True
            return False

    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            self.sizeMap.clear()
            self.currentBytes = 0

    def contains(self, key: K) -> bool:
        with self.lock:
            return key in self.cache

    def size(self) -> int:
        with self.lock:
            return len(self.cache)

    def memoryUsage(self) -> int:
        with self.lock:
            return self.currentBytes

    def getStats(self) -> CacheStats:
        with self.lock:
            return self.stats

    def resize(self, newSize: int) -> None:
        with self.lock:
            if newSize < self.maxBytes:
                # If shrinking, may need to evict some items
                while self.currentBytes > newSize and self.cache:
                    self._evictOldest()

            self.maxBytes = newSize

    def getAllKeys(self) -> List[K]:
        with self.lock:
            return list(self.cache.keys())
