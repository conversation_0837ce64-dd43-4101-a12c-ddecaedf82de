"""
Cache registry for managing multiple cache instances.
"""

import threading
from typing import Dict, Optional

from kivy.logger import Logger

from cache_manager.config import getConfig
from cache_manager.core.cache import AbstractCache


class CacheRegistry:
    """
    Registry for managing multiple cache instances with total memory limit.

    Provides a central point for accessing and managing all caches in the system,
    with the ability to enforce a total memory usage limit across all caches.
    """

    def __init__(self, totalMemoryLimit: int = 512 * 1024 * 1024):
        """
        Initialize the cache registry with a total memory limit

        Args:
            totalMemoryLimit: Maximum total memory usage in bytes for all caches (default: 512MB)
        """
        self.caches: Dict[str, AbstractCache] = {}
        self.totalMemoryLimit = totalMemoryLimit
        self.lock = threading.RLock()

        Logger.info(
            f"Memory Manager: 🔧 Initialized cache registry with {totalMemoryLimit / (1024 * 1024):.2f} MB total limit"
        )

    def register(self, name: str, cache: AbstractCache) -> None:
        """
        Register a cache instance

        Args:
            name: Unique name for the cache
            cache: Cache instance to register
        """
        with self.lock:
            self.caches[name] = cache
            Logger.info(f"Memory Manager: ✅ Registered cache '{name}'")

            # Check if we need to enforce memory limits after adding a new cache
            self.enforceMemoryLimit()

    def unregister(self, name: str) -> None:
        """
        Unregister a cache instance

        Args:
            name: Name of the cache to unregister
        """
        with self.lock:
            if name in self.caches:
                del self.caches[name]
                Logger.info(f"Memory Manager: ❌ Unregistered cache '{name}'")

    def getCache(self, name: str) -> Optional[AbstractCache]:
        """
        Get a cache instance by name

        Args:
            name: Name of the cache

        Returns:
            The cache instance or None if not found
        """
        return self.caches.get(name)

    def getAllCaches(self) -> Dict[str, AbstractCache]:
        """
        Get all registered cache instances

        Returns:
            Dictionary of all cache instances
        """
        return self.caches.copy()

    def getTotalMemoryUsage(self) -> int:
        """
        Get total memory usage of all caches - Real-time calculation

        Returns:
            Total memory usage in bytes
        """
        with self.lock:
            return sum(cache.memoryUsage() for cache in self.caches.values())

    def getMemoryUsagePercentage(self) -> float:
        """
        Get memory usage as a percentage of the total limit

        Returns:
            Percentage of memory limit used (0-100)
        """
        with self.lock:
            if self.totalMemoryLimit <= 0:
                return 0.0
            return (self.getTotalMemoryUsage() / self.totalMemoryLimit) * 100.0

    def enforceMemoryLimit(self) -> bool:
        """
        Enforce the total memory limit by reducing cache sizes if necessary

        Returns:
            True if memory reduction was needed, False otherwise
        """
        with self.lock:
            totalUsage = self.getTotalMemoryUsage()

            # If we're under the limit, no action needed
            if totalUsage <= self.totalMemoryLimit:
                return False

            # Calculate how much memory we need to free
            # Use memoryTargetRatio from config to set cleanup target
            # This provides buffer for system operations and prevents frequent cleanups
            config = getConfig()
            toFree = totalUsage - int(self.totalMemoryLimit * config.memoryTargetRatio)
            Logger.warning(
                f"Memory Manager: ⚠️ Cache memory usage ({totalUsage / (1024 * 1024):.2f} MB) exceeds limit ({self.totalMemoryLimit / (1024 * 1024):.2f} MB)"
            )

            # Free memory using our strategy
            self._freeMemory(toFree)

            # Invalidate memory cache after cleanup
            pass  # No longer needed with incremental tracking

            # Check if we succeeded
            newUsage = self.getTotalMemoryUsage()
            Logger.info(
                f"Memory Manager: 📊 After enforcement: {newUsage / (1024 * 1024):.2f} MB / {self.totalMemoryLimit / (1024 * 1024):.2f} MB ({self.getMemoryUsagePercentage():.1f}%)"
            )

            return True

    def _freeMemory(self, bytesToFree: int) -> None:
        """
        Free the specified amount of memory by reducing cache sizes

        Args:
            bytesToFree: Amount of memory to free in bytes
        """
        if bytesToFree <= 0:
            return

        # Strategy 1: Resize caches proportionally to their size
        self._resizeCachesProportionally(bytesToFree)

        # Check if we need to free more memory
        remainingToFree = bytesToFree - (self.getTotalMemoryUsage() - self.totalMemoryLimit)

        # Strategy 2: If still over limit, clear least recently used caches
        if remainingToFree > 0:
            self._clearLeastUsedCaches(remainingToFree)

    def _resizeCachesProportionally(self, bytesToFree: int) -> None:
        """
        Resize caches proportionally to their size to free memory

        Args:
            bytesToFree: Amount of memory to free in bytes
        """
        # Batch collect cache information to avoid multiple memory calculations
        cacheInfos = self._collectCacheInfoBatch()

        if not cacheInfos:
            return

        # Sort by memory size (largest first)
        cacheInfos.sort(key=lambda x: x[2], reverse=True)

        # Calculate total size from collected data
        totalSize = sum(info[2] for info in cacheInfos)

        # Skip if total size is zero
        if totalSize == 0:
            return

        # Calculate reduction factor with protection against over-reduction
        # Use cacheReductionRatio from config to limit single cleanup reduction
        # This prevents cache performance cliff while allowing gradual memory recovery
        config = getConfig()
        reductionFactor = min(config.cacheReductionRatio, bytesToFree / totalSize)

        # Resize each cache
        for name, cache, _memorySize in cacheInfos:
            # Calculate new size
            currentItems = cache.size()
            if currentItems <= 1:
                continue  # Skip caches with 0 or 1 items

            newItems = max(1, int(currentItems * (1 - reductionFactor)))

            # Only resize if there's a change
            if newItems < currentItems:
                Logger.info(f"Memory Manager: 📉 Resizing cache '{name}' from {currentItems} to {newItems} items")
                cache.resize(newItems)

    def _clearLeastUsedCaches(self, bytesToFree: int) -> None:
        """
        Clear least recently used caches to free memory

        Args:
            bytesToFree: Amount of memory to free in bytes
        """
        # Batch collect cache information to avoid multiple calls
        cacheInfos = []
        for name, cache in self.caches.items():
            stats = cache.getStats()
            memorySize = cache.memoryUsage()  # Single call per cache
            cacheInfos.append((name, cache, stats.lastAccessTime, memorySize))

        # Sort by last access time (oldest first)
        cacheInfos.sort(key=lambda x: x[2])

        # Clear caches until we've freed enough memory
        freedBytes = 0
        for name, cache, _lastAccess, memorySize in cacheInfos:
            if freedBytes >= bytesToFree:
                break

            # Only clear if cache has items
            if cache.size() > 0:
                Logger.warning(
                    f"Memory Manager: 🧹 Clearing cache '{name}' ({memorySize / (1024 * 1024):.2f} MB) to free memory"
                )
                cache.clear()
                freedBytes += memorySize

    def clearAll(self) -> None:
        """Clear all caches"""
        with self.lock:
            for name, cache in self.caches.items():
                Logger.info(f"Memory Manager: 🧹 Clearing cache '{name}'")
                cache.clear()

    def setTotalMemoryLimit(self, newLimit: int) -> None:
        """
        Set a new total memory limit

        Args:
            newLimit: New memory limit in bytes
        """
        with self.lock:
            oldLimit = self.totalMemoryLimit
            self.totalMemoryLimit = newLimit
            Logger.info(
                f"Memory Manager: 🔄 Changed total memory limit from {oldLimit / (1024 * 1024):.2f} MB to {newLimit / (1024 * 1024):.2f} MB"
            )

            # Enforce the new limit
            self.enforceMemoryLimit()

    def _collectCacheInfoBatch(self) -> list:
        """
        Batch collect cache information (name, cache, memorySize) to minimize lock contention

        Returns:
            List of tuples (name, cache, memorySize)
        """
        cacheInfos = []
        for name, cache in self.caches.items():
            memorySize = cache.memoryUsage()
            cacheInfos.append((name, cache, memorySize))
        return cacheInfos

    def getMemoryReport(self) -> Dict:
        """
        Get a detailed memory usage report

        Returns:
            Dictionary with memory usage information
        """
        with self.lock:
            totalUsage = self.getTotalMemoryUsage()
            percentage = self.getMemoryUsagePercentage()

            # Get per-cache information
            cacheDetails = []
            for name, cache in self.caches.items():
                usage = cache.memoryUsage()
                cacheDetails.append(
                    {
                        "name": name,
                        "items": cache.size(),
                        "memoryBytes": usage,
                        "memoryMB": usage / (1024 * 1024),
                        "percentage": (usage / self.totalMemoryLimit * 100) if self.totalMemoryLimit > 0 else 0,
                        "stats": cache.getStats().__dict__,
                    }
                )

            # Sort by memory usage (largest first)
            cacheDetails.sort(key=lambda x: x["memoryBytes"], reverse=True)

            return {
                "totalMemoryBytes": totalUsage,
                "totalMemoryMB": totalUsage / (1024 * 1024),
                "limitBytes": self.totalMemoryLimit,
                "limitMB": self.totalMemoryLimit / (1024 * 1024),
                "usagePercentage": percentage,
                "cacheCount": len(self.caches),
                "caches": cacheDetails,
            }
