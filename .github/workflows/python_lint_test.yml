name: Lint & Unit Test

on: [ pull_request, workflow_dispatch ]

jobs:
  python-lint-and-test:
    name: Python Lint & Unit Test
    runs-on: ubuntu-latest
    steps:
      # 1. Checkout the code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      # 2. Set up SSH key
      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.CI_SSH_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          eval "$(ssh-agent -s)"
          ssh-add ~/.ssh/id_ed25519
          ssh-keyscan github.com >> ~/.ssh/known_hosts
          echo "Host github.com\n  User git\n  IdentityFile ~/.ssh/id_ed25519\n  StrictHostKeyChecking accept-new" >> ~/.ssh/config
        shell: bash

      # 3. Set up Python
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pipenv'

      # 4. Install pipenv
      - name: Install pipenv
        run: pip install pipenv

      # 5. Install dependencies
      - name: Install dependencies
        run: pipenv install --dev --deploy

      # 6. Lint the code
      - name: Run ruff lint
        run: |
          pipenv run ruff check
          pipenv run ruff format --check

      # 7. Run Python unit tests
      - name: Run Python unit tests
        run: pipenv run pytest
