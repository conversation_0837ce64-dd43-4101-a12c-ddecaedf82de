import pytest

from chains.tests import (
    ADDRESS1,
    ADDRESS2,
    PANIC_TOKEN,
    PANIC_TOKEN_ISSUE_TX,
    PANIC_TOKEN_TRANSFER_TX,
    TOKEN_ISSUE_TX_SUFFICIENT_FUEL,
    TOKEN_TRANSFER_TX_INSUFFICIENT_FUEL,
    TOKEN_TRANSFER_TX_SUFFICIENT_FUEL,
)
from common import hexToBytes
from common.models import TransactionExecutionStatus
from config import CONFIG
from vm import ContractExecutor

Alice = ADDRESS1
Bob = ADDRESS2


def register_contract(state, wasmPath: str, address: str):
    """
    Register the token contract.
    """
    with open(wasmPath, "rb") as f:
        contractCode = f.read()
    # state create contract
    state.createContract(hexToBytes(address))
    # state set code
    state.setCode(hexToBytes(address), contractCode)


@pytest.mark.asyncio
async def test_transaction_fuel(sposChain):
    """
    Test applying token contract transactions with fuel consumption.
    Includes successful transaction, insufficient fuel transaction.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}

    tokenContractClient = ContractExecutor(address=CONFIG.tokenAddress)

    # Set useFuel to False in the first transaction.
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_ISSUE_TX_SUFFICIENT_FUEL, blockEnv, 2, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # check balance after issue 100_0000 to Alice
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000

    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_TRANSFER_TX_SUFFICIENT_FUEL, blockEnv, 3, True
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # check balance, Alice transfer 20_0000 to Bob, fuel consumed 20_0000
    # Alice balance = 100_0000 - 20_0000 - 30_0000 = 50_0000
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 50_0000

    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Bob)
    assert err is None
    assert balance == 20_0000

    # Alice transfer to Bob without enough fuel (10000)
    # transfer failed but fuel consumed
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_TRANSFER_TX_INSUFFICIENT_FUEL, blockEnv, 4, True
    )
    assert receipt.status == TransactionExecutionStatus.FAILURE

    # check balance, Alice balance = 50_0000 - 10000 = 49_0000
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 49_0000

    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Bob)
    assert err is None
    assert balance == 20_0000


@pytest.mark.asyncio
async def test_panic_transfer(sposChain):
    """
    Test contract execution failed but fuel still consumed.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}

    tokenContractClient = ContractExecutor(address=CONFIG.tokenAddress)

    # register the panic token contract
    testTokenPath = "chains/tests/panic_token.wasm"
    register_contract(state, testTokenPath, PANIC_TOKEN)
    panicTokenContractClient = ContractExecutor(address=PANIC_TOKEN)
    panicTokenContractClient.constructor(state, "vgraph_token")

    # issue 100_0000 token to Alice for executing contract function
    # Set useFuel to False in the first transaction.
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, TOKEN_ISSUE_TX_SUFFICIENT_FUEL, blockEnv, 2, useFuel=False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000

    # call contract function failed
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, PANIC_TOKEN_ISSUE_TX, blockEnv, 3, useFuel=True
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # check panic-token balance and token balance
    balance, err = panicTokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000

    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 90_0000

    # call panic token transfer
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, PANIC_TOKEN_TRANSFER_TX, blockEnv, 4, useFuel=True
    )
    assert receipt.status == TransactionExecutionStatus.FAILURE

    # check balance
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 70_0000

    balance, err = panicTokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000


@pytest.mark.asyncio
async def test_panic_token_transfer(sposChain):
    """
    Test applying panic token contract transaction with fuel consumption.
    If a panic occurs during the fuel consumption transfer itself, the state should be reverted and fuel should be refunded.
    """
    state = sposChain.statedbManager.getState()
    blockEnv = {}

    # set panic token to tokenAddress in CONFIG
    CONFIG.tokenAddress = PANIC_TOKEN

    # register the panic token contract
    testTokenAddress = CONFIG.tokenAddress
    testTokenPath = "chains/tests/panic_token.wasm"
    register_contract(state, testTokenPath, testTokenAddress)
    tokenContractClient = ContractExecutor(address=testTokenAddress)
    tokenContractClient.constructor(state, "vgraph_token")

    # Set useFuel to False in the first transaction.
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, PANIC_TOKEN_ISSUE_TX, blockEnv, 2, False
    )
    assert receipt.status == TransactionExecutionStatus.SUCCESS

    # check balance after issue 100_0000 to Alice
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000

    # use a transfer function with panic to consume fuel
    # state should be reverted because fuel consumption transfer itself failed
    receipt = sposChain.chainStateManager.transactionProcessor.applyTransaction(
        state, PANIC_TOKEN_TRANSFER_TX, blockEnv, 3, True
    )
    assert receipt.status == TransactionExecutionStatus.FAILURE

    # check balance
    balance, err = tokenContractClient.executeReadOnly(state, "balance", int, Alice)
    assert err is None
    assert balance == 100_0000
