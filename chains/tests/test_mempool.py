import random
import time
from copy import deepcopy

import pytest

from chains.mempool import MemoryPool
from common import bytesToHex
from common.models import OperationCallContract, OperationType, Transaction


def createTestTransaction(sender="0x1234", fuel=100, dependentHash="", timestamp=None):
    """Helper function to create a test transaction"""
    if timestamp is None:
        timestamp = int(time.time() * 1000000)  # Use microseconds for uniqueness

    operationData = OperationCallContract(
        op_type=OperationType.CALL_CONTRACT, contract_address="0x5678", function_name="test", parameters=[]
    )

    transaction = Transaction(
        sender=sender,
        timestamp=timestamp,
        dependent_transaction_hash=dependentHash,
        fuel=fuel,
        op_data=operationData,
        public_keys=[],
        signatures=[],
    )
    return transaction


def testMempoolSortByFuel():
    """Test that transactions are sorted by fuel (higher fuel first)"""
    mempool = MemoryPool()

    # Add transactions with different fuel values
    transaction1 = createTestTransaction(fuel=100)
    transaction2 = createTestTransaction(fuel=200)
    transaction3 = createTestTransaction(fuel=50)

    mempool.addTransaction(transaction1)
    mempool.addTransaction(transaction2)
    mempool.addTransaction(transaction3)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that transactions are sorted by fuel (higher fuel first)
    assert len(sortedTransactions) == 3
    assert sortedTransactions[0].fuel == 200
    assert sortedTransactions[1].fuel == 100
    assert sortedTransactions[2].fuel == 50


def testMempoolDependencyOrdering():
    """Test that transactions respect dependencies"""
    mempool = MemoryPool()

    # Create transactions with dependencies
    transaction1 = createTestTransaction(fuel=100)
    transaction1Hash = bytesToHex(transaction1.hash())

    transaction2 = createTestTransaction(fuel=200, dependentHash=transaction1Hash)
    transaction2Hash = bytesToHex(transaction2.hash())

    transaction3 = createTestTransaction(fuel=300, dependentHash=transaction2Hash)
    transaction3Hash = bytesToHex(transaction3.hash())

    # Add transactions in reverse order
    mempool.addTransaction(transaction3)
    mempool.addTransaction(transaction2)
    mempool.addTransaction(transaction1)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that all transactions are included
    assert len(sortedTransactions) == 3

    # Get transaction hashes from the sorted result
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]
    transaction1Index = resultHashes.index(transaction1Hash)
    transaction2Index = resultHashes.index(transaction2Hash)
    transaction3Index = resultHashes.index(transaction3Hash)

    # Check that dependencies are respected
    # transaction1 should come before transaction2, and transaction2 should come before transaction3
    assert transaction1Index < transaction2Index < transaction3Index


def testMempoolCircularDependency():
    """Test that circular dependencies are handled correctly"""
    mempool = MemoryPool()

    # Create transactions with circular dependencies
    transaction1 = createTestTransaction(fuel=100)
    transaction1Hash = bytesToHex(transaction1.hash())

    transaction2 = createTestTransaction(fuel=200, dependentHash=transaction1Hash)
    transaction2Hash = bytesToHex(transaction2.hash())

    # Create a circular dependency: transaction3 depends on transaction2, and transaction1 depends on transaction3
    transaction3 = createTestTransaction(fuel=300, dependentHash=transaction2Hash)
    transaction3Hash = bytesToHex(transaction3.hash())

    # Update transaction1 to depend on transaction3, creating a cycle
    transaction1Circular = deepcopy(transaction1)
    transaction1Circular.dependent_transaction_hash = transaction3Hash

    # Add transactions
    mempool.addTransaction(transaction3)
    mempool.addTransaction(transaction2)
    mempool.addTransaction(transaction1Circular)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that all transactions are included despite the cycle
    assert len(sortedTransactions) == 3

    # Verify that all transactions are included
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]
    assert bytesToHex(transaction1Circular.hash()) in resultHashes
    assert transaction2Hash in resultHashes
    assert transaction3Hash in resultHashes

    # In our implementation, we break cycles by removing the dependency from the lowest fuel transaction
    # The cycle should be broken, and all transactions should still be included


def testMempoolMixedDependencies():
    """Test a mix of dependencies and fuel priorities"""
    mempool = MemoryPool()

    # Create two independent transactions with different fuel values
    transaction1 = createTestTransaction(fuel=100)
    transaction1Hash = bytesToHex(transaction1.hash())

    transaction2 = createTestTransaction(fuel=200)
    transaction2Hash = bytesToHex(transaction2.hash())

    # Create a transaction that depends on transaction1
    transaction3 = createTestTransaction(fuel=300, dependentHash=transaction1Hash)
    transaction3Hash = bytesToHex(transaction3.hash())

    # Add transactions in mixed order
    mempool.addTransaction(transaction3)
    mempool.addTransaction(transaction2)
    mempool.addTransaction(transaction1)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that all transactions are included
    assert len(sortedTransactions) == 3

    # Get transaction hashes from the sorted result
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]

    # Check that all transactions are in the result
    assert transaction1Hash in resultHashes
    assert transaction2Hash in resultHashes
    assert transaction3Hash in resultHashes

    # Find indices to verify dependency ordering
    transaction1Index = resultHashes.index(transaction1Hash)
    transaction3Index = resultHashes.index(transaction3Hash)

    # Verify that transaction1 comes before transaction3 (since transaction3 depends on transaction1)
    assert transaction1Index < transaction3Index, "transaction1 should come before transaction3"


def testMempoolInvalidDependency():
    """Test handling of invalid dependencies"""
    mempool = MemoryPool()

    # Create a transaction with an invalid dependency hash
    transaction1 = createTestTransaction(fuel=100, dependentHash="invalid_hash")

    # Add the transaction
    mempool.addTransaction(transaction1)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that the transaction is included despite the invalid dependency
    assert len(sortedTransactions) == 1
    assert sortedTransactions[0].hash() == transaction1.hash()


def testMempoolTransactionLimit():
    """Test that the transaction limit is respected while maintaining dependencies"""
    mempool = MemoryPool()

    # Create a chain of dependencies: transaction1 <- transaction2 <- transaction3 <- ... <- transaction10
    transactions = []
    previousHash = ""

    # Create 10 transactions with a chain of dependencies
    for i in range(10):
        transaction = createTestTransaction(fuel=100 - i, dependentHash=previousHash)
        transactions.append(transaction)
        previousHash = bytesToHex(transaction.hash())

    # Add transactions to mempool in reverse order
    for transaction in reversed(transactions):
        mempool.addTransaction(transaction)

    # Get transactions with a limit of 5
    sortedTransactions = mempool.getTransactions(maxTransactions=5)

    # Check that we get at most 5 transactions
    assert len(sortedTransactions) <= 5

    # Check that dependencies are respected
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]
    for tx in sortedTransactions:
        if tx.dependent_transaction_hash and tx.dependent_transaction_hash != "":
            assert tx.dependent_transaction_hash in resultHashes

    # Create a more complex scenario with multiple dependency chains
    mempool = MemoryPool()

    # Chain 1: high fuel transactions
    chain1 = []
    previousHash = ""
    for i in range(5):
        transaction = createTestTransaction(fuel=200 - i, dependentHash=previousHash)
        chain1.append(transaction)
        previousHash = bytesToHex(transaction.hash())

    # Chain 2: medium fuel transactions
    chain2 = []
    previousHash = ""
    for i in range(5):
        transaction = createTestTransaction(fuel=150 - i, dependentHash=previousHash)
        chain2.append(transaction)
        previousHash = bytesToHex(transaction.hash())

    # Chain 3: low fuel transactions
    chain3 = []
    previousHash = ""
    for i in range(5):
        transaction = createTestTransaction(fuel=100 - i, dependentHash=previousHash)
        chain3.append(transaction)
        previousHash = bytesToHex(transaction.hash())

    # Add all transactions to mempool
    for transaction in chain1 + chain2 + chain3:
        mempool.addTransaction(transaction)

    # Get transactions with a limit of 7
    sortedTransactions = mempool.getTransactions(maxTransactions=7)

    # Check that we get at most 7 transactions
    assert len(sortedTransactions) <= 7

    # Check that dependencies are respected
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]
    for tx in sortedTransactions:
        if tx.dependent_transaction_hash and tx.dependent_transaction_hash != "":
            assert tx.dependent_transaction_hash in resultHashes


def testMempoolDependentTransactionOnChainWithIsOnChain():
    """Test that when a dependent transaction is already on chain, it's handled correctly with isOnChain function"""

    # Create transactions
    transaction1 = createTestTransaction(fuel=100)
    transaction1Hash = bytesToHex(transaction1.hash())

    # Create a function to check if a transaction is on chain
    def isOnChain(txHash: bytes) -> bool:
        return bytesToHex(txHash) == transaction1Hash

    mempool = MemoryPool(isOnChain=isOnChain)

    # Create a chain of transactions
    # tx1 <- tx2 <- tx3
    # where tx1 is already on chain (not in mempool)
    transaction2 = createTestTransaction(fuel=200, dependentHash=transaction1Hash)
    transaction2Hash = bytesToHex(transaction2.hash())

    transaction3 = createTestTransaction(fuel=300, dependentHash=transaction2Hash)

    # Only add tx2 and tx3 to mempool (tx1 is assumed to be on chain)
    mempool.addTransaction(transaction2)
    mempool.addTransaction(transaction3)

    # Get sorted transactions
    sortedTransactions = mempool.getTransactions()

    # Check that all transactions in mempool are included
    assert len(sortedTransactions) == 2

    # Get transaction hashes from the sorted result
    resultHashes = [bytesToHex(tx.hash()) for tx in sortedTransactions]
    transaction2Index = resultHashes.index(transaction2Hash)
    transaction3Index = resultHashes.index(bytesToHex(transaction3.hash()))

    # In our implementation, when a transaction depends on another transaction,
    # the dependency comes first, regardless of fuel
    # So tx2 (which tx3 depends on) should come before tx3
    assert transaction2Index < transaction3Index, "tx2 should come before tx3 because tx3 depends on tx2"

    # Verify that the dependencies are maintained
    tx2_in_results = next(tx for tx in sortedTransactions if tx.hash() == transaction2.hash())
    assert tx2_in_results.dependent_transaction_hash == transaction1Hash, "tx2's dependency on tx1 should be maintained"

    tx3_in_results = next(tx for tx in sortedTransactions if tx.hash() == transaction3.hash())
    assert tx3_in_results.dependent_transaction_hash == transaction2Hash, "tx3's dependency on tx2 should be maintained"


def testMempoolTransactionAlreadyOnChain():
    """Test that when a transaction is already on chain, it's not added to the mempool"""
    # Create transactions
    transaction1 = createTestTransaction(fuel=100)
    transaction1Hash = bytesToHex(transaction1.hash())

    # Create a function to check if a transaction is on chain
    def isOnChain(txHash: bytes) -> bool:
        return bytesToHex(txHash) == transaction1Hash

    mempool = MemoryPool(isOnChain=isOnChain)

    transaction2 = createTestTransaction(fuel=200)

    # Try to add transaction1 (which is "on chain") to the mempool
    mempool.addTransaction(transaction1)

    # Add transaction2 to the mempool
    mempool.addTransaction(transaction2)

    # Get transactions from the mempool
    sortedTransactions = mempool.getTransactions()

    # Check that transaction1 is not in the mempool
    assert len(sortedTransactions) == 1, "Only transaction2 should be in the mempool"
    assert sortedTransactions[0].hash() == transaction2.hash(), "transaction2 should be in the mempool"


def create_complex_dependency_graph(numTransactions=1000, dependencyProbability=0.3, cycleProbability=0.05):
    """
    Create a complex transaction dependency graph

    Args:
        numTransactions: Number of transactions
        dependencyProbability: Probability of a transaction having a dependency
        cycleProbability: Probability of creating cyclic dependencies

    Returns:
        List of transactions
    """
    transactions = []
    transaction_hash_map = {}

    # First create all transactions
    for _ in range(numTransactions):
        fuel = random.randint(1, 1000)
        tx = createTestTransaction(fuel=fuel)
        transactions.append(tx)
        txHash = bytesToHex(tx.hash())
        transaction_hash_map[txHash] = tx

    # Then add dependencies
    for i in range(numTransactions):
        if random.random() < dependencyProbability:
            # Randomly select a dependency
            dependentIdx = random.randint(0, numTransactions - 1)
            if dependentIdx != i:  # Avoid self-dependency
                dependentHash = bytesToHex(transactions[dependentIdx].hash())
                transactions[i].dependent_transaction_hash = dependentHash

    # Add some cyclic dependencies
    if cycleProbability > 0:
        numCycles = int(numTransactions * cycleProbability)
        for _ in range(numCycles):
            # Create a cycle of random length
            cycleLength = random.randint(2, min(5, numTransactions // 10))
            cycleStart = random.randint(0, numTransactions - cycleLength)

            # Create cyclic dependencies
            for j in range(cycleLength):
                currentIdx = cycleStart + j
                nextIdx = cycleStart + (j + 1) % cycleLength
                transactions[currentIdx].dependent_transaction_hash = bytesToHex(transactions[nextIdx].hash())

    return transactions


@pytest.mark.benchmark(group="mempool")
def test_mempool_getTransactions_performance(benchmark):
    """Test the performance of getTransactions method"""
    # Create a memory pool with a large number of transactions
    mempool = MemoryPool()

    # Create a complex dependency graph
    transactions = create_complex_dependency_graph(
        numTransactions=1000, dependencyProbability=0.3, cycleProbability=0.05
    )

    # Add transactions to the memory pool
    for tx in transactions:
        mempool.addTransaction(tx)

    # Use benchmark to test the performance of getTransactions method
    result = benchmark(mempool.getTransactions)

    # Verify results
    assert len(result) == len(transactions)


@pytest.mark.benchmark(group="mempool")
def test_mempool_getTransactions_with_limit_performance(benchmark):
    """Test the performance of getTransactions method with a transaction limit"""
    # Create a memory pool with a large number of transactions
    mempool = MemoryPool()

    # Create a complex dependency graph
    transactions = create_complex_dependency_graph(
        numTransactions=1000, dependencyProbability=0.3, cycleProbability=0.05
    )

    # Add transactions to the memory pool
    for tx in transactions:
        mempool.addTransaction(tx)

    # Use benchmark to test the performance of getTransactions method with a limit
    result = benchmark(lambda: mempool.getTransactions(maxTransactions=500))

    # Verify results
    assert len(result) <= 500


@pytest.mark.benchmark(group="mempool")
def test_mempool_getTransactions_with_cycles_performance(benchmark):
    """Test the performance of getTransactions method with cyclic dependencies"""
    # Create a memory pool with a large number of transactions and cyclic dependencies
    mempool = MemoryPool()

    # Create a complex dependency graph with increased probability of cycles
    transactions = create_complex_dependency_graph(numTransactions=500, dependencyProbability=0.5, cycleProbability=0.2)

    # Add transactions to the memory pool
    for tx in transactions:
        mempool.addTransaction(tx)

    # Use benchmark to test the performance of getTransactions method with cyclic dependencies
    result = benchmark(mempool.getTransactions)

    # Verify results
    assert len(result) == len(transactions)
