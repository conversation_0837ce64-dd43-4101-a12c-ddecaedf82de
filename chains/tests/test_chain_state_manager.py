from typing import Any, List

import orjson
import pytest
import serde.json

import vm
from chains.tests import ADDRESS1, ADDRESS2, ADDRESS3, PRIVATE_KEY1, PRIVATE_KEY2
from chains.vgraph_pow_primechain.session import QueryContractParams
from common.models import Block, OperationCallContract, OperationType, Transaction
from common.signature import signTransactionWithPrivateKey

FUEL = 1000000


@pytest.mark.asyncio
# @pytest.mark.skip(reason="for generate data")
@pytest.mark.parametrize(
    "name, blocksTransactions",
    [
        (
            "short_chain",
            [
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="issue",
                                # issue enough fuels to ADDRESS1 to pay for the `transfer` transaction
                                parameters=[ADDRESS1, FUEL + 1000],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=1,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS1, ADDRESS2, 100],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=2,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
            ],
        ),
        (
            "long_chain",
            [
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS2,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="issue",
                                # issue enough fuels to ADDRESS2 to pay for the `transfer` transaction
                                parameters=[ADDRESS2, FUEL + 1000],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=1,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY2,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS1, ADDRESS2, 100],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=2,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS2,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS2, ADDRESS3, 50],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=3,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY2,
                    )
                ],
            ],
        ),
    ],
)
async def test_generate_pow_chain(powChain, name, blocksTransactions):
    genesisBlock = powChain.chainStateManager.getBlock(powChain.chainStateManager.genesis().hash(), 0)
    chain = [genesisBlock]

    for transactions in blocksTransactions:
        block = powChain.miner._getCandidateBlock(transactions)
        powChain.chainStateManager.processBlock(block, False)
        chain.append(block)

    # write to json file
    with open(f"chains/tests/pow_reorg_test_{name}.json", "w", encoding="utf-8") as f:
        f.write(serde.json.to_json(chain, option=orjson.OPT_INDENT_2))


@pytest.mark.asyncio
# @pytest.mark.skip(reason="for generate data")
@pytest.mark.parametrize(
    "name, blocksTransactions",
    [
        (
            "short_chain",
            [
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="issue",
                                # issue enough fuels to ADDRESS1 to pay for the `transfer` transaction
                                parameters=[ADDRESS1, FUEL + 1000],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=1,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS1, ADDRESS2, 100],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=2,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
            ],
        ),
        (
            "long_chain",
            [
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS2,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="issue",
                                # issue enough fuels to ADDRESS2 to pay for the `transfer` transaction
                                parameters=[ADDRESS2, FUEL + 1000],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=1,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY2,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS1,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS1, ADDRESS2, 100],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=2,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY1,
                    )
                ],
                [
                    signTransactionWithPrivateKey(
                        Transaction(
                            dependent_transaction_hash="",
                            sender=ADDRESS2,
                            op_data=OperationCallContract(
                                op_type=OperationType.CALL_CONTRACT,
                                contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
                                function_name="transfer",
                                parameters=[ADDRESS2, ADDRESS3, 50],
                            ),
                            public_keys=[],
                            signatures=[],
                            timestamp=3,
                            fuel=FUEL,
                        ),
                        PRIVATE_KEY2,
                    )
                ],
            ],
        ),
    ],
)
async def test_generate_spos_chain(sposChain, name, blocksTransactions):
    genesisBlock = sposChain.chainStateManager.getBlock(sposChain.chainStateManager.genesis().hash(), 0)
    chain = [genesisBlock]

    for transactions in blocksTransactions:
        block = sposChain.minter._getCandidateBlock(transactions)
        sposChain.chainStateManager.processBlock(block, False)
        chain.append(block)

    # write to json file
    with open(f"chains/tests/spos_reorg_test_{name}.json", "w", encoding="utf-8") as f:
        f.write(serde.json.to_json(chain, option=orjson.OPT_INDENT_2))


def queryBalance(state, account: str) -> Any:
    """
    query balance of an address
    """
    return queryContract(
        state,
        QueryContractParams(
            query_api_key=None,
            snapshot_transaction_hash="",
            contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
            function_name="balance",
            args=[account],
        ),
    )


def queryContract(state, queryContractParams: QueryContractParams) -> Any:
    """
    query a contract function
    """
    # handle signatures and public keys
    contractExecutor = vm.ContractExecutor(
        address=queryContractParams.contract_address,
    )

    return contractExecutor.executeReadOnlyWithEnv(
        state,
        queryContractParams.toQueryEnv(),
        queryContractParams.function_name,
        Any,
        *queryContractParams.args,
    )


@pytest.mark.asyncio
async def test_pow_reorg_switch_chain(powChain):
    """
    Test for POW reorg
    """
    with open("chains/tests/pow_reorg_test_short_chain.json", "r", encoding="utf-8") as f:
        oldChain = serde.json.from_json(List[Block], f.read())
    with open("chains/tests/pow_reorg_test_long_chain.json", "r", encoding="utf-8") as f:
        newChain = serde.json.from_json(List[Block], f.read())

    # connect old chain, first block is genesis block, already connected
    for block in oldChain[1:]:
        powChain.chainStateManager.processBlock(block, False)

    # check balance of old chain
    state = powChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (900, None)
    assert queryBalance(state, ADDRESS2) == (100, None)
    assert queryBalance(state, ADDRESS3) == (0, None)

    # mock sync the new chain
    powChain.chainStateManager.processSyncBlocks(newChain)

    # check memory pool, should have 1 transaction
    # the transaction is from the old chain but not in the new chain
    assert len(powChain.memoryPool.transactions) == 1
    assert oldChain[1].transactions[1].hash() in powChain.memoryPool.transactions

    # check current block, should be the last block in the new chain
    assert powChain.chainStateManager.currentBlock().hash() == newChain[-1].hash()
    # traverse check the canonical chain, should be the new chain
    header = powChain.chainStateManager.currentHeader()
    while header is not None:
        assert header.hash() == newChain[header.height].hash()
        header = powChain.chainStateManager.getHeaderByHash(header.parent_hash)

    # check header chain cache
    headerCache = powChain.headerChain.headerCache.get()
    heightCache = powChain.headerChain.heightCache.get()
    for block in oldChain[1:]:
        # traverse check the header chain cache, the old chain should be removed
        assert not headerCache.contains(block.hash())
        # traverse check the height chain cache, the old chain should be removed
        assert not heightCache.contains(block.hash())

    # check indexes
    # check transactionLookupCache
    transactionLookupCache = powChain.chainStateManager.transactionLookupCache
    for block in oldChain[1:]:
        for tx in block.transactions:
            assert not transactionLookupCache.contains(tx.hash())

    # check chain state manager cache
    for block in oldChain[1:]:
        assert not powChain.chainStateManager.blockCache.contains(block.hash())
        assert not powChain.chainStateManager.bodyCache.contains(block.hash())
        assert not powChain.chainStateManager.bodyRLPCache.contains(block.hash())
        assert not powChain.chainStateManager.receiptsCache.contains(block.hash())

    # check contract data
    state = powChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (0, None)
    assert queryBalance(state, ADDRESS2) == (950, None)
    assert queryBalance(state, ADDRESS3) == (50, None)


@pytest.mark.asyncio
async def test_pow_reorg_just_forward(powChain):
    """
    Test for POW reorg, just forward
    """
    with open("chains/tests/pow_reorg_test_long_chain.json", "r", encoding="utf-8") as f:
        newChain = serde.json.from_json(List[Block], f.read())

    oldChain = newChain[:2]

    # connect old chain, first block is genesis block, already connected
    for block in oldChain[1:]:
        powChain.chainStateManager.processBlock(block, False)

    # mock sync the new chain
    powChain.chainStateManager.processSyncBlocks(newChain)

    # check memory pool, should have 0 transaction, cause reorg just forward
    # the transaction is from the old chain but not in the new chain
    assert len(powChain.memoryPool.transactions) == 0

    # check current block, should be the last block in the new chain
    assert powChain.chainStateManager.currentBlock().hash() == newChain[-1].hash()
    # traverse check the canonical chain, should be the new chain
    header = powChain.chainStateManager.currentHeader()
    while header is not None:
        assert header.hash() == newChain[header.height].hash()
        header = powChain.chainStateManager.getHeaderByHash(header.parent_hash)

    # check contract data
    state = powChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (0, None)
    assert queryBalance(state, ADDRESS2) == (950, None)
    assert queryBalance(state, ADDRESS3) == (50, None)


@pytest.mark.asyncio
async def test_spos_reorg_switch_chain(sposChain):
    """
    Test for SPOS reorg, switch chain
    """
    with open("chains/tests/spos_reorg_test_short_chain.json", "r", encoding="utf-8") as f:
        oldChain = serde.json.from_json(List[Block], f.read())
    with open("chains/tests/spos_reorg_test_long_chain.json", "r", encoding="utf-8") as f:
        newChain = serde.json.from_json(List[Block], f.read())

    # connect old chain, first block is genesis block, already connected
    for block in oldChain[1:]:
        sposChain.chainStateManager.processBlock(block, False)

    # check balance of old chain
    state = sposChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (900, None)
    assert queryBalance(state, ADDRESS2) == (100, None)
    assert queryBalance(state, ADDRESS3) == (0, None)

    # mock sync the new chain
    sposChain.chainStateManager.processSyncBlocks(newChain)

    # check memory pool, should have 1 transaction, cause reorg switch chain
    # the transaction is from the old chain but not in the new chain
    assert len(sposChain.memoryPool.transactions) == 1
    assert oldChain[1].transactions[1].hash() in sposChain.memoryPool.transactions

    # check current block, should be the last block in the new chain
    assert sposChain.chainStateManager.currentBlock().hash() == newChain[-1].hash()
    # traverse check the canonical chain, should be the new chain
    header = sposChain.chainStateManager.currentHeader()
    while header is not None:
        assert header.hash() == newChain[header.height].hash()
        header = sposChain.chainStateManager.getHeaderByHash(header.parent_hash)

    # check header chain cache
    headerCache = sposChain.headerChain.headerCache.get()
    heightCache = sposChain.headerChain.heightCache.get()
    for block in oldChain[1:]:
        # traverse check the header chain cache, the old chain should be removed
        assert not headerCache.contains(block.hash())
        # traverse check the height chain cache, the old chain should be removed
        assert not heightCache.contains(block.hash())

    # check indexes
    # check transactionLookupCache
    transactionLookupCache = sposChain.chainStateManager.transactionLookupCache
    for block in oldChain[1:]:
        for tx in block.transactions:
            assert not transactionLookupCache.contains(tx.hash())

    # check chain state manager cache
    for block in oldChain[1:]:
        assert not sposChain.chainStateManager.blockCache.contains(block.hash())
        assert not sposChain.chainStateManager.bodyCache.contains(block.hash())
        assert not sposChain.chainStateManager.bodyRLPCache.contains(block.hash())
        assert not sposChain.chainStateManager.receiptsCache.contains(block.hash())

    # check contract data
    state = sposChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (0, None)
    assert queryBalance(state, ADDRESS2) == (950, None)
    assert queryBalance(state, ADDRESS3) == (50, None)


@pytest.mark.asyncio
async def test_spos_reorg_forward_chain(sposChain):
    """
    Test for SPOS reorg, forward chain
    """
    with open("chains/tests/spos_reorg_test_long_chain.json", "r", encoding="utf-8") as f:
        newChain = serde.json.from_json(List[Block], f.read())

    oldChain = newChain[:2]

    # connect old chain, first block is genesis block, already connected
    for block in oldChain[1:]:
        sposChain.chainStateManager.processBlock(block, False)

    # mock sync the new chain
    sposChain.chainStateManager.processSyncBlocks(newChain)

    # check memory pool, should have 0 transaction, cause reorg just forward
    # the transaction is from the old chain but not in the new chain
    assert len(sposChain.memoryPool.transactions) == 0

    # check current block, should be the last block in the new chain
    assert sposChain.chainStateManager.currentBlock().hash() == newChain[-1].hash()
    # traverse check the canonical chain, should be the new chain
    header = sposChain.chainStateManager.currentHeader()
    while header is not None:
        assert header.hash() == newChain[header.height].hash()
        header = sposChain.chainStateManager.getHeaderByHash(header.parent_hash)

    # check contract data
    state = sposChain.statedbManager.getState()
    assert queryBalance(state, ADDRESS1) == (0, None)
    assert queryBalance(state, ADDRESS2) == (950, None)
    assert queryBalance(state, ADDRESS3) == (50, None)
