import pytest

from chains.tests import TRANSACTIONS_200


@pytest.mark.asyncio
@pytest.mark.benchmark(group="pow")
async def test_get_block(powChain, benchmark):
    """
    Benchmark for chainStateManager.getBlock

    test for getting block info
    """
    block = powChain.chainStateManager.genesis()
    benchmark(powChain.chainStateManager.getBlock, block.hash(), block.height)


@pytest.mark.asyncio
@pytest.mark.benchmark(group="pow")
async def test_get_blocks(powChain, benchmark):
    """
    Benchmark for chainStateManager.getBlocks

    test for locator generator algorithm
    """
    locator = []
    benchmark(powChain.chainStateManager.getBlocks, locator)


@pytest.mark.asyncio
@pytest.mark.benchmark(group="pow")
async def test_process_block(powChain, benchmark):
    """
    Benchmark for chainStateManager.processBlock

    test for processing block
    case: block with 201 transactions, 1 minting transaction, 100 issue and 100 transfer
    """

    def generate_and_connect_block():
        block = powChain.miner._getCandidateBlock(transactions=TRANSACTIONS_200)
        powChain.chainStateManager.processBlock(block, False)

    benchmark(generate_and_connect_block)
