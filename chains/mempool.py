from __future__ import annotations

import threading
from typing import Callable, Dict, List, Optional, Set

from kivy.logger import Logger

from common import bytesToHex
from common.models import Block, Transaction


class MemoryPool:
    """
    Transaction pool that caches pending transactions in memory.

    The MemoryPool maintains transactions that have not yet been included in a block,
    handles transaction dependencies, and provides methods to retrieve transactions
    in an optimal order for block inclusion.
    """

    _LOGGER_TITLE = "Memory Pool:"

    def __init__(self, isOnChain: Optional[Callable[[bytes], bool]] = None):
        self.transactions: Dict[bytes, Transaction] = dict()
        self.lock = threading.RLock()
        self.isOnChain = isOnChain

    def addTransaction(self, transaction: Transaction):
        """
        Add a transaction to the memory pool.

        Args:
            transaction: The transaction to add to the pool

        If the transaction is already on chain (when isOnChain callback is provided),
        it will not be added to the pool.
        """
        # First check if the transaction itself is already on chain
        transactionHash = transaction.hash()
        if self.isOnChain is not None and self.isOnChain(transactionHash):
            Logger.debug(
                f"{self._LOGGER_TITLE} Transaction {bytesToHex(transactionHash)} is already on chain, not adding to mempool"
            )
            return

        with self.lock:
            self.transactions[transaction.hash()] = transaction
        Logger.trace(f"{self._LOGGER_TITLE} Transaction {bytesToHex(transactionHash)} added to the memory pool")

    def removeTransaction(self, transactionHash: bytes):
        """
        Remove a transaction from the memory pool.

        Args:
            transactionHash: The hash of the transaction to remove
        """
        with self.lock:
            result = self.transactions.pop(transactionHash, None)
            if result:
                Logger.debug(f"{self._LOGGER_TITLE} Transaction {transactionHash} removed from the memory pool")

    def getTransaction(self, transactionHash: bytes) -> Optional[Transaction]:
        """
        Retrieve a transaction from the pool by its hash.

        Args:
            transactionHash: The hash of the transaction to retrieve

        Returns:
            The transaction if found, None otherwise
        """
        return self.transactions.get(transactionHash)

    def getTransactions(self, maxTransactions: int = 2000) -> List[Transaction]:
        """
        Return transactions from the pool in an optimal order for block inclusion.

        This method performs a topological sort of transactions based on their dependencies,
        prioritizing transactions with higher fuel. It handles circular dependencies by
        breaking cycles in a way that preserves as much of the dependency ordering as possible.

        Args:
            maxTransactions: Maximum number of transactions to return (default: 2000)

        Returns:
            A list of transactions sorted by dependencies and fuel (higher fuel first),
            limited to maxTransactions

        Notes:
            - Transactions with higher fuel are prioritized
            - Dependencies are respected (if A depends on B, B will come before A)
            - Circular dependencies are detected and broken by removing the dependency
              from the transaction with the lowest fuel
        """
        with self.lock:
            # Get all transactions
            allTransactions = list(self.transactions.values())
            if not allTransactions:
                return []

            # Build transaction hash to transaction mapping
            transactionHashToTransaction = {}
            for transaction in allTransactions:
                txHash = bytesToHex(transaction.hash())
                transactionHashToTransaction[txHash] = transaction

            # Sort transactions by fuel (higher fuel first)
            fuelSortedTransactions = sorted(allTransactions, key=lambda tx: tx.fuel, reverse=True)
            fuelSortedHashes = [bytesToHex(tx.hash()) for tx in fuelSortedTransactions]

            # Build dependency graph
            dependencyGraph = {}
            for transaction in allTransactions:
                txHash = bytesToHex(transaction.hash())
                dependencyGraph[txHash] = set()

                # Only add dependency when the dependent transaction hash exists and is in the memory pool
                if (
                    transaction.dependent_transaction_hash
                    and transaction.dependent_transaction_hash != ""
                    and transaction.dependent_transaction_hash in transactionHashToTransaction
                ):
                    dependencyGraph[txHash].add(transaction.dependent_transaction_hash)

            # Perform topological sort with cycle detection
            sortedTransactions = self._topologicalSort(dependencyGraph, transactionHashToTransaction, fuelSortedHashes)

            # Optimization 4: If no need to limit transaction count, return results directly
            if len(sortedTransactions) <= maxTransactions:
                return sortedTransactions

            Logger.debug(
                f"{self._LOGGER_TITLE} Limiting transactions from {len(sortedTransactions)} to {maxTransactions}"
            )
            return self._selectTransactionsWithDependencies(
                sortedTransactions,
                maxTransactions,
                dependencyGraph,
                transactionHashToTransaction,
            )

    def _topologicalSort(
        self,
        graph: Dict[str, Set[str]],
        transactionHashToTransaction: Dict[str, Transaction],
        fuelSortedHashes: List[str],
    ) -> List[Transaction]:
        """
        Perform a topological sort on the transaction dependency graph.

        This method implements an efficient topological sorting algorithm that respects
        transaction dependencies while prioritizing transactions with higher fuel.
        It handles cycles in the dependency graph by breaking them in a way that
        preserves the maximum number of dependencies.

        Args:
            graph: A dictionary mapping transaction hashes to sets of dependencies
            transactionHashToTransaction: A dictionary mapping transaction hashes to Transaction objects
            fuelSortedHashes: A list of transaction hashes sorted by fuel (highest first)

        Returns:
            A list of Transaction objects in topological order, respecting dependencies
            and prioritizing by fuel

        Notes:
            The implementation uses an iterative approach to avoid stack overflow when
            processing large transaction graphs.
        """
        # Optimization 1: Only perform cycle detection when the graph has edges
        hasDependencies = any(graph.values())

        if hasDependencies:
            # Optimization 2: Only detect cycles when there are dependencies
            cycles = self._findCycles(graph)

            # If there are cycles, break them
            if cycles:
                Logger.debug(f"{self._LOGGER_TITLE} Found {len(cycles)} cycles in transaction dependencies")
                modifiedGraph = self._breakCycles(graph, cycles, transactionHashToTransaction)
            else:
                modifiedGraph = graph

            # Optimization 3: Use iterative approach for topological sort to avoid recursive call stack overflow risk
            visited = set()
            result = []

            # Use stack instead of recursion
            def topologicalSortIterative(startNodes):
                # Use stack to simulate recursion
                nodeStack = []
                # Track whether nodes have been fully processed
                processed = set()

                # Initialize stack, add all starting nodes
                for node in startNodes:
                    if node not in visited:
                        nodeStack.append((node, False))  # (node, dependencies processed flag)
                        visited.add(node)

                while nodeStack:
                    node, dependenciesProcessed = nodeStack.pop()

                    if dependenciesProcessed:
                        # All dependencies of the node have been processed, add the node to the result
                        result.append(transactionHashToTransaction[node])
                        processed.add(node)
                        continue

                    # Re-add the node to the stack, mark as dependencies processed
                    nodeStack.append((node, True))

                    # Process all dependencies
                    for dep in modifiedGraph.get(node, set()):
                        if dep in processed:
                            # Dependency is already in the result, skip
                            continue
                        if dep not in visited:
                            # Dependency not yet visited, add to stack
                            nodeStack.append((dep, False))
                            visited.add(dep)
                        elif dep not in processed:
                            # Dependency visited but not fully processed, indicating a cycle
                            # This shouldn't happen since we've already broken cycles
                            # But for robustness, we still handle this case
                            continue

            # Process nodes sorted by fuel
            topologicalSortIterative(fuelSortedHashes)
            return result
        else:
            # Optimization 4: Even without complex dependencies, we still need topological sorting
            # Use a simplified topological sorting algorithm
            result = []
            visited = set()

            # Perform topological sort
            def visit(node):
                if node in visited:
                    return
                visited.add(node)

                # Process all dependencies first
                for dep in graph.get(node, set()):
                    visit(dep)

                # Then add this node
                result.append(transactionHashToTransaction[node])

            # Process nodes sorted by fuel
            for txHash in fuelSortedHashes:
                if txHash not in visited:
                    visit(txHash)

            return result

    def _findCycles(self, graph: Dict[str, Set[str]]) -> List[List[str]]:
        """
        Find all cycles in the transaction dependency graph.

        This method implements an efficient cycle detection algorithm using a depth-first
        search approach. It identifies all cycles in the dependency graph, which will be
        used to break circular dependencies.

        Args:
            graph: A dictionary mapping transaction hashes to sets of dependencies

        Returns:
            A list of cycles, where each cycle is a list of transaction hashes in the
            order they appear in the cycle

        Notes:
            The implementation uses an iterative approach instead of recursion to avoid
            stack overflow when processing large graphs with many cycles.
        """
        cycles = []
        visited = set()  # Nodes that have been fully processed
        tempVisited = set()  # Nodes on the current path

        # Optimization 1: Use iterative approach for DFS to avoid recursive call stack overflow
        def findCyclesIterative(startNode):
            # Use stack to simulate recursive calls
            stack = [(startNode, 0)]  # (node, neighbor index)
            path = []  # Current path
            nodeToNeighbors = {}  # Mapping from node to its neighbor list

            while stack:
                node, neighborIndex = stack[-1]  # Look at the top element without popping

                # If it's a new node, initialize processing
                if neighborIndex == 0:
                    if node in visited:
                        stack.pop()
                        continue

                    if node in tempVisited:
                        # Found a cycle
                        cycleStart = path.index(node)
                        cycles.append(path[cycleStart:] + [node])
                        stack.pop()
                        continue

                    # Add node to current path
                    tempVisited.add(node)
                    path.append(node)
                    # Get all neighbors
                    nodeToNeighbors[node] = list(graph.get(node, set()))

                # Process next neighbor
                neighbors = nodeToNeighbors[node]
                if neighborIndex < len(neighbors):
                    # Update neighbor index of the top element
                    stack[-1] = (node, neighborIndex + 1)
                    # Push the next neighbor onto the stack
                    nextNode = neighbors[neighborIndex]
                    stack.append((nextNode, 0))
                else:
                    # All neighbors processed, pop the node
                    stack.pop()
                    path.pop()
                    tempVisited.remove(node)
                    visited.add(node)

        # Perform DFS for each unvisited node
        for node in graph:
            if node not in visited:
                findCyclesIterative(node)

        return cycles

    def _breakCycles(
        self, graph: Dict[str, Set[str]], cycles: List[List[str]], transactionHashToTransaction: Dict[str, Transaction]
    ) -> Dict[str, Set[str]]:
        """
        Break cycles in the dependency graph to enable topological sorting.

        This method resolves circular dependencies by removing specific edges from the
        dependency graph. For each cycle, it identifies the transaction with the lowest
        fuel and removes its dependency that forms part of the cycle. This approach
        preserves the maximum number of dependencies while ensuring the graph becomes acyclic.

        Args:
            graph: A dictionary mapping transaction hashes to sets of dependencies
            cycles: A list of cycles, where each cycle is a list of transaction hashes
            transactionHashToTransaction: A dictionary mapping transaction hashes to Transaction objects

        Returns:
            A modified graph with all cycles broken, suitable for topological sorting

        Notes:
            Breaking cycles at the lowest-fuel transaction minimizes the impact on high-value
            transactions while ensuring the graph can be properly sorted.
        """
        # Create a copy of the graph to modify
        modifiedGraph = {node: set(deps) for node, deps in graph.items()}

        for cycle in cycles:
            # Find the transaction with the lowest fuel in the cycle
            lowestFuelTransaction = min(
                cycle, key=lambda transactionHash: transactionHashToTransaction[transactionHash].fuel
            )

            # Find the transaction it depends on in the cycle
            for i, transactionHash in enumerate(cycle):
                if transactionHash == lowestFuelTransaction:
                    # Get the dependency that's part of the cycle
                    dependency = cycle[(i + 1) % len(cycle)]

                    # Remove this dependency to break the cycle
                    if dependency in modifiedGraph[transactionHash]:
                        modifiedGraph[transactionHash].remove(dependency)
                        Logger.debug(
                            f"{self._LOGGER_TITLE} Breaking cycle by removing dependency from {transactionHash} to {dependency}"
                        )

        return modifiedGraph

    def _selectTransactionsWithDependencies(
        self,
        sortedTransactions: List[Transaction],
        maxTransactions: int,
        dependencyGraph: Dict[str, Set[str]],
        transactionHashToTransaction: Dict[str, Transaction],
    ) -> List[Transaction]:
        """
        Select transactions up to the maximum limit while preserving all dependencies.

        When the number of transactions exceeds the maximum limit, this method selects
        a subset of transactions that maximizes the total fuel while ensuring that all
        dependencies of included transactions are also included. This maintains the
        integrity of transaction dependencies even when limiting the number of transactions.

        Args:
            sortedTransactions: List of transactions already sorted by dependencies and fuel
            maxTransactions: Maximum number of transactions to return
            dependencyGraph: Existing dependency graph mapping transaction hashes to dependencies
            transactionHashToTransaction: Existing mapping of transaction hashes to transactions

        Returns:
            A list of up to maxTransactions, with all dependencies included

        Notes:
            The algorithm prioritizes high-fuel transactions but will skip a transaction
            if including it and all its dependencies would exceed the maximum limit.
        """
        selectedTransactionHashes = set()
        result = []

        # Sort transactions by fuel (higher fuel first)
        fuelSortedTransactions = sorted(sortedTransactions, key=lambda tx: tx.fuel, reverse=True)

        # Cache for dependencies to avoid recalculating
        allDependenciesCache = {}

        for transaction in fuelSortedTransactions:
            if len(result) >= maxTransactions:
                break

            transactionHash = bytesToHex(transaction.hash())
            if transactionHash in selectedTransactionHashes:
                continue

            # Get all dependencies
            if transactionHash not in allDependenciesCache:
                allDependenciesCache[transactionHash] = self._getAllDependencies(transactionHash, dependencyGraph)
            allDependencies = allDependenciesCache[transactionHash]

            # Calculate new dependencies (not yet selected)
            newDependencies = [dep for dep in allDependencies if dep not in selectedTransactionHashes]

            # Skip if adding this transaction and all its dependencies would exceed the maximum
            if len(result) + len(newDependencies) + 1 > maxTransactions:
                continue

            # Add all dependencies
            for depHash in newDependencies:
                selectedTransactionHashes.add(depHash)
                result.append(transactionHashToTransaction[depHash])

            # Add the transaction itself
            selectedTransactionHashes.add(transactionHash)
            result.append(transaction)

        return result

    def _getAllDependencies(self, transactionHash: str, dependencyGraph: Dict[str, Set[str]]) -> List[str]:
        """
        Get all direct and indirect dependencies of a transaction recursively.

        This method traverses the dependency graph to find all transactions that must be
        included before the specified transaction. It handles both direct dependencies
        (transactions directly depended on) and indirect dependencies (dependencies of
        dependencies).

        Args:
            transactionHash: Hash of the transaction to find dependencies for
            dependencyGraph: Dependency graph mapping transaction hashes to their dependencies

        Returns:
            A list of all dependency transaction hashes that must be included before
            the specified transaction

        Notes:
            The implementation uses an iterative approach instead of recursion to avoid
            stack overflow when processing transactions with deep dependency chains.
        """
        # Optimization 1: Use a set to store results, avoiding duplicate checks
        resultSet = set()

        # Optimization 2: Use iterative approach instead of recursion to avoid stack overflow
        def getAllDependenciesIterative(startHash):
            if not dependencyGraph.get(startHash, set()):
                return  # No dependencies, return directly

            # Use stack to simulate recursion
            stack = [startHash]
            visited = {startHash}  # Track visited nodes to avoid infinite loops from circular dependencies

            while stack:
                currentHash = stack.pop()

                # Process all dependencies of the current node
                for depHash in dependencyGraph.get(currentHash, set()):
                    if depHash not in visited:
                        visited.add(depHash)
                        stack.append(depHash)
                        resultSet.add(depHash)

        getAllDependenciesIterative(transactionHash)
        return list(resultSet)

    def hasTransaction(self, transactionHash: bytes) -> bool:
        """
        Check if a transaction exists in the memory pool.

        Args:
            transactionHash: The hash of the transaction to check

        Returns:
            True if the transaction is in the pool, False otherwise
        """
        return transactionHash in self.transactions

    def onConnectBlock(self, block: Block):
        """
        Remove all transactions in a block from the memory pool.

        This method is called when a new block is added to the blockchain,
        to ensure that transactions already included in the blockchain
        are removed from the memory pool.

        Args:
            block: The block containing transactions to remove from the pool
        """
        transactions = block.transactions
        with self.lock:
            for transaction in transactions:
                self.removeTransaction(transaction.hash())

    def getStatusString(self) -> str:
        """
        Get a string representation of the memory pool's current status.

        Returns:
            A string describing the current state of the memory pool,
            including the number of transactions it contains
        """
        return f"{self._LOGGER_TITLE} {len(self.transactions)} transaction(s)"
