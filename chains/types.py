from dataclasses import dataclass
from enum import Enum
from typing import List, Optional

from common.models import Receipt
from state import StateDB


class ProcessBlockResult:
    """
    ProcessBlockResult is used to return the result of processing a block.
    It contains the process state of the block and the error if there's any.
    """

    class ProcessBlockState(Enum):
        UNVALIDATED = 1  # initial state
        VALIDATED = 2  # block is validated
        BLOCK_SAVED = 3  # block is saved to the disk, but not connected to the blocktree
        BLOCK_CONNECTED = 4  # block connected to the blocktree

    class ProcessBlockError(Enum):
        NONE = 0
        RUNTIME_ERROR = 1  # wasm runtime error
        INVALID_BLOCK = 2  # this block is invalid
        MINTING_TX_FAILED = 3  # minting transaction failed
        NEED_REORG = 4  # need reorg
        BLOCK_EXISTS = 5  # block already exists
        ORPHAN_BLOCK = 6  # this block is an orphan block
        LOWER_OR_EQUAL_HEIGHT = 7  # this block is lower or equal to the current best block

    def __init__(self, state: ProcessBlockState, error: ProcessBlockError):
        self.state = state
        self.error = error

    @classmethod
    def unvalidated(cls, error: ProcessBlockError = ProcessBlockError.NONE):
        return cls(cls.ProcessBlockState.UNVALIDATED, error)

    @classmethod
    def validated(cls, error: ProcessBlockError = ProcessBlockError.NONE):
        return cls(cls.ProcessBlockState.VALIDATED, error)

    @classmethod
    def blockSaved(cls, error: ProcessBlockError = ProcessBlockError.NONE):
        return cls(cls.ProcessBlockState.BLOCK_SAVED, error)

    @classmethod
    def blockConnected(cls, error: ProcessBlockError = ProcessBlockError.NONE):
        return cls(cls.ProcessBlockState.BLOCK_CONNECTED, error)

    def isBlockSaved(self):
        return self.state in [self.ProcessBlockState.BLOCK_SAVED, self.ProcessBlockState.BLOCK_CONNECTED]

    def isBlockConnected(self):
        return self.state == self.ProcessBlockState.BLOCK_CONNECTED

    def isError(self):
        return self.error != self.ProcessBlockError.NONE

    def isNeedReorg(self):
        return self.error == self.ProcessBlockError.NEED_REORG

    def isExists(self):
        return self.error == self.ProcessBlockError.BLOCK_EXISTS

    def isOrphan(self):
        return self.error == self.ProcessBlockError.ORPHAN_BLOCK

    def isLowerOrEqualHeight(self):
        return self.error == self.ProcessBlockError.LOWER_OR_EQUAL_HEIGHT


@dataclass
class ApplyBlockResult:
    state: Optional[StateDB]
    receipts: Optional[List[Receipt]]
    error: Optional[str]
