from __future__ import annotations

import asyncio
import math
import time
from typing import List, Optional

from kivy.logger import Logger

from chains.mempool import MemoryPool
from chains.signature_manager import SignatureManager
from chains.statedb_manager import StatedbManager
from chains.vgraph_spos_chain.chain_state_manager import VGraphSPOSChainStateManager
from chains.vgraph_spos_chain.client import VGraphSPOSClient
from chains.vgraph_spos_chain.clock import Clock, ClockUtils
from common import bytesToHex, deriveSha, hexToBytes, publicKeyToAddress
from common.models import (
    Block,
    BloomFilter,
    OperationCallContract,
    OperationType,
    Receipts,
    SPOSHeader,
    Transaction,
    createBloom,
)
from common.models.transaction import Transactions
from common.utils import getRequiredKey
from tree import MemoryTrie
from vm import ContractExecutor


class VGraphSPOSChainMinter:
    """
    Minter for VGraphSPOSChain

    Main functions:
    - generateBlock: generate block periodically
    - createNewBlock: create a new block
    """

    _LOGGER_TITLE = "VGraph<PERSON><PERSON><PERSON><PERSON><PERSON>:"

    def __init__(
        self,
        consensusSettings: dict,
        clock: Clock,
        memoryPool: MemoryPool,
        statedbManager: StatedbManager,
        chainStateManager: VGraphSPOSChainStateManager,
        signatureManager: SignatureManager,
        client: VGraphSPOSClient,
    ):
        self.statedbManager = statedbManager

        self.slotId = None
        self.consensusSetting = consensusSettings

        self.clock = clock
        self.memoryPool = memoryPool
        self.chainStateManager = chainStateManager
        self.signatureManager = signatureManager
        self.client = client

        self.slotNums = getRequiredKey(self.consensusSetting, "slotNums")
        self.blockInterval = getRequiredKey(self.consensusSetting, "blockInterval")
        self.address = self.signatureManager.address

        self._initContractClients()
        self.checkAndUpdateSlotID()
        Logger.info(f"{self._LOGGER_TITLE} Minter initialized, slot id: {self.slotId}")

        self.mintNTPTime = 0
        self.mintLocalTime = 0
        asyncio.create_task(self.generateBlock())

    def _initContractClients(self) -> None:
        """
        Initialize contract clients for block operations
        """
        self.sposContractClient = ContractExecutor(
            address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
        )

    def checkAndUpdateSlotID(self):
        slotId, err = self.sposContractClient.executeReadOnly(
            self.statedbManager.getState(), "check_owners_supernode_slot", int, self.address
        )
        if err:
            Logger.error(f"{self._LOGGER_TITLE} check owners' supernode slot failed: {err}")
            raise Exception(err)

        if slotId > self.slotNums:
            Logger.error(
                f"{self._LOGGER_TITLE} Runtime error, get slot id {slotId}, which exceeds slot nums {self.slotNums}"
            )
            raise Exception("Slot id exceeds slot nums")

        self.slotId = slotId
        Logger.debug(f"{self._LOGGER_TITLE} Update owners' supernode slot success, slot id: {slotId}")

    # ----- mint -----

    async def generateBlock(self):
        while True:
            if self.slotId > 0:  # self.slotID > 0 <-> this node is supernode
                currentTime = ClockUtils.nanosecondsToSeconds(self.clock.getCorrectedTime())
                secondsInCurrentInterval = int(currentTime % self.blockInterval)
                if secondsInCurrentInterval == 0:
                    # Determine the current slot to generate
                    currentSlotToGenerate = (
                        int(currentTime % (self.slotNums * self.blockInterval) / self.blockInterval) + 1
                    )

                    # Determine if this node should generate a block
                    if currentSlotToGenerate == self.slotId:
                        Logger.info(f"{self._LOGGER_TITLE} Trying to generate block...")
                        # get current time(in seconds)
                        Logger.info(f"{self._LOGGER_TITLE} Corrected time: {currentTime} (in Seconds)")
                        self.mintNTPTime = self.clock.getCorrectedTime()
                        self.mintLocalTime = time.time_ns()

                        await self.createNewBlock()
                    else:
                        Logger.info(
                            f"{self._LOGGER_TITLE} Waiting for slot {currentSlotToGenerate} to generate the block."
                        )

            await asyncio.sleep(1)

    async def createNewBlock(self) -> Optional[Block]:
        """
        Create new block. If success, return the block, else return None

        Logic:
        1. check if this node is supernode
        2. get candidate block
        3. process block
        """

        # check if this node is supernode
        if self.slotId <= 0:
            Logger.warning(f"{self._LOGGER_TITLE} This node is not supernode, stop creating block.")
            return None

        # get transactions from mempool
        transactions = self.memoryPool.getTransactions()

        # get candidate block
        candidateBlock = self._getCandidateBlock(transactions=transactions)
        if candidateBlock is None:
            return None

        # broadcast block to supernode, ask for signature
        supernodeList, err = self.sposContractClient.executeReadOnly(
            self.statedbManager.getState(), "get_all_supernodes", List[str]
        )
        if err:
            Logger.error(f"{self._LOGGER_TITLE} Get all supernodes failed: {err}")
            return None

        results = await self.client.requestSignatures(candidateBlock, supernodeList)
        # filter out invalid results
        for result in results:
            publicKey = result.get("publickey", None)
            signature = result.get("signature", None)
            if publicKey is None or signature is None:
                continue
            if publicKeyToAddress(publicKey) not in supernodeList:
                continue

            candidateBlock.header.public_keys.append(publicKey)
            candidateBlock.header.signatures.append(signature)

        have = len(candidateBlock.header.public_keys)
        need = math.ceil(len(supernodeList) * 2 / 3)
        if have < need:
            Logger.error(
                f"{self._LOGGER_TITLE} Not enough signatures, block creation failed. Have: {have}, Need: {need}"
            )
            return None

        # process block
        result = self.chainStateManager.processBlock(candidateBlock, isGenesis=False)
        if result.isBlockConnected():
            return candidateBlock
        else:
            Logger.error(f"{self._LOGGER_TITLE} New block creation failed: {result.error}")
            return None

    def _getCandidateBlock(self, transactions: List[Transaction]) -> Optional[Block]:
        """
        return a candidate block

        the parameters are the transactions in the mempool

        Logic:
        1. get block template
        2. pack block
        """

        # get block template
        candidateBlockHeader = SPOSHeader(
            parent_hash=hexToBytes("00000000000000000000000000000000"),
            height=0,
            state_root="",
            transactions_root="",
            receipts_root="",
            bloom=BloomFilter(),
            local_timestamp=0,
            protocol_timestamp=0,
            slot_id=0,
            proposer_address=b"",
            public_keys=[],
            signatures=[],
        )
        currentBestBlock = self.chainStateManager.currentHeader()
        if currentBestBlock is not None:
            candidateBlockHeader.parent_hash = currentBestBlock.hash()
            candidateBlockHeader.height = currentBestBlock.height + 1

        # pack block
        # add minting transaction
        mintingTransaction = Transaction(
            dependent_transaction_hash="",
            sender=self.address,
            op_data=OperationCallContract(
                op_type=OperationType.CALL_CONTRACT,
                contract_address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
                function_name="mint_token",
                parameters=[self.address, 100_000_000_000],
            ),
            public_keys=[],
            signatures=[],
            timestamp=self.mintLocalTime,  # maybe use ntp timestamp
            fuel=1000000,
        )
        # sign
        mintingTransaction = self.signatureManager.signTransaction(mintingTransaction)

        finalTransactions: List[Transaction] = [mintingTransaction] + transactions

        # calculate transactions root using memory trie
        candidateBlockHeader.transactions_root = bytesToHex(deriveSha(Transactions(finalTransactions), MemoryTrie()))

        # apply block in sandbox
        result = self.chainStateManager.applyBlockInSandbox(
            Block(candidateBlockHeader, finalTransactions), isGenesis=False
        )
        if result.error is not None:
            Logger.error(f"{self._LOGGER_TITLE} Create candidate block failed due to apply block in sandbox failed")
            return None
        sandboxState, receipts = result.state, result.receipts
        # pack state root
        candidateBlockHeader.state_root = bytesToHex(sandboxState.intermediateRoot())
        # pack receipts root
        candidateBlockHeader.receipts_root = bytesToHex(deriveSha(Receipts(receipts), MemoryTrie()))
        # pack bloom filter
        candidateBlockHeader.bloom = createBloom(receipts)

        # local timestamp in nanoseconds
        candidateBlockHeader.local_timestamp = self.mintLocalTime
        # ntp timestamp in nano seconds
        candidateBlockHeader.protocol_timestamp = self.mintNTPTime
        candidateBlockHeader.slot_id = self.slotId  # slot id
        candidateBlockHeader.proposer_address = hexToBytes(self.address)  # proposer address

        # sign block
        candidateBlockHeader = self.signatureManager.signBlockHeader(candidateBlockHeader)

        Logger.debug(
            f"{self._LOGGER_TITLE} Get candidate block: {candidateBlockHeader.hashHex()}, parent: {bytesToHex(candidateBlockHeader.parent_hash)}"
        )

        return Block(candidateBlockHeader, finalTransactions)

    # ----- misc -----

    def onConnectBlock(self, block: Block):
        self.checkAndUpdateSlotID()

    def getStatusString(self) -> str:
        """
        return the status of the minter
        """
        return f"""{self._LOGGER_TITLE}
    Slot ID: {self.slotId}"""
