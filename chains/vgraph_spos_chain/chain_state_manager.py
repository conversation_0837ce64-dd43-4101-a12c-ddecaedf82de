from __future__ import annotations

import asyncio
import threading
from typing import Callable, Dict, List, Optional

from kivy.logger import Logger
from readerwriterlock.rwlock import RWLockFair

import rawdb
import vgraphdb
from cache_manager import CacheManager
from cache_manager.core.cache import AbstractCache

# Import our cache management system
from chains.header_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from chains.mempool import MemoryPool
from chains.orphan_block_pool import OrphanBlockPool
from chains.statedb_manager import StatedbManager
from chains.transaction_processor import TransactionProcessor
from chains.types import ApplyBlockResult, ProcessBlockResult
from chains.vgraph_spos_chain.client import VGraphSPOSClient
from common import (
    Atomic,
    bytesToHex,
    deriveSha,
    getRequiredKey,
    hexToBytes,
    to<PERSON>son,
    verifyBlockHeaderSignature,
    verifyTransactionSignatures,
)
from common.models import (
    AbstractHeader,
    Block,
    BloomFilter,
    Body,
    OperationCallContract,
    OperationType,
    Receipt,
    Receipts,
    SPOSHeader,
    Transaction,
    TransactionLookup,
    Transactions,
    createBloom,
    hashDifference,
)
from state import index_undo_log
from tree import MemoryTrie
from vm import ContractExecutor

ApplyBlockCacheLimit = 32
BodyCacheLimit = 256
BlockCacheLimit = 256
ReceiptsCacheLimit = 32
TransactionLookupCacheLimit = 1024


class VGraphSPOSChainStateManager:
    """
    VGraphSPOSChainStateManager is the chain state manager of VGraphSPOSChain,
    it manages the chain state, including blocks, transactions, transaction receipts, etc.

    Main functions:
    - processBlock: process a block, including validate, save, connect, etc.
    - processSyncBlocks: process sync blocks
    - reorganize: reorganize the chain
    """

    _LOGGER_TITLE = "VGraphSPOSChain State Manager:"

    def __init__(
        self,
        consensusSettings: Dict,
        db: vgraphdb.KeyValueStore,
        headerChain: HeaderChain,
        statedbManager: StatedbManager,
        memoryPool: MemoryPool,
        client: VGraphSPOSClient,
    ) -> None:
        self.memoryPool = memoryPool
        self.client = client

        # consensus settings
        self.consensusSettings = consensusSettings
        self.slotNums = getRequiredKey(self.consensusSettings, "slotNums")

        self.db = db
        self.headerChain = headerChain

        self._currentBlock = Atomic(None)

        self.blockCache: AbstractCache[bytes, Block] = CacheManager.createCache(
            name="spos_block_cache", cacheType="lru", maxSize=BlockCacheLimit
        )
        self.bodyCache: AbstractCache[bytes, Body] = CacheManager.createCache(
            name="spos_body_cache", cacheType="lru", maxSize=BodyCacheLimit
        )
        self.bodyRLPCache: AbstractCache[bytes, bytes] = CacheManager.createCache(
            name="spos_body_rlp_cache", cacheType="lru", maxSize=BodyCacheLimit
        )
        self.receiptsCache: AbstractCache[bytes, Receipts] = CacheManager.createCache(
            name="spos_receipts_cache", cacheType="lru", maxSize=ReceiptsCacheLimit
        )
        # state root before apply block -> ApplyBlockResult
        self.applyBlockCache: AbstractCache[bytes, ApplyBlockResult] = CacheManager.createCache(
            name="spos_apply_block_cache", cacheType="lru", maxSize=ApplyBlockCacheLimit
        )

        self.transactionLookupLock = RWLockFair()
        self.transactionLookupCache: AbstractCache[bytes, TransactionLookup] = CacheManager.createCache(
            name="spos_transaction_lookup_cache", cacheType="lru", maxSize=TransactionLookupCacheLimit
        )

        self.genesisBlock: Optional[SPOSHeader] = None

        # TODO: clean stale orphan blocks in the memory occasionally
        self.orphanBlocks = OrphanBlockPool()

        self.statedbManager = statedbManager
        self.transactionProcessor = TransactionProcessor()

        # locks
        # reentrant lock, use lock to protect block state
        self.blockStateLock = threading.RLock()

        # observer for observing connection block events
        self.connectBlockObservers = []

        # init contract clients
        self._initContractClients()

    # ----- init -----

    def _initContractClients(self) -> None:
        """
        Initialize contract clients for block operations
        """
        self.sposContractClient = ContractExecutor(
            address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
        )

    def initChainState(self, onInit: Callable[[], None]) -> None:
        """
        Initialize the chain state, including:
        1. connect genesis block if the chain is empty
        """
        # connect genesis block if the chain is empty
        if (genesisHash := rawdb.readCanonicalHash(self.db, 0)) is None:
            # genesis block not exists
            genesisBlock = self._getGenesisBlock()
            result = self.processBlock(genesisBlock, isGenesis=True)
            if result.isError():
                Logger.error(f"{self._LOGGER_TITLE} initialize chain state fails: {result.error}")
                raise Exception("process genesis block fails.")
            Logger.info(f"{self._LOGGER_TITLE} Chain process genesis block successfully: \n{self.getStatusString()}")
            Logger.info(f"{self._LOGGER_TITLE} Chain state initialized")
        else:
            # restore chain state from the database
            head = rawdb.readHeadBlockHash(self.db)
            if head is None:
                # TODO: auto reset when database file broken
                Logger.error(f"{self._LOGGER_TITLE} Empty database, resetting chain")
                raise Exception("Empty database, resetting chain, delete db file and restart")
            # make sure the entire head block is available
            headBlock = self.getBlockByHash(head)
            if headBlock is None:
                # TODO: auto reset when database file broken
                Logger.error(f"{self._LOGGER_TITLE} Head block not found in the database, resetting chain")
                raise Exception("Head block not found in the database, delete db file and restart")
            # everything seems to be fine, set as the head block
            self._currentBlock.set(headBlock)

            # restore the last known head header
            headHeader = headBlock.header
            if (head := rawdb.readHeadHeaderHash(self.db)) is not None:
                if (header := self.getHeaderByHash(head)) is not None:
                    headHeader = header
            self.headerChain.setCurrentHeader(headHeader)

            # restore the genesis block
            if (genesisBlock := self.getBlockByHash(genesisHash)) is not None:
                self.genesisBlock = genesisBlock
                self.headerChain.setGenesis(genesisBlock.header)
                Logger.info(f"{self._LOGGER_TITLE} Chain state restored from the database")
            else:
                Logger.error(f"{self._LOGGER_TITLE} Genesis block not found in the database")
                raise Exception("Genesis block not found in the database, delete db file and restart")
        # call onInit after chain state initialized
        onInit()

    # ----- blocks -----

    def _getGenesisBlock(self) -> Block:
        """
        return the genesis block of the chain
        """

        header = SPOSHeader(
            parent_hash=hexToBytes("0x00000000000000000000000000000000"),
            height=0,
            state_root="",
            transactions_root="",
            receipts_root="",
            bloom=BloomFilter(),
            local_timestamp=0,
            protocol_timestamp=0,
            slot_id=1,
            proposer_address=hexToBytes("0x00000000000000000000000000000000"),
            public_keys=[],
            signatures=[],
        )

        # pack transactions
        transactions: List[Transaction] = []

        # pack transactions
        # set supernode
        transaction = Transaction(
            dependent_transaction_hash="",
            sender="0x00000000000000000000000000000000",
            op_data=OperationCallContract(
                op_type=OperationType.CALL_CONTRACT,
                contract_address="0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9",
                function_name="content_slot",
                parameters=[
                    1,
                    "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06",
                ],
            ),
            public_keys=[],
            signatures=[],
            timestamp=0,
            fuel=1000000,
        )

        transactions.append(transaction)

        # calculate transactions_root using memory trie
        header.transactions_root = bytesToHex(deriveSha(Transactions(transactions), MemoryTrie()))

        # apply block in sandbox
        result = self.applyBlockInSandbox(Block(header, transactions), isGenesis=True)
        if result.error is not None:
            Logger.error(f"{self._LOGGER_TITLE} Apply genesis block in sandbox failed: {result.error}")
            raise Exception("Apply genesis block in sandbox failed")
        sandboxState, receipts = result.state, result.receipts
        # pack state root
        header.state_root = bytesToHex(sandboxState.intermediateRoot())
        # pack receipts root
        header.receipts_root = bytesToHex(deriveSha(Receipts(receipts), MemoryTrie()))
        # pack bloom filter
        header.bloom = createBloom(receipts)

        return Block(header, transactions)

    def clearApplyBlockCache(self):
        """
        Clear the apply block cache.
        Should be called whenever the state changes in a way that could invalidate the cache.
        """
        self.applyBlockCache.clear()
        Logger.debug(f"{self._LOGGER_TITLE} Apply block cache cleared")

    def applyBlockInSandbox(self, block: Block, isGenesis: bool = False) -> ApplyBlockResult:
        """
        Apply a block in a temporary state (sandbox) without affecting the actual blockchain state.

        Assume that the block is not orphan and is prevalidated.

        This function processes all transactions in the block against a copy parent block's state.
        If the state with the same root has already been processed for this block,
        it returns the cached result to avoid redundant computation.

        Args:
            block: The block to apply
            isGenesis: Whether this is a genesis block (special handling)

        Returns:
            ApplyBlockResult containing:
            - The resulting state after applying all transactions
            - List of transaction receipts
            - Error message (if any)

        Note:
            - For genesis blocks, all transactions are applied directly
            - For regular blocks, the first transaction must be a minting transaction
            - Results are cached based on state root, with cache size limits applied
        """
        if block is None:
            Logger.error(f"{self._LOGGER_TITLE} Cannot apply None block in sandbox")
            return ApplyBlockResult(None, None, "Block is None")

        # Get the state to work with
        if isGenesis:
            state = self.statedbManager.getState()
        else:
            parent = self.getBlock(block.parentHash(), block.height() - 1)
            if parent is None:
                Logger.debug(f"{self._LOGGER_TITLE} Parent block not found")
                return ApplyBlockResult(None, None, "Parent block not found")
            state = self.statedbManager.getStateByStateRootHash(hexToBytes(parent.stateRoot()))
        stateRoot = state.intermediateRoot()

        # Check if we've already processed this state root (caching mechanism)
        cachedResult = self.applyBlockCache.get(stateRoot)
        if cachedResult is not None:
            Logger.debug(f"{self._LOGGER_TITLE} Apply block in sandbox: {block.hashHex()} (cached)")
            return cachedResult

        # Create a copy of the state to work with (sandbox)
        sandboxState = state.copy()

        # Convert block to environment for transaction processing
        blockEnv = block.toEnvironment()

        if isGenesis:
            # For genesis blocks, apply all transactions directly
            receipts = self.transactionProcessor.applyTransactions(sandboxState, blockEnv, block.transactions)
        else:
            # For regular blocks, first transaction must be a minting transaction
            mintingTransaction: Transaction = block.transactions[0]
            if not mintingTransaction.isMintingTransaction():
                Logger.error(f"{self._LOGGER_TITLE} First transaction is not minting transaction")
                return ApplyBlockResult(None, None, "First transaction is not minting transaction")

            try:
                # super node don't have token at block_height 1
                blockHeight = int(blockEnv.get("block_height", 0))
                useFuel = blockHeight > 1

                # Apply the minting transaction first
                mintingReceipt = self.transactionProcessor.applyTransaction(
                    sandboxState, mintingTransaction, blockEnv, transactionIndex=0, useFuel=useFuel, allowError=False
                )
            except Exception as e:
                Logger.error(f"{self._LOGGER_TITLE} Apply block failed: {e}")
                return ApplyBlockResult(None, None, "Apply block failed")

            # Apply the remaining transactions with the minting receipt included
            receipts = self.transactionProcessor.applyTransactions(
                sandboxState, blockEnv, block.transactions[1:], [mintingReceipt]
            )

        # Create and cache the result
        result = ApplyBlockResult(sandboxState, receipts, None)
        self.applyBlockCache.put(stateRoot, result)
        return result

    def preValidateBlock(self, block: Block) -> bool:
        """
        Do a quick validation for block. return True if the block is valid, otherwise return False

        The validation is primitive, only check the block header.
        The following validations are not included(they will be checked in more detailed validation):
        - slot owner(due to the need of state)
        - state root and receipt root(due to the need of apply block in sandbox)

        Note that this validation does not require parent block exists.
        """
        # Basic null checks
        if block is None:
            Logger.debug(f"{self._LOGGER_TITLE} Block is None")
            return False

        # ----- block -----
        # validation:
        #   - block id
        #   - block height
        #   - slot id
        #   - block's signature
        header: SPOSHeader = block.header
        if header is None:
            Logger.debug(f"{self._LOGGER_TITLE} Block header is None")
            return False

        if header.height <= 0:
            Logger.debug(f"{self._LOGGER_TITLE} Block height {header.height} is invalid")
            return False
        if header.slot_id <= 0 or header.slot_id > self.slotNums:
            Logger.debug(f"{self._LOGGER_TITLE} Slot id {header.slot_id} is invalid")
            return False
        if not verifyBlockHeaderSignature(header):
            Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} signature verification failed")
            return False

        # ----- parent block -----
        # validation:
        #   - block height = parent block height + 1
        parentBlock = self.getBlock(block.parentHash(), block.height() - 1)
        if parentBlock is not None:
            if block.height() != parentBlock.height() + 1:
                Logger.debug(
                    f"{self._LOGGER_TITLE} Block height {block.height} is not equal to parent block height {parentBlock.height} + 1"
                )
                return False

        # ----- transactions -----
        # validation:
        #   - transactions must not be empty
        #   - first transaction must be minting transaction, the rest must not be
        #   - moreover, check if the minting transaction is valid(validator_address is from_address)
        #   - each transaction signature must be valid (batch check)
        if len(block.transactions) == 0:
            Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} has no transactions")
            return False

        firstTransaction = block.transactions[0]
        if not firstTransaction.isMintingTransaction():
            Logger.debug(f"{self._LOGGER_TITLE} First transaction is not minting transaction")
            return False
        if hexToBytes(firstTransaction.sender) != header.proposer_address:
            Logger.debug(
                f"{self._LOGGER_TITLE} Minting transaction from address {firstTransaction.sender} is not equal to proposer address {bytesToHex(header.proposer_address)}"
            )
            return False

        for transaction in block.transactions[1:]:
            if transaction.isMintingTransaction():
                Logger.debug(
                    f"{self._LOGGER_TITLE} Transaction {bytesToHex(transaction.hash())} is minting transaction"
                )
                return False

        if not verifyTransactionSignatures(block.transactions):
            Logger.debug(f"{self._LOGGER_TITLE} Transaction signatures verification failed")
            return False

        # ----- merkle root -----
        # validation:
        #   - check if the transactions_root is correct
        calculatedRoot = bytesToHex(deriveSha(Transactions(block.transactions), MemoryTrie()))
        if block.transactionsRoot() != calculatedRoot:
            Logger.debug(
                f"{self._LOGGER_TITLE} Block {block.hashHex()} transactions root mismatch. Expected: {calculatedRoot}, Got: {block.transactionsRoot()}"
            )
            return False

        return True

    def checkSlotOwner(self, block: Block) -> bool:
        """
        Check if the proposer address owns the slot at the state of parent block.
        Assume that the block is not orphan and is prevalidated.

        Args:
            block (Block): The block to validate

        Returns:
            bool: True if the proposer owns the slot, False otherwise

        Raises:
            ValueError: If block height is invalid
        """
        parent = self.getBlock(block.parentHash(), block.height() - 1)
        if parent is None:
            Logger.debug(f"{self._LOGGER_TITLE} Parent block not found")
            return False

        header: SPOSHeader = block.header
        # get the state of parent block
        parentState = self.statedbManager.getStateByStateRootHash(hexToBytes(parent.stateRoot()))
        # get the slot owner
        slotOwner, err = self.sposContractClient.executeReadOnly(parentState, "get_slot_owner", str, header.slot_id)
        if err:
            Logger.warning(f"{self._LOGGER_TITLE} Get slot owner error for slot {header.slot_id}: {err}")
            return False
        if slotOwner != bytesToHex(header.proposer_address):
            Logger.debug(
                f"{self._LOGGER_TITLE} Slot owner {slotOwner} is not equal to proposer address {bytesToHex(header.proposer_address)}"
            )
            return False

        return True

    def checkStateRootAndReceiptRoot(self, block: Block) -> bool:
        """
        Check if the state root, receipt root and bloom are correct. Will apply the block in sandbox and check the result.

        Assume that the block is not orphan and is prevalidated.
        """
        result = self.applyBlockInSandbox(block)
        if result.error is not None:
            Logger.debug(f"{self._LOGGER_TITLE} Apply block in sandbox failed: {result.error}")
            return False

        # check if the state root and receipt root are correct
        calculatedStateRoot = bytesToHex(result.state.intermediateRoot())
        if block.stateRoot() != calculatedStateRoot:
            Logger.debug(
                f"{self._LOGGER_TITLE} State root mismatch. Expected: {block.stateRoot()}, Got: {calculatedStateRoot}"
            )
            return False

        calculatedReceiptsRoot = bytesToHex(deriveSha(Receipts(result.receipts), MemoryTrie()))
        if block.receiptsRoot() != calculatedReceiptsRoot:
            Logger.debug(
                f"{self._LOGGER_TITLE} Receipts root mismatch. Expected: {block.receiptsRoot()}, Got: {calculatedReceiptsRoot}"
            )
            return False

        calculatedBloom = createBloom(result.receipts).toHex()
        if block.bloom().toHex() != calculatedBloom:
            Logger.debug(
                f"{self._LOGGER_TITLE} Bloom mismatch. Expected: {block.bloom().toHex()}, Got: {calculatedBloom}"
            )
            return False

        return True

    def processBlock(self, block: Block, isGenesis: bool) -> ProcessBlockResult:
        """
        Process a block, whether it's a new block or a sync block.

        This function handles the complete block processing workflow:
        1. Checks if the block is an orphan (parent not found)
        2. Validates if the block already exists in the database
        3. Performs pre-validation checks on non-genesis blocks
        4. Saves valid blocks to the database
        5. Performs full validation (slot owner, state root, receipt root)
        6. Connects the block to the chain if it's a valid extension
        7. Handles orphan blocks and potential chain reorganizations

        Args:
            block: The block to process
            isGenesis: Flag indicating if this is the genesis block

        Returns:
            ProcessBlockResult: Contains the processing state and any error information
            - UNVALIDATED: Initial state with potential runtime errors
            - VALIDATED: Block passed validation but encountered issues
            - BLOCK_SAVED: Block saved to disk but not connected to chain
            - BLOCK_CONNECTED: Block successfully connected to the chain
        """
        Logger.debug(f"{self._LOGGER_TITLE} Processing block {block.hashHex()}...")

        with self.blockStateLock:
            # get current best block header
            currentHeader = self.headerChain.currentHeader.get()
            if currentHeader is None and not isGenesis:
                return ProcessBlockResult.unvalidated(ProcessBlockResult.ProcessBlockError.RUNTIME_ERROR)

            # check if it's an orphan block first(no need to check for genesis block)
            if not isGenesis:
                parentBlock = self.getBlock(block.parentHash(), block.height() - 1)
                # if parent block not exists, it's an orphan block
                isOrphan = parentBlock is None
                isHigher = block.height() > currentHeader.height
                # if block is higher than current best block, maybe we need to reorg, cache it in memory
                # otherwise, just drop it
                if isOrphan:
                    if isHigher:
                        self.orphanBlocks.addOrphanBlock(block)
                        return ProcessBlockResult.validated(ProcessBlockResult.ProcessBlockError.NEED_REORG)
                    else:
                        return ProcessBlockResult.validated(ProcessBlockResult.ProcessBlockError.ORPHAN_BLOCK)

            # ----- At the following step, the block is not an orphan block -----
            # check if the block is already saved
            isSaved = self.hasBlock(block.hash(), block.height())
            if not isSaved:
                # if not exists, do a quick validation first(except genesis block)
                if not isGenesis and not self.preValidateBlock(block):
                    return ProcessBlockResult.validated(ProcessBlockResult.ProcessBlockError.INVALID_BLOCK)
                # if valid, save it to db
                rawdb.writeBlock(self.db, block)
                Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} saved to db")
            else:
                Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} already exists, skip validation and saving")

            # now the block is saved, do a final check(slot owner, state root, receipt root)
            if not isGenesis:
                if not self.checkSlotOwner(block):
                    return ProcessBlockResult.validated(ProcessBlockResult.ProcessBlockError.INVALID_BLOCK)
                if not self.checkStateRootAndReceiptRoot(block):
                    # If validation of state root or receipt root fails, clear the cache
                    # as this might indicate a state inconsistency
                    self.clearApplyBlockCache()
                    return ProcessBlockResult.validated(ProcessBlockResult.ProcessBlockError.INVALID_BLOCK)

            # check if it can be the best block
            # this block can be the best block if:
            # there's no best block, or
            # the block is the child of the best block
            if currentHeader is None or currentHeader.hash() == block.parentHash():
                err = self.connectBlock(block, isGenesis)
                if err:
                    # block saved, but connect block failed
                    return ProcessBlockResult.blockSaved(ProcessBlockResult.ProcessBlockError.RUNTIME_ERROR)

                # block is connected
                # if block is connected, process orphan blocks
                self.processOrphanBlocks(block.hash())

                return ProcessBlockResult.blockConnected()
            else:
                # if the block is higher than current best block, we need to reorg
                # otherwise, we just save the block to disk
                if block.height() > currentHeader.height:
                    return ProcessBlockResult.blockSaved(ProcessBlockResult.ProcessBlockError.NEED_REORG)
                Logger.debug(
                    f"{self._LOGGER_TITLE} Block {block.hashHex()} is lower or equal to the current best block"
                )
                return ProcessBlockResult.blockSaved(ProcessBlockResult.ProcessBlockError.LOWER_OR_EQUAL_HEIGHT)

    def connectBlock(self, block: Block, isGenesis: bool) -> Optional[str]:
        """
        Connect a block to the blocktree.
        Assumed that the block is validated, saved in the db and is not orphan.

        This method finalizes the block connection process by:
        1. Applying the block state changes to the main state database
        2. Updating the blockchain head
        3. Writing transaction receipts to the database
        4. Updating header chain references
        5. Notifying observers of the new block

        Args:
            block (Block): The block to connect to the blockchain
            isGenesis (bool): Whether this block is the genesis block

        Returns:
            Optional[str]: None if successful, error message string if failed
        """

        # get apply block result
        # mostly the result is cached, because connectBlock should be called only by processBlock
        # but just in case, call applyBlockInSandbox again
        result = self.applyBlockInSandbox(block, isGenesis)
        if result.error is not None:
            Logger.error(f"{self._LOGGER_TITLE} Apply block in sandbox failed: {result.error}")
            return result.error

        # commit to the state
        try:
            newState = self.statedbManager.applyState(result.state)
            newState.commit(block.height())
            # Clear the apply block cache as the state has changed
            self.clearApplyBlockCache()
        except Exception as e:
            Logger.error(f"{self._LOGGER_TITLE} Failed to commit state: {e}")
            return str(e)

        self.blockCache.put(block.hash(), block)

        # set head
        self.writeHeadBlock(block)

        # write receipts
        blockHash = block.hash()
        for receipt in result.receipts:
            receipt.block_hash = blockHash
        rawdb.writeReceipts(self.db, block.hash(), block.height(), result.receipts)

        # if it's genesis block, set genesis block hash
        if isGenesis:
            self.genesisBlock = block.header
            self.headerChain.setGenesis(block.header)

        self.headerChain.setCurrentHeader(block.header)
        # connect block successfully
        Logger.debug(f"{self._LOGGER_TITLE} Block {block.hashHex()} connected successfully.")
        self.notifyConnectBlockObservers(block)

        return None

    def processOrphanBlocks(self, parentBlockHash: bytes):
        """
        Process orphan blocks that have the same parent block hash, find one that can be connected to the blocktree
        """
        orphanBlocks = self.orphanBlocks.getOrphanBlocks(parentBlockHash)
        for block in orphanBlocks:
            result = self.processBlock(block, isGenesis=False)
            if result.isBlockConnected():
                # once a block is connected, remove all orphan blocks with the same parent block hash
                self.orphanBlocks.removeOrphanBlocks(parentBlockHash)
                break
            else:
                Logger.error(f"{self._LOGGER_TITLE} Orphan block {block.hashHex()} failed to connect")

    def processSyncBlocks(self, blocks: List[Block]):
        """
        Process sync blocks received from peers during synchronization.

        This method handles blocks received during chain synchronization:
        1. Validates the input blocks for basic integrity
        2. Verifies the first block is a common ancestor in our blockchain
        3. Processes each block in sequence
        4. Performs chain reorganization if necessary when the new chain is better

        Args:
            blocks (List[Block]): Ordered list of blocks to process, starting with common ancestor

        Returns:
            None
        """
        Logger.debug(f"{self._LOGGER_TITLE} Sync blocks received: {blocks}")

        # check if the chain is empty
        if not blocks or len(blocks) == 0:
            return

        # check if blocks contain None
        if any([block is None for block in blocks]):
            return

        # check if the first block in our blockchain, i.e. common ancestor
        commonAncestor = blocks[0]
        if self.getBlock(commonAncestor.hash(), commonAncestor.height()) is None:
            return

        # needReorg = blocks[-1].height() > self.currentBlock().height()
        needReorg = self.processBlock(blocks[-1], isGenesis=False).isNeedReorg()
        if needReorg:
            # get common ancestor state
            state = self.statedbManager.getStateByHeight(commonAncestor.height())
            if state is None:
                Logger.error(f"{self._LOGGER_TITLE} Failed to get state by height: {commonAncestor.height()}")
                return

            # clear the cache and prepare for reorg
            self.clearApplyBlockCache()

            for block in blocks[1:]:
                result = self.processBlock(block, isGenesis=False)
                if not (result.blockSaved() or result.isBlockConnected()):
                    Logger.error(f"{self._LOGGER_TITLE} Process block {block.hashHex()} failed: {result.error}")
                    return

            # then reorganize
            currentBlock = self.currentBlock()
            # if the last block is not the current best block, reorganize
            if currentBlock is not None and currentBlock.hash() != blocks[-1].hash():
                self.reorganize(currentBlock.header, blocks[-1])

    def reorganize(self, oldHead: AbstractHeader, newHead: Block):
        """
        Reorganize the chain

        reorg takes two blocks, an old chain and a new chain and will reconstruct the
        blocks and inserts them to be part of the new canonical chain and accumulates
        potential missing transactions
        """
        newChain: List[Block] = []
        oldChain: List[Block] = []
        commonBlock: Optional[Block] = None

        deleteTxs: List[bytes] = []
        addTxs: List[bytes] = []

        oldBlock = self.getBlock(oldHead.hash(), oldHead.height)
        if oldBlock is None:
            Logger.error(f"{self._LOGGER_TITLE} current head block missing")
            return
        newBlock = newHead

        # Reduce the longer chain to the same height as the shorter one
        if oldBlock.height() > newBlock.height():
            # Old chain is longer, gather all transactions and logs as deleted ones
            while oldBlock.height() > newBlock.height():
                oldChain.append(oldBlock)
                deleteTxs.extend([tx.hash() for tx in oldBlock.transactions])
                oldBlock = self.getBlock(oldBlock.parentHash(), oldBlock.height() - 1)
                if oldBlock is None:
                    Logger.error(f"{self._LOGGER_TITLE} invalid old chain")
                    return
        else:
            # New chain is longer, stash all blocks away for subsequent insertion
            while newBlock.height() > oldBlock.height():
                newChain.append(newBlock)
                newBlock = self.getBlock(newBlock.parentHash(), newBlock.height() - 1)
                if newBlock is None:
                    Logger.error(f"{self._LOGGER_TITLE} invalid new chain")
                    return

        # Both sides of the reorg are at the same number, reduce both until the common ancestor is found
        while True:
            # If the common ancestor was found, break
            if oldBlock.hash() == newBlock.hash():
                commonBlock = oldBlock
                break

            # remove the old block as well as stash away a new block
            oldChain.append(oldBlock)
            deleteTxs.extend([tx.hash() for tx in oldBlock.transactions])
            newChain.append(newBlock)

            # Step back with both chains
            oldBlock = self.getBlock(oldBlock.parentHash(), oldBlock.height() - 1)
            if oldBlock is None:
                Logger.error(f"{self._LOGGER_TITLE} invalid old chain")
                return
            newBlock = self.getBlock(newBlock.parentHash(), newBlock.height() - 1)
            if newBlock is None:
                Logger.error(f"{self._LOGGER_TITLE} invalid new chain")
                return

        # print reorg log
        if len(oldChain) > 0 and len(newChain) > 0:
            Logger.info(
                f"{self._LOGGER_TITLE} Chain reorg detected: height {commonBlock.height()}, hash {commonBlock.hashHex()}, drop {len(oldChain)}, dropfrom {oldChain[0].hashHex()}, add {len(newChain)}, addfrom {newChain[0].hashHex()}"
            )
        elif len(newChain) > 0:
            # Special case happens in the post merge stage that current head is
            # the ancestor of new head while these two blocks are not consecutive
            Logger.info(
                f"{self._LOGGER_TITLE} Extend chain: add {len(newChain)}, height {newChain[0].height()}, hash {newChain[0].hashHex()}"
            )
        else:
            # len(newChain) == 0 && len(oldChain) > 0
            # rewind the canonical chain to a lower point.
            Logger.error(
                f"{self._LOGGER_TITLE} Impossible reorg, please file an issue, oldheight {oldBlock.height()}, oldhash {oldBlock.hashHex()}, oldblocks {len(oldChain)}, newheight {newBlock.height()}, newhash {newBlock.hashHex()}, newblocks {len(newChain)}"
            )

        with self.blockStateLock:
            # Acquire the tx-lookup lock before mutation. This step is essential
            # as the txlookups should be changed atomically, and all subsequent
            # reads should be blocked until the mutation is complete.
            with self.transactionLookupLock.gen_wlock():
                # Insert the new chain segment in incremental order, from the old to the new.
                for block in reversed(newChain):
                    # Collect the new added transactions.
                    addTxs.extend([tx.hash() for tx in block.transactions])

                # Delete useless indexes right now which includes the non-canonical
                # transaction indexes, canonical chain indexes which above the head.
                indexesBatch = self.db.newBatch()
                diffs = hashDifference(deleteTxs, addTxs)
                for tx in diffs:
                    rawdb.deleteTransactionLookupEntry(indexesBatch, tx)

                # Delete all hash markers that are not part of the new canonical chain.
                # Because the reorg function does not handle new chain head, all hash
                # markers greater than or equal to new chain head should be deleted.
                height = commonBlock.height()

                i = height + 1
                while True:
                    hash = rawdb.readCanonicalHash(self.db, i)
                    if hash is None:
                        break
                    rawdb.deleteCanonicalHash(indexesBatch, i)
                    i += 1

                try:
                    indexesBatch.write()
                except Exception as e:
                    Logger.error(f"{self._LOGGER_TITLE} Failed to delete useless indexes: {e}")

                self.transactionLookupCache.clear()
            # TODO: handle log events

            # Rollback on-chain indexes
            index_undo_log.rollbackToBlock(self.db, commonBlock.height())

            # Recover state to the common ancestor block
            # header chain rollback to common block
            self.headerChain.setHead(commonBlock.height())
            self.statedbManager.clearStateCache()
            # Clear apply block cache since we're reorganizing the chain
            self.clearApplyBlockCache()
            # Clean up the chain state manager cache
            for block in oldChain:
                self.blockCache.remove(block.hash())
                self.bodyCache.remove(block.hash())
                self.bodyRLPCache.remove(block.hash())
                self.receiptsCache.remove(block.hash())

            # Roll forward the new chain
            for block in reversed(newChain):
                result = self.processBlock(block, isGenesis=False)
                # in here, result.isLowerOrEqualHeight() means the block is already in the chain
                # due to process orphan blocks
                # example:
                # node A: A -> B -> C
                # node B: A -> B -> D -> E -> F
                # node B mint a block F, and broadcast it to node A
                # node A will process F first, add it into orphan block pool. then request syncing blocks from node B
                # syncing block includes [B, D, E, F]
                # then in process block, [D, E, F] will be saved into the block tree.
                # in reorganization, rollback to common ancestor(B), and then process [D, E, F]
                # when processing D, it will connect to the block tree successfully
                # when processing E, it will connect to the block tree successfully
                # and F will be processed when processing E since it's an orphan block who's parent is E. It will connect to the block tree successfully
                # so when processing F, it will return LOWER_OR_EQUAL_HEIGHT since it's already in the chain
                isConnected = result.isBlockConnected() or result.isLowerOrEqualHeight()
                if not isConnected:
                    Logger.error(f"{self._LOGGER_TITLE} Connect block {block.hash()} failed: {result.error}")
                    return

            # Handle old transactions which are not included in the new chain
            # Broadcast orphan transactions
            orphanTxs = []
            for block in oldChain:
                for tx in block.transactions[1:]:
                    if tx.hash() in diffs:
                        orphanTxs.append(tx)

            for tx in orphanTxs:
                self.memoryPool.addTransaction(tx)
                # broadcast the transaction to all peers
                asyncio.create_task(self.client.broadcastTransaction(toJson(tx)))

    def getBlocks(self, locator: List[bytes]) -> Optional[List[Block]]:
        """
        return the blocks by locator
        """
        bestBlock = self.currentBlock()
        # if current best block is None, return empty list
        if bestBlock is None:
            return []

        # find common ancestor
        commonAncestorHash = b""
        commonAncestorHeight = -1
        for blockHash in locator:
            if commonAncestorHeight == -1:
                locatorBlock = self.headerChain.getHeaderByHash(blockHash)
            else:
                locatorBlock = self.getBlock(blockHash, commonAncestorHeight)
            if locatorBlock is not None:
                commonAncestorHeight = locatorBlock.height
                if locatorBlock.height < bestBlock.height():
                    commonAncestorHash = blockHash
                    break

        # if common ancestor is not found, set it to genesis block
        if len(commonAncestorHash) == 0:
            commonAncestorHash = self.genesis().hash()
            commonAncestorHeight = 0

        commonAncestor = self.getBlock(commonAncestorHash, commonAncestorHeight)
        if commonAncestor is None:
            # fail to get common ancestor, error
            return None

        # find and return path from common ancestor to this node's best block
        path = []
        currentBlockHash = bestBlock.hash()
        currentBlockHeight = bestBlock.height()
        while currentBlockHash != commonAncestorHash:
            currentBlock = self.getBlock(currentBlockHash, currentBlockHeight)
            if currentBlock is None:
                # fail to get block, error
                return None
            path.append(currentBlock)
            currentBlockHash = currentBlock.parentHash()
            currentBlockHeight -= 1

        # add common ancestor to the path
        path.append(commonAncestor)
        path.reverse()
        return path

    # ----- block chain reader -----

    def currentHeader(self) -> Optional[SPOSHeader]:
        """
        return the current best block header
        """
        return self.headerChain.currentHeader.get()

    def currentBlock(self) -> Optional[Block]:
        """
        CurrentBlock retrieves the current head block of the canonical chain. The
        block is retrieved from the blockchain's internal cache.
        """
        return self._currentBlock.get()

    def hasHeader(self, hash: bytes, height: int) -> bool:
        """
        HasHeader returns true if the header with the given hash is in the database
        """
        return self.headerChain.hasHeader(hash, height)

    def getHeader(self, hash: bytes, height: int) -> Optional[SPOSHeader]:
        """
        GetHeader retrieves a block header from the database by hash, caching it if
        """
        return self.headerChain.getHeader(hash, height)

    def getHeaderByHash(self, hash: bytes) -> Optional[SPOSHeader]:
        """
        GetHeader retrieves a block header from the database by hash, caching it if
        """
        return self.headerChain.getHeaderByHash(hash)

    def getHeaderByHeight(self, height: int) -> Optional[SPOSHeader]:
        """
        GetHeaderByHeight retrieves the header belonging to the given height
        from the cache or database
        """
        return self.headerChain.getHeaderByHeight(height)

    def getBody(self, hash: bytes) -> Optional[Body]:
        """
        GetBody retrieves a block body (transactions) from the database by
        hash, caching it if found
        """
        if (body := self.bodyCache.get(hash)) is not None:
            return body
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        body = rawdb.readBody(self.db, hash, height)
        if body is None:
            return None
        self.bodyCache.put(hash, body)
        return body

    def getBodyRLP(self, hash: bytes) -> Optional[bytes]:
        """
        GetBodyRLP retrieves a block body (transactions) from the database by
        hash, caching it if found
        """
        if (bodyRLP := self.bodyRLPCache.get(hash)) is not None:
            return bodyRLP
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        bodyRLP = rawdb.readBodyRLP(self.db, hash, height)
        if bodyRLP is None:
            return None
        self.bodyRLPCache.put(hash, bodyRLP)
        return bodyRLP

    def hasBlock(self, hash: bytes, height: int) -> bool:
        """
        HasBlock checks if a block is fully present in the database or not.
        """
        if self.blockCache.contains(hash):
            return True
        if not self.hasHeader(hash, height):
            return False
        return rawdb.hasBody(self.db, hash, height)

    def getBlock(self, hash: bytes, height: int) -> Optional[Block]:
        """
        GetBlock retrieves a block by hash, caching it if found
        """
        if (block := self.blockCache.get(hash)) is not None:
            return block
        block = rawdb.readBlock(self.db, SPOSHeader, hash, height)
        if block is None:
            return None
        self.blockCache.put(block.hash(), block)
        return block

    def getBlockByHash(self, hash: bytes) -> Optional[Block]:
        """
        GetBlockByHash retrieves a block by hash, caching it if found
        """
        height = self.headerChain.getBlockHeight(hash)
        if height is None:
            return None
        return self.getBlock(hash, height)

    def getBlockByHeight(self, height: int) -> Optional[Block]:
        """
        GetBlockByHeight retrieves a block by height, caching it if found
        """
        hash = rawdb.readCanonicalHash(self.db, height)
        if hash is None:
            return None
        return self.getBlock(hash, height)

    def getReceiptsByHash(self, hash: bytes) -> Optional[List[Receipt]]:
        """
        GetReceiptsByHash retrieves a block's receipts by hash, caching it if found
        """
        if (receipts := self.receiptsCache.get(hash)) is not None:
            return receipts
        height = rawdb.readHeaderHeight(self.db, hash)
        if height is None:
            return None
        receipts = rawdb.readReceipts(self.db, hash, height)
        if receipts is None:
            return None
        self.receiptsCache.put(hash, receipts)
        return receipts

    def getCanonicalHash(self, height: int) -> Optional[bytes]:
        """
        GetCanonicalHash retrieves the hash of the canonical block at a given height
        """
        return self.headerChain.getCanonicalHash(height)

    def getTransactionLookup(self, hash: bytes) -> Optional[TransactionLookup]:
        """
        GetTransactionLookup retrieves the lookup along with the transaction
        itself associate with the given transaction hash.
        """
        with self.transactionLookupLock.gen_rlock():
            if (item := self.transactionLookupCache.get(hash)) is not None:
                return item
            lookup = rawdb.readTransaction(self.db, hash)
            if lookup is None:
                return None
            self.transactionLookupCache.put(hash, lookup)
            return lookup

    def hasState(self, hashHex: str) -> bool:
        """
        HasState returns true if the state with the given hash is in the database
        """
        return self.statedbManager.hasState(hexToBytes(hashHex))

    def hasBlockAndState(self, hash: bytes, height: int) -> bool:
        """
        HasBlockAndState returns true if the block and state with the given hash and height are in the database
        """
        block: Optional[Block] = self.getBlock(hash, height)
        if block is None:
            return False
        return self.hasState(block.header.state_root)

    def genesis(self) -> Optional[SPOSHeader]:
        """
        Genesis retrieves the genesis block of the chain
        """
        return self.genesisBlock

    def writeHeadBlock(self, block: Block) -> None:
        """
        writeHeadBlock injects a new head block into the current blockchain. This method assumes that the block
        is indeed a true head. It will also reset the head header and the head snap sync block to this very same
        block if they are older or if they are on a different side chain.

        Note, this function assumes that the `mu` mutex is held!
        """
        batch = self.db.newBatch()
        rawdb.writeHeadHeaderHash(batch, block.hash())
        rawdb.writeCanonicalHash(batch, block.hash(), block.height())
        rawdb.writeTxLookupEntriesByBlock(batch, block)
        rawdb.writeHeadBlockHash(batch, block.hash())

        rawdb.writeHeader(batch, block.header)

        # Flush the whole batch into the disk, exit the node if failed
        try:
            batch.write()
        except Exception as e:
            Logger.error(f"Failed to update chain indexes and markers: {e}")

        # Update all in-memory chain markers in the last step
        self.headerChain.setCurrentHeader(block.header)
        self._currentBlock.set(block)

    # ----- utils -----

    def generateLocator(self) -> List[str]:
        currentBlock = self.currentBlock()
        if currentBlock is None:
            return []

        currentBlockHash = currentBlock.hash()

        # start from the best block
        currentBlockHash = currentBlockHash
        currentBlockHeight = currentBlock.height()
        locator = []
        step = 1
        counter = 1
        MAX_LOCATOR_LENGTH = 32

        while currentBlockHash != hexToBytes("00000000000000000000000000000000") and counter < MAX_LOCATOR_LENGTH:
            locator.append(bytesToHex(currentBlockHash))

            for _ in range(step):
                currentBlock = self.getBlock(currentBlockHash, currentBlockHeight)
                if currentBlock is None:
                    break
                currentBlockHash = currentBlock.parentHash()
                currentBlockHeight -= 1
                if currentBlockHeight < 0:
                    return locator

            if counter >= 10:
                # double steps, exponential growth
                step *= 2
            counter += 1

        return locator

    # ----- misc -----

    def registerConnectBlockObserver(self, observer):
        """
        Register observer on connect block event
        """
        self.connectBlockObservers.append(observer)

    def notifyConnectBlockObservers(self, block: Block):
        """
        Notify observers on connect block event
        """
        for observer in self.connectBlockObservers:
            observer.onConnectBlock(block)

    def getStatusString(self) -> str:
        """
        return the status of the chain state manager, including:
        - the current state of the chain(best block, parent block, height, etc.)
        """
        currentHeader: SPOSHeader = self.headerChain.currentHeader.get()
        if currentHeader is None:
            return "No blocks in the chain"
        transactions = self.getBody(currentHeader.hash()).transactions
        return f"""{self._LOGGER_TITLE}
    Current best block: {currentHeader.hashHex()}
    Parent block: {bytesToHex(currentHeader.parent_hash)}
    Height: {currentHeader.height}
    Transaction count: {len(transactions)}
    Signature count: {len(currentHeader.signatures)}
    State Root: {currentHeader.state_root}
    Transactions Root: {currentHeader.transactions_root}
    Receipts Root: {currentHeader.receipts_root}
    Bloom: {currentHeader.bloom}
    Protocol timestamp: {currentHeader.protocol_timestamp}
    Proposer address: {bytesToHex(currentHeader.proposer_address)}"""
