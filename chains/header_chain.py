from typing import Optional, Type

import kivy

import rawdb
import vgraphdb

# Import our cache management system
from cache_manager import CacheManager
from common import Atomic
from common.models import AbstractHeader

headerCacheLimit = 512
heightCacheLimit = 1024


class HeaderChain:
    def __init__(self, headerType: Type[AbstractHeader], chainDb: vgraphdb.KeyValueStore):
        self.headerType: Type[AbstractHeader] = headerType

        self.chainDb: vgraphdb.KeyValueStore = chainDb
        self.genesisHeader: Optional[AbstractHeader] = None

        self.currentHeader: Atomic[Optional[AbstractHeader]] = Atomic(None)
        self.currentHeaderHash: Optional[bytes] = None

        self.headerCache: Atomic = Atomic(
            CacheManager.createCache(name="header_cache", cacheType="lru", maxSize=headerCacheLimit)
        )
        self.heightCache: Atomic = Atomic(
            CacheManager.createCache(name="height_cache", cacheType="lru", maxSize=heightCacheLimit)
        )

    def getBlockHeight(self, hash: bytes) -> int:
        """
        Get<PERSON>lock<PERSON><PERSON><PERSON> retrieves the block number belonging to the given hash
        from the cache or database
        """
        if self.heightCache.get().contains(hash):
            return self.heightCache.get().get(hash)

        number = rawdb.readHeaderHeight(self.chainDb, hash)
        if number is not None:
            self.heightCache.get().put(hash, number)
        return number

    # TODO: may refactor to batch operations, such as inertChain, writeHeaders

    def writeHeader(self, header: AbstractHeader):
        """
        WriteHeader writes the given header to the database
        """
        rawdb.writeHeader(self.chainDb, header)
        self.heightCache.get().put(header.hash, header.height)

    def getHeader(self, hash: bytes, height: int) -> Optional[AbstractHeader]:
        """
        GetHeader retrieves the header belonging to the given hash
        from the cache or database
        """
        if self.headerCache.get().contains(hash):
            return self.headerCache.get().get(hash)

        header = rawdb.readHeader(self.chainDb, self.headerType, hash, height)
        if header is not None:
            self.headerCache.get().put(hash, header)
        return header

    def getHeaderByHash(self, hash: bytes) -> Optional[AbstractHeader]:
        """
        GetHeader retrieves a block header from the database by hash, caching it if
        """
        height = self.getBlockHeight(hash)
        if height is None:
            return None
        return self.getHeader(hash, height)

    def getHeaderbByHeight(self, height: int) -> Optional[AbstractHeader]:
        """
        GetHeaderByHeight retrieves the header belonging to the given height
        from the cache or database
        """
        hash = rawdb.readCanonicalHash(self.chainDb, height)
        if hash is None:
            return None
        return self.getHeader(hash, height)

    def hasHeader(self, hash: bytes, height: int) -> bool:
        """
        HasHeader returns true if the header with the given hash is in the database
        """
        if self.heightCache.get().contains(hash) or self.headerCache.get().contains(hash):
            return True
        return rawdb.hasHeader(self.chainDb, hash, height)

    def getHeaderByHeight(self, height: int) -> Optional[AbstractHeader]:
        """
        GetHeaderByHeight retrieves the header belonging to the given height
        from the cache or database
        """
        hash = rawdb.readCanonicalHash(self.chainDb, height)
        if hash is None:
            return None
        return self.getHeader(hash, height)

    def getCanonicalHash(self, height: int) -> Optional[bytes]:
        """
        GetCanonicalHash retrieves the hash of the canonical block at the given height
        """
        return rawdb.readCanonicalHash(self.chainDb, height)

    def getCurrentHeader(self) -> Optional[AbstractHeader]:
        """
        CurrentHeader retrieves the current head header of the canonical chain. The
        header is retrieved from the HeaderChain's internal cache.
        """
        if self.currentHeader.get() is not None:
            return self.currentHeader.get()

        header = self.getHeaderByHash(self.currentHeaderHash)
        if header is not None:
            self.currentHeader.set(header)
        return header

    def setCurrentHeader(self, head: AbstractHeader):
        """
        SetCurrentHeader sets the in-memory head header marker of the canonical chain as the given header.
        """
        self.currentHeader.set(head)
        self.currentHeaderHash = head.hash

    def setGenesis(self, head: AbstractHeader):
        """
        SetGenesisHeader sets the genesis header of the chain
        """
        self.genesisHeader = head

    def setHead(self, headBlock: int):
        """
        SetHead rewinds the local chain to a new head block.
        Everything above the new head will be deleted and the new one set.
        """
        batch = self.chainDb.newBatch()
        origin = True

        header = self.currentHeader.get()
        while header is not None and header.height > headBlock:
            height = header.height

            # Rewind chain to new head
            parent = self.getHeaderByHash(header.parent_hash)
            if parent is None:
                parent = self.genesisHeader
            parentHash = parent.hash()

            # Update the head header
            rawdb.writeHeadHeaderHash(self.chainDb, parentHash)

            self.setCurrentHeader(parent)

            # If this is the first iteration, wipe any leftover data upwards too so
            # we don't end up with dangling daps in the database
            heights = []
            if origin:
                h = height + 1
                while len(rawdb.readAllHashes(self.chainDb, h)) > 0:
                    heights.append(h)
                    h += 1
                origin = False
            heights.append(height)

            # Remove the related data from the database on all sidechains
            for h in heights:
                hashes = rawdb.readAllHashes(self.chainDb, h)
                if len(hashes) == 0:
                    hashes.append(header.height)
                for hash in hashes:
                    rawdb.deleteHeader(batch, hash, h)
                rawdb.deleteCanonicalHash(batch, h)

            header = self.currentHeader.get()

        # Flush all accumulated deletions.
        try:
            batch.write()
        except Exception as e:
            kivy.Logger.error("Failed to rewind block, error: %s", e)

        self.headerCache.get().clear()
        self.heightCache.get().clear()
