import pytest

from vm import ContractTester

contractClient = ContractTester("name_service")


@pytest.fixture(autouse=True)
def register_contract():
    # default new init contract
    contractClient.constructor(
        (
            ("package1", "contract1", "868313975a304236b060121356ed07cc"),
            ("package2", "contract2", "021bb3b101db4ea5807602d7231a3ed4"),
            ("package3", "contract3", "f3b1b3b101db4ea5807602d124123sxc"),
        )
    )


def test_all():
    _, err = contractClient.execute("check_package_contract_name", None, "_p1", "c1")
    assert err == "Invalid package name"
    _, err = contractClient.execute("check_package_contract_name", None, "p1", "_c1")
    assert err == "Invalid contract name"

    _, err = contractClient.execute("check_package_contract_name", None, "package1", "contract1")
    assert err == "Duplicate contract name in this vgraph package"
    _, err = contractClient.execute("check_package_contract_name", None, "package1", "contract2")
    assert err == "Duplicate vgraph package name"

    packageName = "package-test"
    contractName = "contract-test"
    _, err = contractClient.execute("check_package_contract_name", None, packageName, contractName)
    assert err is None

    contractAddress, err = contractClient.execute("register_contract", str, packageName, contractName, "address1")
    assert err is None

    print("contractAddress:", contractAddress)

    _, err = contractClient.execute("check_package_contract_name", None, packageName, contractName)
    assert err == "Duplicate contract name in this vgraph package"

    secondContractAddress, err = contractClient.execute("get_contract_address", str, packageName, contractName)
    assert err is None
    assert "address1" == secondContractAddress
