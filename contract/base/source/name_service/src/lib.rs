#[glue::contract]
mod name_service {
    use glue::collections::Map;

    #[glue::storage]
    pub struct NameService {
        pub package_contract_name_tuple_contract_address_map: Map<(String, String), String>, // (package_name,contract_name) -> contract_address
        pub package_name_set: Map<String, ()>, // package_name
    }

    impl NameService {
        /// Create a new NameService instance.
        ///
        /// # Arguments
        /// * `system_contract_list` - A list of system contracts, each represented by a vector of strings (package name, contract name, contract address).
        ///
        /// package name: The name of the package that the contract belongs to.
        /// contract name: The name of the contract (module name).
        /// contract address: The address of the contract.
        #[glue::constructor]
        pub fn new(system_contract_list: Vec<Vec<String>>) -> Self {
            let mut package_contract_name_tuple_contract_address_map = Map::new();
            let mut package_name_set = Map::new();
            for (package_contract_name_and_contract_address) in system_contract_list {
                let package_name = &package_contract_name_and_contract_address[0];
                let contract_name = &package_contract_name_and_contract_address[1];
                let contract_address = &package_contract_name_and_contract_address[2];
                package_contract_name_tuple_contract_address_map.insert(&(package_name.clone(), contract_name.clone()), &contract_address.clone());
                package_name_set.insert(package_name, &());
            }
            Self {
                package_contract_name_tuple_contract_address_map,
                package_name_set,
            }
        }

        /// Check if the package name and contract name are valid.
        ///
        /// package name: The name of the package that the contract belongs to.
        /// contract name: The name of the contract (module name).
        #[glue::readonly]
        pub fn check_package_contract_name(&self, package_name: String, contract_name: String) -> anyhow::Result<()> {
            // check cargo package name
            match is_valid_name(&package_name) {
                true => {}
                false => { return Err(anyhow::anyhow!("Invalid package name")); }
            }

            // check contract name (module name)
            match is_valid_name(&contract_name) {
                true => {}
                false => { return Err(anyhow::anyhow!("Invalid contract name")); }
            }

            // check duplicate vgraph package contract name
            self.check_duplicate_vgraph_package_contract_name(&package_name, &contract_name)
        }

        /// Register a contract.
        ///
        /// package name: The name of the package that the contract belongs to.
        /// contract name: The name of the contract (module name).
        /// Returns the contract address.
        #[glue::atomic]
        pub fn register_contract(&mut self, package_name: String, contract_name: String, contract_address: String) -> anyhow::Result<()> {
            // check package name
            self.check_package_contract_name(package_name.clone(), contract_name.clone())?;

            // // use uuid generate contract address
            // let contract_address = uuid::Uuid::new_v4().simple().to_string();

            // insert package name
            self.package_contract_name_tuple_contract_address_map.insert(&(package_name.clone(), contract_name), &contract_address);
            self.package_name_set.insert(&package_name, &());
            Ok(())
        }


        /// Get the contract address.
        ///
        /// package name: The name of the package that the contract belongs to.
        /// contract name: The name of the contract (module name).
        /// Returns the contract address. If the contract does not exist, return None.
        #[glue::readonly]
        pub fn get_contract_address(&self, package_name: String, contract_name: String) -> Option<String> {
            self.package_contract_name_tuple_contract_address_map.get(&(package_name, contract_name))
        }


        fn check_duplicate_vgraph_package_contract_name(&self, package_name: &String, contract_name: &String) -> anyhow::Result<()> {
            // check if the contract name is already registered in this package
            if self.package_contract_name_tuple_contract_address_map.contains(&(package_name.clone(), contract_name.clone())) {
                return Err(anyhow::anyhow!("Duplicate contract name in this vgraph package"));
            }

            // check if the package name is already registered
            if self.package_name_set.contains(package_name) {
                return Err(anyhow::anyhow!("Duplicate vgraph package name"));
            }

            Ok(())
        }
    }


    // check cargo package name and contract name
    #[inline]
    fn is_valid_name(name: &str) -> bool {
        // Check if the name is empty
        if name.is_empty() {
            return false;
        }

        // Check the length constraint: no more than 64 characters
        if name.len() > 64 {
            return false;
        }

        // Check if it starts with a lowercase letter
        if let Some(first_char) = name.chars().next() {
            if !first_char.is_ascii_lowercase() {
                return false;
            }
        } else {
            // Empty string is not a valid crate name (this is already checked above)
            return false;
        }

        // Check if it contains only allowed characters and not consecutive invalid characters
        let mut prev_char = '\0';
        for c in name.chars() {
            if !(c.is_ascii_lowercase() || c.is_ascii_digit() || c == '-' || c == '_') {
                return false;
            }
            if (c == '-' || c == '_') && (c == prev_char || (prev_char == '-' || prev_char == '_')) {
                return false;
            }
            prev_char = c;
        }

        // Check if it starts or ends with a hyphen or underscore
        if name.starts_with('-') || name.ends_with('-') || name.starts_with('_') || name.ends_with('_') {
            return false;
        }

        true
    }

    #[cfg(test)]
    mod tests {
        use super::*;

        #[test]
        fn test_valid_crate_names() {
            let valid_names = vec![
                String::from("valid_crate"),
                String::from("another_valid_crate"),
                String::from("valid123crate"),
                String::from("valid-crate"),
                String::from("validcrate"), // No special characters
                String::from("v"), // Single character, valid as it is a lowercase letter
                String::from("v2"), // Single letter followed by a digit
                String::from("a-b-c-d"), // Alternating letters and hyphens
            ];
            for name in valid_names.iter() {
                assert!(is_valid_name(name), "Expected '{}' to be valid", name);
            }
        }

        #[test]
        fn test_invalid_crate_names() {
            let invalid_names = vec![
                String::from(""), // Empty string
                String::from("InvalidCrate"), // Starts with an uppercase letter
                String::from("valid__crate"), // Consecutive underscores
                String::from("valid--crate"), // Consecutive hyphens
                String::from("-invalid-crate"), // Starts with a hyphen
                String::from("invalid-crate-"), // Ends with a hyphen
                String::from("invalid__crate"), // Consecutive underscores
                String::from("invalid--crate"), // Consecutive hyphens
                String::from("_invalid_crate"), // Starts with an underscore
                String::from("invalid_crate_"), // Ends with an underscore
                String::from("invalid-_-crate"), // Mixed consecutive hyphen and underscore
                String::from("crate_name_with_a_very_long_name_that_exceeds_sixty_four_characters_which_is_not_allowed"), // Too long
                String::from("invalid@crate"), // Contains invalid character '@'
                String::from("invalid crate"), // Contains space
            ];
            for name in invalid_names.iter() {
                assert!(!is_valid_name(name), "Expected '{}' to be invalid", name);
            }
        }

        #[glue::test]
        fn test_all() {
            let contract_address = "0x1234567890abcdef".to_string();
            let mut name_service = NameService::new(vec![vec!["package1".to_string(), "contract1".to_string(), "address1".to_string()]]);
            let err_message = name_service.check_package_contract_name("package1".to_string(), "contract1".to_string()).err().unwrap().to_string();
            assert_eq!(err_message, "Duplicate contract name in this vgraph package");

            let err_message = name_service.check_package_contract_name("package1".to_string(), "contract2".to_string()).err().unwrap().to_string();
            assert_eq!(err_message, "Duplicate vgraph package name");

            assert!(name_service.check_package_contract_name("package-test".to_string(), "contract-test".to_string()).is_ok());

            let register_result = name_service.register_contract("package-test".to_string(), "contract-test".to_string(), contract_address.clone());
            assert!(register_result.is_ok());
            let err_message = name_service.check_package_contract_name("package-test".to_string(), "contract-test".to_string()).err().unwrap().to_string();
            assert_eq!(err_message, "Duplicate contract name in this vgraph package");
            assert_eq!(name_service.get_contract_address("package-test".to_string(), "contract-test".to_string()), Some(contract_address.clone()));
        }
    }
}

