use sha3::{<PERSON>, Keccak256};
use std::collections::HashMap;
use std::ffi::{c_char, CStr, CString};
use std::hash::{Defa<PERSON><PERSON><PERSON><PERSON>, <PERSON>h, <PERSON><PERSON>};
use std::ptr;

use serde::{Deserialize, Serialize};

#[inline]
pub unsafe fn clone_c_string(iter_id: *mut c_char) -> *mut c_char {
    let c_str = CStr::from_ptr(iter_id);
    let cloned = CString::new(c_str.to_bytes()).unwrap();
    cloned.into_raw()
}

#[cfg(target_arch = "wasm32")]
extern "C" {
    fn debugger(ptr: *mut c_char);
}

#[cfg(target_arch = "wasm32")]
pub fn call_debugger(string: &str) {
    unsafe {
        let debugger_str_ptr = string_to_c_char(string);
        debugger(debugger_str_ptr);
    }
}

#[inline]
pub fn string_to_c_char(string: &str) -> *mut c_char {
    CString::new(string)
        .map(|c_string| c_string.into_raw())
        .unwrap_or_else(|_| ptr::null_mut())
}

#[no_mangle]
pub extern "C" fn allocate_c_char(len: usize) -> *mut c_char {
    let buffer = vec![0u8; len];
    let raw_ptr = buffer.as_ptr() as *mut c_char;
    std::mem::forget(buffer);
    raw_ptr
}

#[no_mangle]
pub extern "C" fn deallocate_c_char(ptr: *mut c_char) {
    if ptr.is_null() {
        return;
    }
    unsafe {
        let _ = CString::from_raw(ptr);
    }
}

#[inline]
pub fn get_string(ptr: *mut c_char) -> Option<String> {
    // if None
    if ptr.is_null() {
        return None;
    }
    let c_str = unsafe { CStr::from_ptr(ptr) };
    let str_slice = c_str.to_str().unwrap();
    Some(str_slice.to_string())
}

#[inline]
pub fn write_string(string: &str) -> *mut c_char {
    CString::new(string)
        .map(|c_string| c_string.into_raw())
        .unwrap_or_else(|_| ptr::null_mut())
}

#[inline]
pub fn object_to_json_ptr<T: serde::Serialize>(object: T) -> anyhow::Result<*mut c_char> {
    let path_json = serde_json::to_string(&object)?;
    Ok(write_string(&path_json))
}

#[inline]
pub fn json_ptr_to_object<T: serde::de::DeserializeOwned>(ptr: *mut c_char) -> anyhow::Result<T> {
    let c_string = unsafe { CString::from_raw(ptr) };
    let path_json = c_string.to_str()?;
    let object = serde_json::from_str(path_json)?;
    Ok(object)
}

#[derive(serde::Serialize, serde::Deserialize, Debug)]
pub struct SerializableResult {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub value: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub err: Option<String>,
}

impl SerializableResult {
    #[inline]
    pub fn from<T: serde::Serialize>(result: Result<T, anyhow::Error>) -> Self {
        match result {
            Ok(value) => SerializableResult {
                value: Option::from(serde_json::to_string(&Some(value)).unwrap()),
                err: None,
            },
            Err(err) => SerializableResult {
                value: None,
                err: Some(err.to_string()),
            },
        }
    }

    #[inline]
    pub fn default() -> Self {
        SerializableResult {
            value: None,
            err: None,
        }
    }
}

#[inline]
/// Generates a human-readable or numeric hash for a key.
/// If the key's string representation is short enough, it's used directly.
/// Otherwise, a numeric hash is returned with prefix @.
pub fn key_hash<K: Hash + serde::Serialize>(key: &K) -> String {
    let key_string = serde_json::to_string(&key);
    match key_string {
        Ok(key) => {
            // if key is number convert to string and return
            if key.parse::<i64>().is_ok() {
                return key;
            }
            // if key is String and len < 19, use key directly
            if key.starts_with('"') && key.ends_with('"') && key.len() < 19 {
                return key[1..key.len() - 1].to_string();
            } else if key.starts_with("[") && key.ends_with("]") {
                let vec = serde_json::from_str::<Vec<String>>(&key);
                match vec {
                    Ok(vec) => {
                        let temp_key = vec.join("-");
                        if temp_key.len() < 19 {
                            return temp_key;
                        }
                    }
                    Err(_) => {}
                }
            }
        }
        Err(_) => {}
    }

    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    // if key is too long, use hash value, prefix with "@"
    format!("@{}", hasher.finish().to_string())
}

#[derive(Serialize, Deserialize, Debug)]
pub struct FuncArgs<T> {
    pub args: T,
    pub envs: HashMap<String, String>,
}

/// Computes the Keccak-256 hash of the input data.
pub fn keccak256(data: &[u8]) -> [u8; 32] {
    let mut hasher = Keccak256::new();
    hasher.update(data);
    hasher.finalize().into()
}

pub fn bytes_to_hex<T: AsRef<[u8]>>(bytes: T) -> String {
    format!("0x{}", hex::encode(bytes.as_ref()))
}

pub fn hex_to_bytes(hex: &str) -> Option<Vec<u8>> {
    hex::decode(hex.strip_prefix("0x")?).ok()
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_key_hash() {
        let key = "hello";
        let hash = key_hash(&key);
        assert_eq!(hash, "hello");

        let key = "hello world";
        let hash = key_hash(&key);
        assert_eq!(hash, "hello world");

        let key = vec!["hello".to_string(), "world".to_string()];
        let hash = key_hash(&key);
        assert_eq!(hash, "hello-world");

        let key = vec!["hello".to_string(), "world".to_string(), "123".to_string()];
        let hash = key_hash(&key);
        assert_eq!(hash, "hello-world-123");

        let key = ("123", "hello".to_string());
        let hash = key_hash(&key);
        assert_eq!(hash, "123-hello");

        let key = ("123", "hello".to_string(), "world".to_string());
        let hash = key_hash(&key);
        assert_eq!(hash, "123-hello-world");

        let key: i32 = 123;
        let hash = key_hash(&key);
        assert_eq!(hash, "123");

        let key: i64 = 1234567890123456789i64;
        let hash = key_hash(&key);
        assert_eq!(hash, "1234567890123456789");

        let key: u32 = 123u32;
        let hash = key_hash(&key);
        assert_eq!(hash, "123");
    }

    #[test]
    fn test_keccak256() {
        // Test with an empty input
        let input = b"";
        let expected = [
            0xc5, 0xd2, 0x46, 0x01, 0x86, 0xf7, 0x23, 0x3c, 0x92, 0x7e, 0x7d, 0xb2, 0xdc, 0xc7,
            0x03, 0xc0, 0xe5, 0x00, 0xb6, 0x53, 0xca, 0x82, 0x27, 0x3b, 0x7b, 0xfa, 0xd8, 0x04,
            0x5d, 0x85, 0xa4, 0x70,
        ];
        assert_eq!(keccak256(input), expected);
    }
}

