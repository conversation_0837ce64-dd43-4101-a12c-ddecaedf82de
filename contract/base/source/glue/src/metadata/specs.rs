use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct ContractSpec {
    /// The contract documentation.
    docs: Vec<String>,

    /// The constructor of the contract.
    constructor: FunctionSpec,

    /// The readonly functions of the contract.
    readonly_functions: Vec<FunctionSpec>,

    /// The atomic functions of the contract.
    atomic_functions: Vec<FunctionSpec>,

    /// The events of the contract.
    events: Vec<EventSpec>,
}

impl ContractSpec {
    pub fn new() -> ContractSpecBuilder {
        ContractSpecBuilder {
            spec: ContractSpec {
                docs: Vec::new(),
                constructor: FunctionSpec::from_label("".to_string()).build(),
                readonly_functions: Vec::new(),
                atomic_functions: Vec::new(),
                events: Vec::new(),
            },
        }
    }
}

pub struct ContractSpecBuilder {
    spec: ContractSpec,
}

impl ContractSpecBuilder {
    pub fn new() -> Self {
        Self {
            spec: ContractSpec {
                docs: Vec::new(),
                constructor: FunctionSpec::from_label("".to_string()).build(),
                readonly_functions: Vec::new(),
                atomic_functions: Vec::new(),
                events: Vec::new(),
            },
        }
    }
}

impl ContractSpecBuilder {
    pub fn constructor(mut self, constructor: FunctionSpec) -> Self {
        self.spec.constructor = constructor;
        self
    }

    pub fn readonly_functions(mut self, readonly_functions: Vec<FunctionSpec>) -> Self {
        self.spec.readonly_functions = readonly_functions;
        self
    }

    pub fn atomic_functions(mut self, atomic_functions: Vec<FunctionSpec>) -> Self {
        self.spec.atomic_functions = atomic_functions;
        self
    }

    pub fn events(mut self, events: Vec<EventSpec>) -> Self {
        self.spec.events = events;
        self
    }

    pub fn docs(mut self, docs: Vec<String>) -> Self {
        self.spec.docs = docs;
        self
    }

    pub fn build(self) -> ContractSpec {
        self.spec
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct FunctionSpec {
    /// The name of the function.
    label: String,
    /// The deployment handler documentation.
    docs: Vec<String>,
    /// Whether the function is readonly.
    readonly: bool,
    /// The args of the function.
    args: Vec<FunctionParamSpec>,
    /// The result of the function.
    return_type: FunctionReturnTypeSpec,
}

impl FunctionSpec {
    pub fn from_label(label: String) -> FunctionSpecBuilder {
        FunctionSpecBuilder {
            spec: FunctionSpec {
                label,
                docs: Vec::new(),
                readonly: false,
                args: Vec::new(),
                return_type: FunctionReturnTypeSpec::new("".to_string()),
            },
        }
    }
}

pub struct FunctionSpecBuilder {
    spec: FunctionSpec,
}

impl FunctionSpecBuilder {
    pub fn readonly(mut self, readonly: bool) -> Self {
        self.spec.readonly = readonly;
        self
    }

    pub fn args(mut self, args: Vec<FunctionParamSpec>) -> Self {
        self.spec.args = args;
        self
    }

    pub fn return_type(mut self, return_type: FunctionReturnTypeSpec) -> Self {
        self.spec.return_type = return_type;
        self
    }

    pub fn docs(mut self, docs: Vec<String>) -> Self {
        self.spec.docs = docs;
        self
    }

    pub fn build(self) -> FunctionSpec {
        self.spec
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct FunctionParamSpec {
    /// The name of the parameter.
    label: String,

    /// The type of the parameter.
    #[serde(rename = "type")]
    ty: String,
}

impl FunctionParamSpec {
    pub fn new(label: String, ty: String) -> Self {
        Self { label, ty }
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct FunctionReturnTypeSpec {
    /// The type of the result.
    #[serde(rename = "type")]
    ty: String,
}

impl FunctionReturnTypeSpec {
    pub fn new(ty: String) -> Self {
        Self { ty }
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct EventSpec {
    /// The name of the event.
    label: String,

    /// The event documentation.
    docs: Vec<String>,

    /// The fields of the event.
    fields: Vec<EventFieldSpec>,
}

impl EventSpec {
    pub fn new(label: String) -> EventSpecBuilder {
        EventSpecBuilder {
            spec: EventSpec {
                label,
                docs: Vec::new(),
                fields: Vec::new(),
            },
        }
    }
}

pub struct EventSpecBuilder {
    spec: EventSpec,
}

impl EventSpecBuilder {
    pub fn docs(mut self, docs: Vec<String>) -> Self {
        self.spec.docs = docs;
        self
    }

    pub fn fields(mut self, fields: Vec<EventFieldSpec>) -> Self {
        self.spec.fields = fields;
        self
    }

    pub fn build(self) -> EventSpec {
        self.spec
    }
}

#[derive(Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct EventFieldSpec {
    /// The name of the field.
    label: String,

    /// The type of the field.
    #[serde(rename = "type")]
    ty: String,

    /// Whether the field is topic.
    topic: bool,
}

impl EventFieldSpec {
    pub fn new(label: String, ty: String, topic: bool) -> Self {
        Self { label, ty, topic }
    }
}
