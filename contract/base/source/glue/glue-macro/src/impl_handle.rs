use crate::utils::{extract_anyhow_result_t, extract_func_type, DocExtractor, FuncType};
use quote::{format_ident, quote, ToTokens};
use syn::punctuated::Punctuated;
use syn::token::Comma;
use syn::{parse_str, Item};

pub fn process_impl(
    module_docs: Vec<String>,
    module_code: &mut proc_macro2::TokenStream,
    items: &mut Vec<Item>,
) {
    // Count the number of constructor and atomic functions for validation
    let mut readonly_atomic_macro_count = 0;
    let mut constructor_macro_count = 0;

    let mut constructor_fn_code = quote! {};
    let mut readonly_fns_code = quote! {};
    let mut atomic_fns_code = quote! {};
    let mut events_code = quote! {};

    // Traverse all items in the module
    for item in items.iter_mut() {
        match item {
            // Struct with #[glue::event] attribute or #[derive(Event)] attribute
            Item::Struct(item_struct) => {
                let mut is_event = false;
                let struct_name = &item_struct.ident;
                let mut struct_docs = Vec::new();

                // Collect struct docs
                for attr in &item_struct.attrs {
                    if let Some(doc) = attr.extract_docs() {
                        struct_docs.push(doc);
                    }

                    // Check if this is an event struct
                    let attr_path = attr.path().to_token_stream().to_string();
                    if attr_path == "event" || attr_path == "glue :: event" ||
                       (attr.path().is_ident("derive") && attr.to_token_stream().to_string().contains("Event")) {
                        is_event = true;
                    }
                }

                if is_event {
                    // Collect field information
                    let mut event_fields_code = quote! {};

                    if let syn::Fields::Named(fields) = &item_struct.fields {
                        for field in &fields.named {
                            if let Some(field_name) = &field.ident {
                                let field_type = &field.ty;
                                let mut is_topic = false;

                                // Check if field has #[topic] attribute
                                for attr in &field.attrs {
                                    if attr.path().is_ident("topic") {
                                        is_topic = true;
                                        break;
                                    }
                                }

                                event_fields_code.extend(quote! {
                                    ::glue::metadata::EventFieldSpec::new(
                                        ::core::stringify!(#field_name).to_string(),
                                        ::core::stringify!(#field_type).to_string(),
                                        #is_topic
                                    ),
                                });
                            }
                        }
                    }

                    // Generate event metadata
                    events_code.extend(quote! {
                        ::glue::metadata::EventSpec::new(::core::stringify!(#struct_name).to_string())
                            .docs(vec![#(#struct_docs.to_string()),*])
                            .fields(vec![#event_fields_code])
                            .build(),
                    });
                }
            },
            // Impl block
            Item::Impl(impl_block) => {
                // Traverse all items in the impl block
                for mut item in &mut impl_block.items {
                    match item {
                        // If the item is a function
                        syn::ImplItem::Fn(ref mut method) => {
                            // Linking impl struct name to method's name.
                            let struct_name = format_ident!(
                                "{}",
                                impl_block.self_ty.to_token_stream().to_string()
                            );
                            let fn_name = method.sig.ident.clone();
                            let export_name = format_ident!("{}", fn_name);

                            let fn_docs =
                                method.attrs.iter().filter_map(|attr| attr.extract_docs());

                            // Match method macro
                            for attr in &method.attrs {
                                let func_type = extract_func_type(attr);

                                if func_type.is_none() {
                                    continue;
                                }

                                let func_type = func_type.unwrap();

                                if func_type == FuncType::Constructor {
                                    // glue::constructor
                                    constructor_macro_count += 1;
                                    // Get old function argument list and return type
                                    let args = &method.sig.inputs;
                                    let args_names = args
                                        .iter()
                                        .map(|arg| {
                                            if let syn::FnArg::Typed(arg) = arg {
                                                arg.pat.to_token_stream()
                                            } else {
                                                quote! {}
                                            }
                                        })
                                        .collect::<Vec<_>>();
                                    let args_types = args
                                        .iter()
                                        .map(|arg| {
                                            if let syn::FnArg::Typed(arg) = arg {
                                                arg.ty.to_token_stream()
                                            } else {
                                                quote! {}
                                            }
                                        })
                                        .collect::<Vec<_>>();

                                    module_code.extend(quote! {
                                        #[glue::wasm_bind]
                                        fn #export_name(#args) {
                                            let instance = get_instance();
                                            *instance = #struct_name::new(#(#args_names),*);
                                        }
                                    });

                                    let mut block_code_string: String =
                                        method.block.to_token_stream().to_string();

                                    // Apply the changes to the block
                                    method.block = parse_str(block_code_string.as_str()).unwrap();

                                    // Generate the constructor function metadata
                                    constructor_fn_code = quote! {
                                        ::glue::metadata::FunctionSpec::from_label(::core::stringify!(#fn_name).to_string())
                                            .readonly(false)
                                            .args(vec![
                                                #(
                                                    ::glue::metadata::FunctionParamSpec::new(::core::stringify!(#args_names).to_string(), ::core::stringify!(#args_types).to_string()),
                                                )*
                                            ])
                                            .return_type(::glue::metadata::FunctionReturnTypeSpec::new("Self".to_string()))
                                            .docs(vec![#(#fn_docs.to_string()),*])
                                            .build()
                                    };

                                    break;
                                } else if func_type == FuncType::Atomic
                                    || func_type == FuncType::Readonly
                                {
                                    // glue::atomic and glue::readonly
                                    readonly_atomic_macro_count += 1;
                                    // Get args without self
                                    let args = &method
                                        .sig
                                        .inputs
                                        .iter()
                                        .skip(1)
                                        .collect::<Punctuated<_, Comma>>();

                                    let args_names = args
                                        .iter()
                                        .map(|arg| {
                                            if let syn::FnArg::Typed(arg) = arg {
                                                arg.pat.to_token_stream()
                                            } else {
                                                quote! {}
                                            }
                                        })
                                        .collect::<Vec<_>>();

                                    let args_types = args
                                        .iter()
                                        .map(|arg| {
                                            if let syn::FnArg::Typed(arg) = arg {
                                                arg.ty.to_token_stream()
                                            } else {
                                                quote! {}
                                            }
                                        })
                                        .collect::<Vec<_>>();

                                    let fn_output = &method.sig.output;

                                    let fn_return_type = if let syn::ReturnType::Type(_, ty) =
                                        fn_output
                                    {
                                        extract_anyhow_result_t(&ty.to_token_stream().to_string())
                                    } else {
                                        "()".to_string()
                                    };

                                    let atomic_fn = quote! {
                                        #[glue::wasm_bind]
                                        pub extern fn #export_name(#args) #fn_output {
                                            get_instance().#fn_name(#(#args_names),*)
                                        }
                                    };
                                    module_code.extend(atomic_fn);

                                    // Generate the readonly or atomic function metadata
                                    let readonly = func_type == FuncType::Readonly;
                                    let fn_metadata_code = quote! {
                                        ::glue::metadata::FunctionSpec::from_label(::core::stringify!(#fn_name).to_string())
                                            .readonly(#readonly)
                                            .args(vec![
                                                #(
                                                    ::glue::metadata::FunctionParamSpec::new(::core::stringify!(#args_names).to_string(), ::core::stringify!(#args_types).to_string()),
                                                )*
                                            ])
                                            .return_type(::glue::metadata::FunctionReturnTypeSpec::new(#fn_return_type.to_string()))
                                            .docs(vec![#(#fn_docs.to_string()),*])
                                            .build(),
                                    };

                                    if func_type == FuncType::Readonly {
                                        readonly_fns_code.extend(fn_metadata_code);
                                    } else if func_type == FuncType::Atomic {
                                        atomic_fns_code.extend(fn_metadata_code);
                                    }

                                    break;
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }

    // Generate the metadata code for the contract
    module_code.extend(quote! {
        const _: () = {
            #[cfg(not(target_arch = "wasm32"))]
            #[no_mangle]
            pub fn __glue_generate_metadata() -> ::glue::metadata::GlueProject  {
                // TODO: storage layout
                let contract_spec = ::glue::metadata::ContractSpec::new()
                    .constructor(#constructor_fn_code)
                    .readonly_functions(vec![#readonly_fns_code])
                    .atomic_functions(vec![#atomic_fns_code])
                    .events(vec![#events_code])
                    .docs(vec![#(#module_docs.to_string()),*])
                    .build();

                ::glue::metadata::GlueProject::new(contract_spec)
            }
        };


    });

    // Validate the number of constructor and atomic functions
    // Check if there are more than one constructor macro
    if constructor_macro_count > 1 {
        panic!("Only one constructor function is allowed in a contract");
    } else if constructor_macro_count == 0 {
        panic!("One constructor function is required in the contract");
    }
}
