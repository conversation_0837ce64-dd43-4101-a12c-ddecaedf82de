import json
import os
import re

from kivy.logger import <PERSON><PERSON>

from config.config import CONFIG

_LOGGER_TITLE = "SwaggerGen"


def findLibRsFiles(base_path):
    """
    Traverse the base_path directory to find all lib.rs files.
    """
    lib_rs_files = []
    for root, _dirs, files in os.walk(base_path):
        if "lib.rs" in files:
            lib_rs_files.append(os.path.join(root, "lib.rs"))
    Logger.debug(f"Found {len(lib_rs_files)} lib.rs file(s):")
    for f in lib_rs_files:
        Logger.debug(f"  - {f}")
    return lib_rs_files


def extractImplBlock(content, struct_name):
    """
    Extract the entire impl block for a given struct, accounting for nested braces.
    Returns the content inside the impl block or None if not found.
    """
    # Pattern to find the start of the impl block
    pattern = rf"impl\s+{struct_name}\s*{{"
    match = re.search(pattern, content)
    if not match:
        return None

    start = match.end()
    brace_count = 1
    pos = start
    while brace_count > 0 and pos < len(content):
        if content[pos] == "{":
            brace_count += 1
        elif content[pos] == "}":
            brace_count -= 1
        pos += 1

    if brace_count == 0:
        impl_block = content[start : pos - 1]
        return impl_block
    else:
        # Unbalanced braces
        return None


def parseLibRs(file_path):
    """
    Parse a lib.rs file to extract structs and their associated functions along with argument types.
    """
    Logger.debug(f"Parsing file: {file_path}")
    with open(file_path, "r", encoding="utf-8") as file:
        content = file.read()

    # Step 1: Find structs with #[glue::storage]
    struct_pattern = re.compile(r"#\[\s*glue::storage\s*\]\s*pub\s+struct\s+(\w+)\s*{", re.MULTILINE)
    structs = struct_pattern.findall(content)
    Logger.info(f"{_LOGGER_TITLE}:  Found {len(structs)} struct(s) with #[glue::storage]: {structs}")

    parsed_data = []

    for struct in structs:
        Logger.debug(f"  Processing struct: {struct}")
        # Step 2: Extract the entire impl block for the struct
        impl_block = extractImplBlock(content, struct)
        if not impl_block:
            Logger.debug(f"    No impl block found for struct '{struct}'. Skipping.")
            continue
        Logger.debug(f"    Found impl block for struct '{struct}'.")

        # Step 3: Find all functions with any #[glue::*] annotation within impl block
        # This regex accounts for multi-line function signatures and complex return types
        func_pattern = re.compile(
            r"#\[\s*glue::(?P<attribute>\w+)\s*\]\s*pub\s+fn\s+(\w+)\s*\((.*?)\)\s*->\s*([\w:<>,\s\(\)]+)\s*\{",
            re.DOTALL,
        )
        functions = func_pattern.findall(impl_block)
        Logger.debug(f"    Found {len(functions)} function(s) with #[glue::*]: {[f[1] for f in functions]}")

        for func in functions:
            attribute_type, function_name, args_str, return_type = func
            Logger.debug(
                f"      Function: {function_name}, Annotation: {attribute_type}, Args: '{args_str}', Return Type: {return_type}"
            )

            # Step 4: Extract argument names and types, excluding self references
            args = []
            if args_str.strip():
                # Split arguments by comma, handle possible patterns
                arg_list = args_str.split(",")
                for arg in arg_list:
                    arg = arg.strip()
                    # Ignore self references
                    if arg.startswith("&self") or arg.startswith("self"):
                        continue
                    # Extract argument name and type
                    arg_match = re.match(r"(\w+)\s*:\s*([\w:<>,\s\(\)]+)", arg)
                    if arg_match:
                        arg_name = arg_match.group(1)
                        arg_type = arg_match.group(2).strip()
                        args.append({"name": arg_name, "type": arg_type})
            Logger.debug(f"        Extracted args: {args}")

            parsed_data.append(
                {
                    "struct_name": struct,
                    "function_name": function_name,
                    "annotation": attribute_type.lower(),  # e.g., 'readonly', 'atomic', 'constructor'
                    "args": args,
                    "return_type": return_type.strip(),
                }
            )

    return parsed_data


def compileContractInfo(lib_rs_files, base_path):
    """
    Compile all contract information from the found lib.rs files.
    """
    system_contract_packages = getSystemContractPackages()
    all_contracts = []
    for file in lib_rs_files:
        # Determine package name from the path
        # Assuming the path is base_path/package_name/src/lib.rs
        relative_path = os.path.relpath(file, base_path)
        parts = relative_path.split(os.sep)
        if len(parts) < 2:
            Logger.debug(f"    Warning: Unexpected lib.rs path format: {file}")
            package_name = "unknown"
        else:
            package_name = parts[0]  # package_name is the first part after base_path
        Logger.debug(f"compileContractInfo: Processing package: {package_name}")
        if package_name.replace("-", "_") not in system_contract_packages:
            Logger.debug(f"compileContractInfo: Skipping non-system contract: {package_name}")
            continue
        contract_info = parseLibRs(file)
        for contract in contract_info:
            all_contracts.append(
                {
                    "package_name": package_name,
                    "struct_name": contract["struct_name"],
                    "function_name": contract["function_name"],
                    "annotation": contract["annotation"],
                    "args": contract["args"],
                    # 'return_type': contract['return_type']  # Optional: Include if needed
                }
            )
    Logger.info(f"{_LOGGER_TITLE}: Total contracts extracted: {len(all_contracts)}")
    return all_contracts


def loadContractAddresses(config_file):
    """
    Load contract addresses from a JSON configuration file.
    """
    if not os.path.isfile(config_file):
        Logger.debug(f"Warning: Config file '{config_file}' not found. 'contract_address' fields will remain empty.")
        return {}
    with open(config_file, "r", encoding="utf-8") as f:
        data = json.load(f)
        # Convert list of contract objects to dictionary with contract_name as key
        # There is a mismatch in the current contract names and the contract names in the JSON file, the "_" is replaced with "-"
        contract_addresses = {str(item["contract_name"]).replace("_", "-"): item["contract_address"] for item in data}
        # save it as contract_addresses.json
        filename = "contract_addresses.json"
        # Check if current cwd is in swaggerapi, if not, output file will be in swaggerapi
        if os.getcwd().split(os.sep)[-1] != "swaggerapi":
            filename = os.path.join("swaggerapi", "contract_addresses.json")
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(contract_addresses, f, indent=4)
        return contract_addresses


def getSystemContractPackages():
    """
    Read system_contracts.json based on CONFIG.systemContractAddressPath and extract all package names.

    Returns:
        list: A list of package names (contract_name) from the system contracts configuration.
    """
    system_contract_file_path = fileCheck(CONFIG.systemContractAddressPath)
    if not system_contract_file_path:
        Logger.error(f"Error: System contract configuration file not found at {CONFIG.systemContractAddressPath}")
        return []

    try:
        with open(system_contract_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            # Extract all contract_names and replace "-" with "_" to match package naming convention
            package_names = [str(item["contract_name"]).replace("-", "_") for item in data]
            Logger.debug(f"{_LOGGER_TITLE}: Found system contract packages: {package_names}")
            return package_names
    except (json.JSONDecodeError, KeyError) as e:
        Logger.error(f"Error parsing system contract configuration: {str(e)}")
        return []
    except Exception as e:
        Logger.error(f"Unexpected error reading system contract configuration: {str(e)}")
        return []


def generateJsonrpcQueries(contracts, contract_addresses):
    """
    Generate JSON-RPC queries based on the extracted contracts and contract addresses, including argument types.
    """
    queries = []
    query_id = 1
    for contract in contracts:
        address = contract_addresses.get(contract["package_name"], "")
        # Determine JSON-RPC method based on function annotation
        if contract["annotation"] == "readonly":
            method = "contract.query"
        elif contract["annotation"] in ("atomic", "constructor"):
            method = "contract.execute"
        else:
            # Default method if annotation is unrecognized
            method = "contract.execute"
            Logger.debug(
                f"      Warning: Unrecognized annotation '{contract['annotation']}' for function '{contract['function_name']}'. Using 'contract.execute' method."
            )

        # Prepare args with types for potential use in model generation
        args_with_types = [{"name": arg["name"], "type": arg["type"]} for arg in contract["args"]]

        query = {
            "jsonrpc": "2.0",
            "method": method,
            "params": [
                {
                    "contract_address": address,
                    "package_name": contract["package_name"],
                    "contract_name": contract["package_name"],  # Assuming contract name is same as package
                    "struct_name": contract["struct_name"],
                    "function_name": contract["function_name"],
                    "args": args_with_types,  # Includes argument types
                    "signatures": [],
                    "publickeys": [],
                    "privatekey": "",  # String private key
                    # "privatekey": [],
                    "fuel": 0,
                }
            ],
            "id": query_id,
        }
        queries.append(query)
        query_id += 1
    return queries


def directoryCheck(baseDir):
    """
    Check if the base directory exists, if not, check if adding ../ to the path will make it exist
    """
    if not os.path.isdir(baseDir):
        baseDirectory = os.path.join("..", baseDir)
        if not os.path.isdir(baseDirectory):
            Logger.error(f"Error: Base directory '{baseDirectory}' does not exist.")
            return None
        else:
            return baseDirectory
    else:
        return baseDir


def fileCheck(filePath):
    """
    Check if the file exists, if not, check if adding ../ to the path will make it exist
    """
    if not os.path.isfile(filePath):
        file = os.path.join("..", filePath)
        if not os.path.isfile(file):
            Logger.error(f"Error: File '{file}' does not exist.")
            return None
        else:
            return file
    else:
        return filePath


def main():
    baseDirectory = directoryCheck(CONFIG.contractSourcePath)
    if baseDirectory is None:
        return

    # Logger.debug the current working directory
    Logger.debug(f"Current working directory: {os.getcwd()}")

    if not os.path.isdir(baseDirectory):
        Logger.debug(f"Error: Base directory '{baseDirectory}' does not exist.")
        return

    lib_rs_files = findLibRsFiles(baseDirectory)
    if not lib_rs_files:
        Logger.debug("No lib.rs files found. Please check the base_directory path and directory structure.")
        return

    contracts = compileContractInfo(lib_rs_files, baseDirectory)
    if not contracts:
        Logger.debug("No contracts extracted. Please ensure that structs and functions are properly annotated.")
        return

    # Load contract addresses
    system_contract_file_path = fileCheck(CONFIG.systemContractAddressPath)  # Path can be set manually here
    contract_addresses = loadContractAddresses(system_contract_file_path)

    jsonrpc_queries = generateJsonrpcQueries(contracts, contract_addresses)

    # Save to JSON file
    output_file = "jsonrpc_queries.json"
    # Check if current cwd is in swaggerapi, if not, output file will be in swaggerapi
    if os.path.basename(os.getcwd()) != "swaggerapi":
        output_file = os.path.join("swaggerapi", "jsonrpc_queries.json")

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(jsonrpc_queries, f, indent=4)

    Logger.info(f"{_LOGGER_TITLE}: Generated {len(jsonrpc_queries)} JSON-RPC query(s) and saved to '{output_file}'.")


if __name__ == "__main__":
    main()
