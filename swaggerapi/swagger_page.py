import asyncio
import copy
import itertools
import json
import logging.handlers
import os
import tempfile
from typing import Any, Dict, List, Optional, Union

from fastapi import APIRouter, FastAPI, File, Form, HTTPException, UploadFile
from fastapi.responses import JSONResponse, RedirectResponse
from pydantic import BaseModel, create_model

from common.utils import detectWasiFromBytes
from config.config import CONFIG
from swaggerapi.utils import (
    cleanList,
    getContractHexBytecode,
    getHostAndPort,
    loadJsonFile,
    mapRustType,
    readUntilNewline,
)

# ================================
# Initialization
# ================================

app = FastAPI(title="VGraph Swagger API", version="1.0.0", swagger_ui_parameters={"defaultModelsExpandDepth": -1})

# Ensure logs directory exists
if not os.path.exists("logs"):
    os.makedirs("logs")

# Configure Swagger Logger
swaggerLogger = logging.getLogger("uvicorn.error")
fileHandler = logging.handlers.RotatingFileHandler(
    "logs/swagger_api.log",
    maxBytes=10 * 1024 * 1024,  # 10MB per file
    backupCount=10,
)
formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
fileHandler.setFormatter(formatter)
swaggerLogger.addHandler(fileHandler)
swaggerLogger.propagate = False

# Configure Access Logger
accessLogger = logging.getLogger("uvicorn.access")
accessLogger.handlers.clear()
accessLogger.addHandler(fileHandler)

# Logging Title
_LOGGER_TITLE = "Swagger:"

# TCP JSON-RPC Server Details
TCP_JSONRPC_HOST, TCP_JSONRPC_PORT = getHostAndPort(CONFIG.localAddress[0], defaultHost="localhost", defaultPort=9877)

# Initialize Request ID Counter
requestIdCounter = itertools.count(1)

# ================================
# Load Configuration Files
# ================================

# Load Contract Addresses
contractAddresses = loadJsonFile("swaggerapi/contract_addresses.json", swaggerLogger, _LOGGER_TITLE)
jsonrpcQueries = loadJsonFile("swaggerapi/jsonrpc_queries.json", swaggerLogger, _LOGGER_TITLE)
fixedQueries = loadJsonFile("swaggerapi/fixed_queries.json", swaggerLogger, _LOGGER_TITLE)


# ================================
# Helper Functions
# ================================


def createRequestModel(functionName: str, jsonrpcMethod: str, args: List[Dict[str, str]]) -> BaseModel:
    """
    Dynamically creates a Pydantic model for a given function based on its arguments and types.

    :param functionName: Name of the function for which the model is being created.
    :param args: A list of dictionaries, each containing 'name' and 'type' keys for function arguments.
    :return: A Pydantic BaseModel subclass with fields corresponding to the function arguments.
    """
    fields = {"contract_address": (Optional[str], None)}

    for arg in args:
        arg_name = arg["name"]
        arg_type = arg["type"]
        python_type = mapRustType(arg_type)
        fields[arg_name] = (python_type, ...)  # Required field

    # Add common fields with appropriate types
    fields["signatures"] = (Optional[List[str]], [])
    fields["publickeys"] = (Optional[List[str]], [])
    fields["privatekey"] = (str, "")  # String for private key not list
    fields["fuel"] = (int, 0)

    if jsonrpcMethod == "contract.execute":
        fields["dependent_transaction_hash"] = (Optional[str], None)
    elif jsonrpcMethod == "contract.query":
        fields["snapshot_transaction_hash"] = (Optional[str], None)
        fields["query_api_key"] = (Optional[str], None)

    # Create the Pydantic model dynamically
    return create_model(f"{functionName}_Request", **fields)


async def sendJsonrpcMessage(method: str, params: Union[dict, str, tuple]) -> Optional[Dict[str, Any]]:
    """
    Asynchronous function to send JSON-RPC messages over TCP.

    :param method: JSON-RPC method name.
    :param params: Parameters for the JSON-RPC method.
    :return: The result from the JSON-RPC response.
    """
    requestId = next(requestIdCounter)  # Get the next request ID
    try:
        reader, writer = await asyncio.open_connection(TCP_JSONRPC_HOST, TCP_JSONRPC_PORT)
        swaggerLogger.info(f"{_LOGGER_TITLE} Connected to JSON-RPC server at {TCP_JSONRPC_HOST}:{TCP_JSONRPC_PORT}")
    except Exception as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Connection error: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Unable to connect to JSON-RPC server: {str(e)}") from e

    # Construct the JSON-RPC payload
    payload = {
        "jsonrpc": "2.0",
        "method": method,
        "params": [params],  # params should be a list containing a single dict, but may be not for fixedQueries
        "id": requestId,
    }
    if isinstance(params, tuple):
        payload["params"] = list(params)

    filteredPayload = copy.deepcopy(payload)
    # Remove sensitive fields from the log
    # Check if filteredPayload["params"][0] is a dict
    if isinstance(filteredPayload["params"][0], dict):
        filteredPayload["params"][0].pop("query_api_key", None)

    # Serialize and send the message with a newline delimiter
    serializedMessage = json.dumps(payload) + "\n"
    writer.write(serializedMessage.encode())
    await writer.drain()

    filteredSerializedMessage = json.dumps(filteredPayload) + "\n"
    swaggerLogger.info(f"{_LOGGER_TITLE} Sent JSON-RPC request: {filteredSerializedMessage.strip()}")

    try:
        # Await the response until the newline delimiter with a timeout
        response = await readUntilNewline(reader)
        swaggerLogger.info(f"{_LOGGER_TITLE} Received response: {response.decode().strip()}")
    except asyncio.TimeoutError as e:
        writer.close()
        await writer.wait_closed()
        swaggerLogger.error(f"{_LOGGER_TITLE} JSON-RPC request timed out.")
        raise HTTPException(status_code=504, detail="JSON-RPC request timed out.") from e

    writer.close()
    await writer.wait_closed()

    try:
        responseJson = json.loads(response.decode())
        if "error" in responseJson:
            swaggerLogger.error(f"{_LOGGER_TITLE} JSON-RPC Error: {responseJson['error']}")
            raise HTTPException(status_code=400, detail=responseJson["error"]["message"]) from None

        # Parse the 'result' field if it's a JSON string
        result = responseJson.get("result", {})
        if isinstance(result, str):
            try:
                # Attempt to parse the result if it's a JSON-encoded string
                result = json.loads(result)
            except json.JSONDecodeError:
                swaggerLogger.error(f"{_LOGGER_TITLE} Result is not a valid JSON-encoded string.")
                raise HTTPException(status_code=502, detail="Invalid JSON-RPC result format.") from None

        return result
    except json.JSONDecodeError as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Invalid JSON-RPC response.")
        raise HTTPException(status_code=502, detail="Invalid JSON-RPC response.") from e


def createContractEndpoint(functionName: str, jsonrpcMethod: str, paramsInfo: Dict[str, Any]):
    """
    Dynamically creates a contract endpoint.

    :param functionName: Name of the function.
    :param jsonrpcMethod: JSON-RPC method to call.
    :param paramsInfo: Information about the parameters.
    :return: The endpoint function.
    """
    request_model_class = createRequestModel(functionName, jsonrpcMethod, paramsInfo["args"])

    async def endpoint(request: request_model_class):
        # Extract contract_address and other params
        contractAddress = request.contract_address or paramsInfo.get("contract_address")
        if not contractAddress:
            raise HTTPException(status_code=400, detail="Contract address is required and could not be auto-filled.")

        # Collect function-specific arguments based on 'args' list from query_params
        argsList = [getattr(request, arg["name"]) for arg in paramsInfo.get("args", [])]

        # Collect extra fields
        signatures = request.signatures
        publickeys = request.publickeys
        privateKey = request.privatekey

        # Construct the params dict with required fields
        params = {
            "contract_address": contractAddress,
            "function_name": paramsInfo["function_name"],
            "args": argsList,
            "signatures": signatures,
            "publickeys": publickeys,
            "privatekey": privateKey,
        }

        if jsonrpcMethod == "contract.execute":
            if request.dependent_transaction_hash:
                params["dependent_transaction_hash"] = request.dependent_transaction_hash
        elif jsonrpcMethod == "contract.query":
            if request.snapshot_transaction_hash:
                params["snapshot_transaction_hash"] = request.snapshot_transaction_hash

            if request.query_api_key:
                params["query_api_key"] = request.query_api_key
        # set 'fuel' parameter for contract.execute method
        # not set fuel for contract.query method
        if jsonrpcMethod == "contract.execute":
            params["fuel"] = request.fuel

        response = await sendJsonrpcMessage(jsonrpcMethod, params)
        # Ensure that response is a dict, return {} if None
        if response is None:
            return {}
        return response

    endpoint.__name__ = f"post_{functionName}"
    return endpoint


def createFixedEndpoint(method_name: str, jsonrpc_method: str):
    """
    Dynamically creates a fixed endpoint.

    :param method_name: Name of the method.
    :param jsonrpc_method: JSON-RPC method to call.
    :return: The endpoint function.
    """

    class FixedRequest(BaseModel):
        param: Any

    async def endpoint(request: FixedRequest):
        response = await sendJsonrpcMessage(jsonrpc_method, request.param)
        if response is None:
            return {}
        return response

    endpoint.__name__ = f"post_{method_name}"
    return endpoint


# ================================
# Routes
# ================================


# Root Redirect
@app.get("/", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")


# Endpoint to get block data by block ID
@app.get(
    "/block/{blockHash}",
    response_model=Optional[Dict[str, Any]],
    tags=["block"],
)
async def getBlockByHash(blockHash: str, includeFullTransactions: bool = True):
    """
    Get raw block data by block ID
    """
    params = {
        "jsonrpc": "2.0",
        "method": "blocktree.get_block_by_hash",
        "params": (blockHash, includeFullTransactions),
        "id": next(requestIdCounter),
    }
    response = await sendJsonrpcMessage(params["method"], params["params"])
    if response is None:
        raise HTTPException(status_code=404, detail="Block not found.")
    return JSONResponse(content=response)


# Endpoint to get block data by block height
@app.get(
    "/block/height/{height}",
    response_model=Optional[Dict[str, Any]],
    tags=["block"],
)
async def getBlockByHeight(height: int, includeFullTransactions: bool = True):
    """
    Get raw block data by block height
    """
    params = {
        "jsonrpc": "2.0",
        "method": "blocktree.get_block_by_height",
        "params": (height, includeFullTransactions),
        "id": next(requestIdCounter),
    }
    response = await sendJsonrpcMessage(params["method"], params["params"])
    if response is None:
        raise HTTPException(status_code=404, detail="Block not found.")
    return JSONResponse(content=response)


# Endpoint to get transaction data by transaction hash
@app.get(
    "/transaction/{transactionHash}",
    response_model=Optional[Dict[str, Any]],
    tags=["transaction"],
    include_in_schema=False,
)
async def getTransactionByHash(transactionHash: str):
    """
    Get raw transaction data by transaction hash
    """
    params = {
        "jsonrpc": "2.0",
        "method": "transaction.get_transaction_by_hash",
        "params": [transactionHash],
        "id": next(requestIdCounter),
    }
    response = await sendJsonrpcMessage(params["method"], params["params"][0])
    if response is None:
        raise HTTPException(status_code=404, detail="Transaction not found.")
    return JSONResponse(content=response)


# Endpoint to get receipt data by receipt hash
@app.get("/receipt/{receiptHash}", response_model=Optional[Dict[str, Any]], tags=["receipt"], include_in_schema=False)
async def getReceipt(receiptHash: str):
    """
    Get raw receipt data by receipt hash
    """
    params = {
        "jsonrpc": "2.0",
        "method": "transaction.get_receipt",
        "params": [receiptHash],
        "id": next(requestIdCounter),
    }
    response = await sendJsonrpcMessage(params["method"], params["params"][0])
    if response is None:
        raise HTTPException(status_code=404, detail="Receipt not found.")
    return JSONResponse(content=response)


# ================================
# Fixed Endpoints Router
# ================================

fixedRouter = APIRouter()

# Dynamically create fixed endpoints
for fixedQuery in fixedQueries:
    methodName = fixedQuery["method"].replace(".", "_")
    jsonrpcMethod = fixedQuery["method"]
    endpointFn = createFixedEndpoint(methodName, jsonrpcMethod)
    fixedRouter.post(f"/{methodName}", response_model=Optional[Dict[str, Any]])(endpointFn)


# Register Contract Endpoint
@fixedRouter.post(
    "/register_contract",
    response_model=Optional[Dict[str, Any]],
    tags=["fixed"],
    summary="Create a new contract with uploaded bytecode",
    description="Uploads contract bytecode and other parameters to create a new contract via JSON-RPC.",
)
async def postContractRegister(
    contractSourceUrl: str = Form(..., description="URL where the contract source code is hosted"),
    upgradable: bool = Form(..., description="Whether the contract is upgradable"),
    gitCommitHash: str = Form(..., description="Git commit hash of the contract source code"),
    reproducibleBuild: bool = Form(..., description="Whether the contract build is reproducible"),
    fuel: int = Form(..., description="Fuel to register the contract"),
    privatekey: str = Form(..., description="Private key for signing the transaction"),
    signatures: Optional[List[str]] = None,
    publicKeys: Optional[List[str]] = None,
    constructorParameters: Optional[List[str]] = None,
    contractHexBytecode: Optional[UploadFile] = None,
):
    """
    Endpoint to create a new contract by uploading the contract's hex bytecode and providing necessary parameters.
    """
    # Assign default values for the optional parameters
    signatures = cleanList(signatures)
    publicKeys = cleanList(publicKeys)
    constructorParameters = cleanList(constructorParameters)
    contractHexBytecode = contractHexBytecode or File(..., description="Hex bytecode file for the contract")

    swaggerLogger.info(f"{_LOGGER_TITLE} Received contract creation request.")

    tmpFilePath = None  # Initialize variable to store temporary file path

    try:
        # Create a temporary file to save the uploaded bytecode
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmpFilePath = tmp.name
            # Write the uploaded file's content to the temporary file
            content = await contractHexBytecode.read()
            if not content:
                swaggerLogger.error(f"{_LOGGER_TITLE} Uploaded file is empty.")
                raise HTTPException(status_code=400, detail="Uploaded file is empty.")
            tmp.write(content)
            swaggerLogger.info(f"{_LOGGER_TITLE} Uploaded bytecode saved to temporary file {tmpFilePath}.")

        # Use the provided function to get the hex bytecode
        contractHexBytecodeStr = getContractHexBytecode(tmpFilePath)
        contractHexBytecodeBytes = bytes.fromhex(contractHexBytecodeStr)
        if detectWasiFromBytes(contractHexBytecodeBytes):
            swaggerLogger.error(f"{_LOGGER_TITLE} wasi in wat string, Invalid Contract Bytecode.")
            raise HTTPException(status_code=400, detail="Invalid Contract Bytecode.")
        else:
            swaggerLogger.info(f"{_LOGGER_TITLE} Successfully converted uploaded bytecode to wat string.")
        swaggerLogger.info(f"{_LOGGER_TITLE} Successfully converted uploaded bytecode to hex string.")

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Error processing uploaded file: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process the uploaded bytecode file.") from e
    finally:
        # Ensure the temporary file is deleted
        if tmpFilePath and os.path.exists(tmpFilePath):
            try:
                os.remove(tmpFilePath)
                swaggerLogger.info(f"{_LOGGER_TITLE} Temporary file {tmpFilePath} deleted.")
            except Exception as e:
                swaggerLogger.warning(f"{_LOGGER_TITLE} Could not delete temporary file {tmpFilePath}: {str(e)}")

    # Construct the JSON-RPC parameters
    params = {
        "contract_hex_bytecode": contractHexBytecodeStr,
        "constructor_parameters": constructorParameters,
        "contract_source_url": contractSourceUrl,
        "upgradable": upgradable,
        "git_commit_hash": gitCommitHash,
        "reproducible_build": reproducibleBuild,
        "fuel": fuel,
        "signatures": signatures,
        "public_keys": publicKeys,
        "privatekey": privatekey,
    }

    swaggerLogger.debug(f"{_LOGGER_TITLE} Constructed JSON-RPC params: {params}")

    # Send the JSON-RPC message
    try:
        response = await sendJsonrpcMessage("contract.create", params)
        swaggerLogger.info(f"{_LOGGER_TITLE} Successfully received response from JSON-RPC server.")
    except HTTPException as http_exc:
        swaggerLogger.error(f"{_LOGGER_TITLE} JSON-RPC request failed: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Unexpected error during JSON-RPC communication: {str(e)}")
        raise HTTPException(status_code=500, detail="Unexpected error during contract creation.") from e

    return JSONResponse(content=response)


# Upgrade Contract Endpoint
@fixedRouter.post(
    "/upgrade_contract",
    response_model=Optional[Dict[str, Any]],
    tags=["fixed"],
    summary="Upgrade a contract with uploaded bytecode",
    description="Uploads contract bytecode and other parameters to upgrade a contract via JSON-RPC.",
)
async def postContractUpgrade(
    contractSourceUrl: str = Form(..., description="URL where the contract source code is hosted"),
    contractAddress: str = Form(..., description="contract_address"),
    gitCommitHash: str = Form(..., description="Git commit hash of the contract source code"),
    reproducibleBuild: bool = Form(..., description="Whether the contract build is reproducible"),
    fuel: int = Form(..., description="Fuel to register the contract"),
    privatekey: str = Form(..., description="Private key for signing the transaction"),
    signatures: Optional[List[str]] = None,
    publicKeys: Optional[List[str]] = None,
    contractHexBytecode: Optional[UploadFile] = None,
):
    """
    Endpoint to upgrade a contract by uploading the contract's hex bytecode and providing necessary parameters.
    """
    # Assign default values for the optional parameters
    signatures = cleanList(signatures)
    publicKeys = cleanList(publicKeys)
    contractHexBytecode = contractHexBytecode or File(..., description="Hex bytecode file for the contract")

    swaggerLogger.info(f"{_LOGGER_TITLE} Received contract creation request.")

    tmpFilePath = None  # Initialize variable to store temporary file path

    try:
        # Create a temporary file to save the uploaded bytecode
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmpFilePath = tmp.name
            # Write the uploaded file's content to the temporary file
            content = await contractHexBytecode.read()
            if not content:
                swaggerLogger.error(f"{_LOGGER_TITLE} Uploaded file is empty.")
                raise HTTPException(status_code=400, detail="Uploaded file is empty.")
            tmp.write(content)
            swaggerLogger.info(f"{_LOGGER_TITLE} Uploaded bytecode saved to temporary file {tmpFilePath}.")

        # Use the provided function to get the hex bytecode
        contractHexBytecodeStr = getContractHexBytecode(tmpFilePath)
        swaggerLogger.info(f"{_LOGGER_TITLE} Successfully converted uploaded bytecode to hex string.")

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Error processing uploaded file: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process the uploaded bytecode file.") from e
    finally:
        # Ensure the temporary file is deleted
        if tmpFilePath and os.path.exists(tmpFilePath):
            try:
                os.remove(tmpFilePath)
                swaggerLogger.info(f"{_LOGGER_TITLE} Temporary file {tmpFilePath} deleted.")
            except Exception as e:
                swaggerLogger.warning(f"{_LOGGER_TITLE} Could not delete temporary file {tmpFilePath}: {str(e)}")

    # Construct the JSON-RPC parameters
    params = {
        "contract_hex_bytecode": contractHexBytecodeStr,
        "contract_address": contractAddress,
        "contract_source_url": contractSourceUrl,
        "git_commit_hash": gitCommitHash,
        "reproducible_build": reproducibleBuild,
        "fuel": fuel,
        "signatures": signatures,
        "public_keys": publicKeys,
        "privatekey": privatekey,
    }

    swaggerLogger.debug(f"{_LOGGER_TITLE} Constructed JSON-RPC params: {params}")

    # Send the JSON-RPC message
    try:
        response = await sendJsonrpcMessage("contract.upgrade", params)
        swaggerLogger.info(f"{_LOGGER_TITLE} Successfully received response from JSON-RPC server.")
    except HTTPException as http_exc:
        swaggerLogger.error(f"{_LOGGER_TITLE} JSON-RPC request failed: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        swaggerLogger.error(f"{_LOGGER_TITLE} Unexpected error during JSON-RPC communication: {str(e)}")
        raise HTTPException(status_code=500, detail="Unexpected error during contract creation.") from e

    return JSONResponse(content=response)


# Add fixed router to the main FastAPI app
app.include_router(fixedRouter, prefix="/fixed", tags=["fixed"])

# ================================
# Contract Routers
# ================================

# Group endpoints by contract name
contractRouters = {}
for query in jsonrpcQueries:
    paramsInfo = query["params"][0]
    contractName = paramsInfo["contract_name"]
    functionName = paramsInfo["function_name"]
    jsonrpcMethod = query["method"]

    # Skip unrecognized methods
    if jsonrpcMethod not in {"contract.execute", "contract.query"}:
        swaggerLogger.info(
            f"{_LOGGER_TITLE} Skipping unrecognized method '{jsonrpcMethod}' for function '{functionName}'."
        )

    # Create a new APIRouter for each contract if it doesn't already exist
    if contractName not in contractRouters:
        contractRouters[contractName] = APIRouter()

    args = paramsInfo.get("args", [])
    requestModel = createRequestModel(functionName, jsonrpcMethod, args)

    # Define the endpoint path
    endpoint = f"/{functionName}"

    # Define the FastAPI route dynamically
    endpointFn = createContractEndpoint(functionName, jsonrpcMethod, paramsInfo)
    contractRouters[contractName].post(endpoint, response_model=Optional[Dict[str, Any]])(endpointFn)


# Add each contract's router to the main FastAPI app, grouping them by contract name
for contractName, router in contractRouters.items():
    app.include_router(router, prefix=f"/{contractName}", tags=[contractName])
