WASM_FILE_NAME := $(shell basename `pwd` | sed 's/-/_/g')
MOVE_DIR := ../..


all:
	@if ! which cargo-vcontract > /dev/null; then \
		echo "cargo-vcontract is not installed. Please install it first."; \
		exit 1; \
	fi
	cargo vcontract build --generate=code-only && mv target/glue/$(WASM_FILE_NAME).wasm $(MOVE_DIR)
	@echo "build and move $(WASM_FILE_NAME) finished"

clean:
	@rm -f $(MOVE_DIR)/$(WASM_FILE_NAME).wasm
	@cargo clean
	@echo "clean $(WASM_FILE_NAME) success"

cargo-clean:
	@cargo clean
	@echo "cargo clean $(WASM_FILE_NAME) success"

test:
	# filter the test which name contains "test"
	@printf "cargo test $(WASM_FILE_NAME)"
	@cargo test test -q

.PHONY: all clean cargo-clean test
