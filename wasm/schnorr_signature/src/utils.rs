use rand::{rngs::OsRng, rngs::StdRng, SeedableRng};
use sha2::{Digest, Sha256};

use schnorrkel::{Keypair, <PERSON>Key};

/// Generates a cryptographic key pair using a hashed version of the provided seed.
///
/// The function takes any byte slice as a seed, hashes it using SHA-256 to create a
/// fixed-length (32-byte) array, and then uses this hashed seed to generate a Schnorrkel
/// key pair. This approach allows for a variable-length input seed while ensuring the
/// seed used for key generation is of the appropriate length and format.
///
/// # Arguments
///
/// * `seed` - A byte slice (`&[u8]`) that will be used as the seed for the key generation.
///   The seed can be of any length.
///
/// # Returns
///
/// A `Keypair` consisting of a public and private key, generated using the hashed seed.
///
/// # Panics
///
/// Panics if the SHA-256 hash algorithm output does not match the expected 32-byte size.
/// This is highly unlikely unless there is a change in the SHA-256 algorithm's implementation.
///
/// This function is suitable for scenarios where deterministic key generation is required,
/// such as in certain cryptographic protocols or blockchain technologies, where the same
/// seed always produces the same key pair.
pub fn generate_keypair_with_seed(seed: &[u8]) -> Keypair {
    // Hash the seed to create a 32-byte array
    let mut hasher = Sha256::new();
    hasher.update(seed);
    let seed_hash = hasher.finalize();
    let seed_array: [u8; 32] = seed_hash
        .try_into()
        .expect("Hash algorithm changed output size");

    let mut rng = StdRng::from_seed(seed_array);
    Keypair::generate_with(&mut rng)
}

/// Generates a cryptographic key pair using the operating system's random number generator.
///
/// This function creates a new Schnorrkel key pair using the `OsRng` random number generator,
/// which is cryptographically secure and provided by the operating system. Each invocation
/// of this function will produce a unique key pair due to the randomness introduced by `OsRng`.
///
/// # Returns
///
/// A `Keypair` consisting of a randomly generated public and private key.
/// This function is particularly useful in scenarios where a high degree of security is required,
/// such as in cryptographic applications, secure communications, or blockchain technology.
/// As it relies on the operating system's random number generator, it provides a high level
/// of randomness and security.
pub fn generate_keypair() -> Keypair {
    Keypair::generate_with(OsRng)
}

/// Signs a message using a Schnorrkel private key and a given context.
///
/// This function signs a message using the Schnorrkel digital signature algorithm.
/// It requires the message as a byte slice, the signer's private key, and an additional
/// byte slice as context to enhance signature uniqueness and security. The context can
/// be any arbitrary data, such as a string representing the purpose of the signature.
///
/// # Arguments
///
/// * `message` - A byte slice (`&[u8]`) representing the message to be signed.
/// * `private_key` - A `SecretKey` used to sign the message. It should be the private
///   part of a Schnorrkel key pair.
/// * `context` - A byte slice (`&[u8]`) representing the context for the signature. The
///   context is hashed and combined with the message during the signing process.
///
/// # Returns
///
/// A `Vec<u8>` containing the Schnorrkel signature of the message.
///
/// This function is useful for signing messages in cryptographic protocols where
/// Schnorrkel signatures are required. The signature can later be verified by others
/// using the corresponding public key and the same context.
pub fn sign_message(message: &[u8], private_key: SecretKey, context: &[u8]) -> Vec<u8> {
    let keypair = Keypair::from(private_key);
    let context = schnorrkel::signing_context(context);
    let signature = keypair.sign(context.bytes(message));
    signature.to_bytes().to_vec()
}

/// Verifies the signature of a message given the public key and context.
///
/// This function checks if a provided signature for a message is valid based on a
/// Schnorrkel public key and a specific context. The context should be the same as the one
/// used during the signing process. All inputs (message, signature, public key, and context)
/// are provided as byte slices.
///
/// # Arguments
///
/// * `message` - A byte slice (`&[u8]`) representing the message whose signature is to be verified.
/// * `signature` - A byte slice (`&[u8]`) representing the signature to be verified.
/// * `public_key` - A byte slice (`&[u8]`) representing the public key used to verify the signature.
/// * `context` - A byte slice (`&[u8]`) representing the context used during the signing process.
///
/// # Returns
///
/// A `bool` indicating whether the signature is valid (`true`) or not (`false`).
///
/// # Panics
///
/// Panics if the public key or the signature cannot be converted from their byte slice
/// representations into their respective Schnorrkel types. This usually means that the
/// provided byte slices are malformed or do not represent a valid public key or signature.
///
/// This function is essential in cryptographic protocols using Schnorrkel signatures,
/// allowing the verification of a message's authenticity and integrity. It ensures that
/// the message was indeed signed by the holder of the corresponding private key and that
/// it has not been altered since signing.
pub fn verify_signature(
    message: &[u8],
    signature: &[u8],
    public_key: &[u8],
    context: &[u8],
) -> bool {
    let public_key = schnorrkel::PublicKey::from_bytes(public_key).unwrap();
    let signature = schnorrkel::Signature::from_bytes(signature).unwrap();
    let context = schnorrkel::signing_context(context);

    public_key
        .verify(context.bytes(message), &signature)
        .is_ok()
}

/// Verifies if at least `n` out of `m` Schnorrkel signatures are valid for a given message.
///
/// This function is used in scenarios where a message requires multiple signatures to be
/// considered valid, such as in multi-signature wallets or collective decision-making processes.
/// It checks each signature against its corresponding public key and counts the number of valid
/// signatures. The message is considered valid if at least `n` signatures out of `m` are valid.
///
/// # Arguments
///
/// * `message` - A byte slice (`&[u8]`) representing the message to be verified.
/// * `signatures` - A vector of byte slices, each representing a signature to be verified.
///   The length of this vector must be `m`.
/// * `public_keys` - A vector of byte slices, each representing a public key corresponding
///   to each signature. The length of this vector must also be `m`.
/// * `context` - A byte slice (`&[u8]`) representing the context used during the signing process.
/// * `n` - The minimum number of valid signatures required for the message to be considered valid.
/// * `m` - The total number of signatures and public keys provided.
///   This should match the lengths of `signatures` and `public_keys`.
///
/// # Returns
///
/// A `bool` indicating whether at least `n` out of `m` signatures are valid (`true`) or not (`false`).
///
/// # Panics
///
/// Panics if the lengths of `public_keys` and `signatures` do not both equal `m`, or if `n` is greater than `m`.
///
/// This function is particularly useful in collective or shared ownership setups where
/// a decision or transaction must be authorized by a certain number of participants out of a larger group.
pub fn verify_n_of_m_multi_signature(
    message: &[u8],
    signatures: Vec<Vec<u8>>,
    public_keys: Vec<Vec<u8>>,
    context: &[u8],
    n: usize,
    m: usize,
) -> bool {
    assert_eq!(public_keys.len(), m);
    assert_eq!(signatures.len(), m);
    assert!(n <= m);

    let mut valid_signatures = 0;

    for (public_key, signature) in public_keys.iter().zip(signatures.iter()) {
        if verify_signature(message, signature, public_key, context) {
            valid_signatures += 1;
            if valid_signatures >= n {
                return true;
            }
        }
    }

    false
}

#[cfg(test)]
mod test {

    #[test]
    fn test_generate_keypair_with_seed() {
        let seed = "some random seed";
        let keypair = super::generate_keypair_with_seed(seed.as_bytes());
        println!(
            "Public key: {}",
            hex::encode(keypair.public.to_bytes()).into_string()
        );
        println!(
            "Private key: {}",
            hex::encode(keypair.secret.to_bytes()).into_string()
        );
        assert_eq!(
            hex::encode(keypair.public.to_bytes()).into_string(),
            "Guhh76iZeREySNW7F35eCU9h8Ry61oa1kaqaUaz8qo2x".to_string()
        );
        assert_eq!(hex::encode(keypair.secret.to_bytes()).into_string(), "317zQ7EQptp9nD9xDv6BA39qtF6SPBDzRexFDwbXhrkjzR15Y9Ay9cfVNmNiEy44fZ5tjthMcAs44ypj2m1LLg3X".to_string());
    }

    #[test]
    fn test_generate_keypair() {
        let keypair = super::generate_keypair();
        println!(
            "Public key: {}",
            hex::encode(keypair.public.to_bytes()).into_string()
        );
        println!(
            "Private key: {}",
            hex::encode(keypair.secret.to_bytes()).into_string()
        );
        assert_eq!(keypair.public.to_bytes().len(), 32); // public key is 32 bytes
        assert_eq!(keypair.secret.to_bytes().len(), 64); // private key is 64 bytes
    }

    #[test]
    fn test_sign_message() {
        let message = "This is a test message";
        let private_key = schnorrkel::SecretKey::from_bytes(&[0u8; 64]).unwrap();
        let context = "test context";
        let signature = super::sign_message(message.as_bytes(), private_key, context.as_bytes());
        assert_eq!(signature.len(), 64);
        println!("{}", hex::encode(signature).into_string());
    }

    #[test]
    fn test_verify_signature() {
        let message = "This is a test message";
        let private_key = schnorrkel::SecretKey::from_bytes(&[0u8; 64]).unwrap();
        let public_key = private_key.to_public();
        let context = "test context";
        let signature = hex::decode("5syAY4jtsxYQsQoNBAhauutHzg1bNwmq2g5MMGRCp3z7J1aECE6kaTS426FDx59rdEgArmXRYgxU1BizAa7zuhb6").into_vec().unwrap();
        assert!(super::verify_signature(
            message.as_bytes(),
            &signature,
            &public_key.to_bytes(),
            context.as_bytes()
        ));
    }

    #[test]
    fn test_n_of_m_multi_signature_verify() {
        let n = 2;
        let m = 3;
        let message = "This is a test message";
        let context = "test context";

        // generate keys
        let keypair1 = super::generate_keypair();
        let keypair2 = super::generate_keypair();
        let keypair3 = super::generate_keypair();

        // sign the message
        let signature1 = super::sign_message(
            message.as_bytes(),
            keypair1.secret.clone(),
            context.as_bytes(),
        );
        let signature2 = super::sign_message(
            message.as_bytes(),
            keypair2.secret.clone(),
            context.as_bytes(),
        );
        let signature3 = super::sign_message(
            message.as_bytes(),
            keypair3.secret.clone(),
            context.as_bytes(),
        );

        // verify the signatures
        assert_eq!(
            super::verify_n_of_m_multi_signature(
                message.as_bytes(),
                vec![signature1.clone(), signature2.clone(), signature3.clone()],
                vec![
                    keypair1.public.to_bytes().to_vec(),
                    keypair2.public.to_bytes().to_vec(),
                    keypair3.public.to_bytes().to_vec()
                ],
                context.as_bytes(),
                n,
                m
            ),
            true
        );

        let falsified_message = "This is a falsified message";
        // falsified 1 message, should be valid because 2 out of 3 signatures are valid
        let falsified_signature1 = super::sign_message(
            falsified_message.as_bytes(),
            keypair1.secret.clone(),
            context.as_bytes(),
        );
        assert_eq!(
            super::verify_n_of_m_multi_signature(
                message.as_bytes(),
                vec![
                    falsified_signature1.clone(),
                    signature2.clone(),
                    signature3.clone()
                ],
                vec![
                    keypair1.public.to_bytes().to_vec(),
                    keypair2.public.to_bytes().to_vec(),
                    keypair3.public.to_bytes().to_vec()
                ],
                context.as_bytes(),
                n,
                m
            ),
            true
        );

        // falsified 2 messages, should be invalid because only 1 out of 3 signatures are valid
        let falsified_signature2 = super::sign_message(
            falsified_message.as_bytes(),
            keypair2.secret.clone(),
            context.as_bytes(),
        );
        assert_eq!(
            super::verify_n_of_m_multi_signature(
                message.as_bytes(),
                vec![
                    falsified_signature1.clone(),
                    falsified_signature2.clone(),
                    signature3.clone()
                ],
                vec![
                    keypair1.public.to_bytes().to_vec(),
                    keypair2.public.to_bytes().to_vec(),
                    keypair3.public.to_bytes().to_vec()
                ],
                context.as_bytes(),
                n,
                m
            ),
            false
        );
    }
}
