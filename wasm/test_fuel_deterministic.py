from vm.glue import ContractTester

testCalContractClient = ContractTester("test_fuel_deterministic")

def test_cal_glue():
    testCalContractClient.constructor()
    result, err = testCalContractClient.executeReadOnly("add", int, 1, 2)
    print(f"result {result}")
    assert result == 3
    result, err = testCalContractClient.executeReadOnly("sub", int, 1, 2)
    print(f"result {result}, err {err}")
    assert result == -1
    result, err = testCalContractClient.executeReadOnly("mul", int, 1, 2)
    print(f"result {result}")
    assert result == 2
    result, err = testCalContractClient.executeReadOnly("div", int, 1, 2)
    print(f"result {result}")
    assert result == 0
