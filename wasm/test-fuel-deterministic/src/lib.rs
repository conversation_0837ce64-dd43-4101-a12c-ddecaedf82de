pub mod calculate {

    use std::ffi::{c_char, CString};
    use std::collections::HashMap;
    use serde::{Deserialize, Serialize};
    use std::ptr;

    #[derive(Default)]
    pub struct Calculate {}

    impl Calculate {
        pub fn new() -> Self {
            Self {}
        }

        pub fn add(&self, a: i32, b: i32) -> i32 {
            a + b
        }

        pub fn sub(&self, a: i32, b: i32) -> i32 {
            a - b
        }

        pub fn mul(&self, a: i32, b: i32) -> i32 {
            a * b
        }

        pub fn div(&self, a: i32, b: i32) -> i32 {
            a / b
        }
    }

    static mut INSTANCE: std::cell::UnsafeCell<Option<Calculate>> = std::cell::UnsafeCell::new(None);

    #[allow(dead_code)]
    #[inline]
    pub fn get_instance<'a>() -> &'a mut Calculate {
        unsafe {
            if (*INSTANCE.get()).is_none() { *INSTANCE.get() = Some(Calculate::default()); }
            (&mut *INSTANCE.get()).as_mut().unwrap()
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    #[allow(dead_code)]
    pub fn set_instance(instance: Calculate) { unsafe { *INSTANCE.get() = Some(instance); } }

    #[cfg(target_arch = "wasm32")]
    #[derive(serde::Serialize, serde::Deserialize)]
    struct _NewParams {}

    #[derive(Serialize, Deserialize, Debug)]
    pub struct FuncArgs<T> {
        pub args: T,
        pub envs: HashMap<String, String>,
    }

    #[inline]
    pub fn object_to_json_ptr<T: serde::Serialize>(object: T) -> anyhow::Result<*mut c_char> {
        let path_json = serde_json::to_string(&object)?;
        Ok(write_string(&path_json))
    }

    #[cfg(target_arch = "wasm32")]
    extern "C" {
        fn debugger(ptr: *mut c_char);
    }

    #[inline]
    pub fn string_to_c_char(string: &str) -> *mut c_char {
        CString::new(string)
            .map(|c_string| c_string.into_raw())
            .unwrap_or_else(|_| ptr::null_mut())
    }

    #[inline]
    pub fn call_debugger(string: &str) {
        unsafe {
            let debugger_str_ptr = string_to_c_char(string);
            debugger(debugger_str_ptr);
        }
    }

    #[inline]
    pub fn json_ptr_to_object<T: serde::de::DeserializeOwned>(ptr: *mut c_char) -> anyhow::Result<T> {
        let c_string = unsafe { CString::from_raw(ptr) };
        let path_json = c_string.to_str()?;
        let object = serde_json::from_str(path_json)?;
        Ok(object)
    }

    #[derive(serde::Serialize, serde::Deserialize, Debug)]
    pub struct SerializableResult {
        #[serde(skip_serializing_if = "Option::is_none")]
        pub value: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        pub err: Option<String>,
    }

    impl SerializableResult
    {
        #[inline]
        pub fn from<T: serde::Serialize>(result: Result<T, anyhow::Error>) -> Self {
            match result {
                Ok(value) => SerializableResult {
                    value: Option::from(serde_json::to_string(&Some(value)).unwrap()),
                    err: None,
                },
                Err(err) => SerializableResult {
                    value: None,
                    err: Some(err.to_string()),
                },
            }
        }

        #[inline]
        pub fn default() -> Self {
            SerializableResult {
                value: None,
                err: None,
            }
        }
    }

    #[no_mangle]
    pub extern "C" fn allocate_c_char(len: usize) -> *mut c_char {
        let buffer = vec![0u8; len];
        let raw_ptr = buffer.as_ptr() as *mut c_char;
        core::mem::forget(buffer);
        raw_ptr
    }


    #[no_mangle]
    pub extern "C" fn deallocate_c_char(ptr: *mut c_char) {
        unsafe {
            let _ = CString::from_raw(ptr);
        }
    }

    #[cfg(target_arch = "wasm32")]
    #[no_mangle]
    pub extern "C" fn new(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
        let func_args: FuncArgs<_NewParams> = json_ptr_to_object(input_ptr).unwrap();
        let _params = func_args.args;
        let new = || {
            {
                let instance = get_instance();
                *instance = Calculate::new();
            }
        };
        new();
        let serializable_result = SerializableResult::default();
        object_to_json_ptr(&serializable_result).unwrap()
    }

    #[inline]
    pub fn write_string(string: &str) -> *mut c_char {
        CString::new(string)
            .map(|c_string| c_string.into_raw())
            .unwrap_or_else(|_| ptr::null_mut())
    }


    #[cfg(target_arch = "wasm32")]
    #[derive(serde::Serialize, serde::Deserialize)]
    struct _AddParams {
        pub p0: i32,
        pub p1: i32,
    }

    #[cfg(target_arch = "wasm32")]
    #[no_mangle]
    pub extern "C" fn add(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
        let func_args: FuncArgs<_AddParams> = json_ptr_to_object(input_ptr).unwrap();
        let params = func_args.args;
        let add = |a: i32, b: i32| -> i32   {
            {
                get_instance().add(a, b)
            }
        };
        let result_values = add(params.p0, params.p1);
        let serializable_result = SerializableResult::from(Ok(result_values));
        object_to_json_ptr(&serializable_result).unwrap()
    }

    #[cfg(target_arch = "wasm32")]
    #[derive(serde::Serialize, serde::Deserialize)]
    struct _SubParams {
        pub p0: i32,
        pub p1: i32,
    }

    #[cfg(target_arch = "wasm32")]
    #[no_mangle]
    pub extern "C" fn sub(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
        let func_args: FuncArgs<_SubParams> = json_ptr_to_object(input_ptr).unwrap();
        let params = func_args.args;
        let sub = |a: i32, b: i32| -> i32   {
            {
                get_instance().sub(a, b)
            }
        };
        let result_values = sub(params.p0, params.p1);
        let serializable_result = SerializableResult::from(Ok(result_values));
        object_to_json_ptr(&serializable_result).unwrap()
    }

    #[cfg(target_arch = "wasm32")]
    #[derive(serde::Serialize, serde::Deserialize)]
    struct _MulParams {
        pub p0: i32,
        pub p1: i32,
    }

    #[cfg(target_arch = "wasm32")]
    #[no_mangle]
    pub extern "C" fn mul(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
        let func_args: FuncArgs<_MulParams> = json_ptr_to_object(input_ptr).unwrap();
        let params = func_args.args;
        let mul = |a: i32, b: i32| -> i32   {
            {
                get_instance().mul(a, b)
            }
        };
        let result_values = mul(params.p0, params.p1);
        let serializable_result = SerializableResult::from(Ok(result_values));
        object_to_json_ptr(&serializable_result).unwrap()
    }
    

    #[cfg(target_arch = "wasm32")]
    #[derive(serde::Serialize, serde::Deserialize)]
    struct _DivParams {
        pub p0: i32,
        pub p1: i32,
    }

    #[cfg(target_arch = "wasm32")]
    #[no_mangle]
    pub extern "C" fn div(input_ptr: *mut std::ffi::c_char) -> *mut std::ffi::c_char {
        let func_args: FuncArgs<_DivParams> = json_ptr_to_object(input_ptr).unwrap();
        let params = func_args.args;
        let div = |a: i32, b: i32| -> i32   {
            {
                get_instance().div(a, b)
            }
        };
        let result_values = div(params.p0, params.p1);
        let serializable_result = SerializableResult::from(Ok(result_values));
        object_to_json_ptr(&serializable_result).unwrap()
    }
}
