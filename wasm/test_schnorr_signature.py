import pytest

from common import hexToBytes
from vm import ContractTester

schnorrUtilsContractClient = ContractTester(
    "schnorr_signature",
)


@pytest.fixture(autouse=True)
def register_contract():
    schnorrUtilsContractClient.constructor()


# Tips:
# * signature is hex encoded
# * all keys are hex encoded


def test_generate_keys_with_seed():
    seed = "some random seed"
    (publicKey, privateKey), err = schnorrUtilsContractClient.executeReadOnly("generate_keys_with_seed", tuple, seed)
    assert err is None

    print(f"publicKey: {publicKey}")
    print(f"privateKey: {privateKey}")
    assert publicKey == "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739"
    assert (
        privateKey
        == "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6"
    )


def test_generate_keys():
    (publicKey, privateKey), err = schnorrUtilsContractClient.executeReadOnly("generate_keys", tuple)
    assert err is None

    print(f"publicKey: {publicKey}")
    print(f"privateKey: {privateKey}")
    # length of public key bytes should be 32
    publicKeyBytes = hexToBytes(publicKey)
    assert len(publicKeyBytes) == 32
    # length of private key bytes should be 64
    privateKeyBytes = hexToBytes(privateKey)
    assert len(privateKeyBytes) == 64


def test_private_key_to_public_key():
    privateKey = "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6"
    publicKey, err = schnorrUtilsContractClient.executeReadOnly("private_key_to_public_key", str, privateKey)
    assert err is None
    assert publicKey == "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739"


def test_sign_message():
    message = "some message"
    privateKey = "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6"
    context = "some context"
    signature, err = schnorrUtilsContractClient.executeReadOnly("sign_message", str, message, privateKey, context)
    assert err is None
    print(signature)
    # length of signature bytes should be 64
    signatureBytes = hexToBytes(signature)
    assert len(signatureBytes) == 64


def test_verify_signature():
    # test valid message
    message = "some message"
    publicKey = "0xec5f8415bed17e43573a6ce9d443fa845795a03d9689dd974afec4c674657739"
    privateKey = "0x64234ec5cd8b2dca27df3cd6b946ff2b2b8c09977bc833501b8dcb3e53166c08d20f87d0b25716ecf5c1bd9b1e033d4fd8a8ccdc84b35d3c663231328bfeffb6"
    context = "some context"
    signature, err = schnorrUtilsContractClient.executeReadOnly("sign_message", str, message, privateKey, context)
    assert err is None

    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_signature", bool, message, signature, publicKey, context
    )
    assert err is None
    assert result is True

    # test invalid signature
    invalidMessage = "some other message"
    invalidSignature, err = schnorrUtilsContractClient.executeReadOnly(
        "sign_message", str, invalidMessage, privateKey, context
    )
    assert err is None

    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_signature", bool, message, invalidSignature, publicKey, context
    )
    assert err is None
    assert result is False

    # test invalid message
    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_signature", bool, invalidMessage, signature, publicKey, context
    )
    assert err is None
    assert result is False

    # test invalid public key
    (invalidPublicKey, _), err = schnorrUtilsContractClient.executeReadOnly("generate_keys", tuple)
    assert err is None
    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_signature", bool, message, signature, invalidPublicKey, context
    )
    assert err is None
    assert result is False

    # test invalid context
    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_signature", bool, message, signature, publicKey, "invalid context"
    )
    assert err is None
    assert result is False


def test_verify_batch_signatures():
    context = "some context"
    messages = ["message1", "message2", "message3"]

    # generate public keys, private keys, and signatures
    publicKeys = []
    signatures = []

    for i in range(len(messages)):
        (publicKey, privateKey), err = schnorrUtilsContractClient.executeReadOnly("generate_keys", tuple)
        assert err is None
        publicKeys.append(publicKey)

        signature, err = schnorrUtilsContractClient.executeReadOnly(
            "sign_message", str, messages[i], privateKey, context
        )
        assert err is None
        signatures.append(signature)

    # verify the signatures
    result, err = schnorrUtilsContractClient.executeReadOnly(
        "verify_batch_signatures", bool, messages, signatures, publicKeys, context
    )
    assert err is None
    assert result is True
