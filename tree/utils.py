# Thread-safe object pool in Python

from queue import Empty, Full, Queue
from typing import Any, Callable, Optional


class ObjectPool:
    """
    A thread-safe object pool implementation that manages reusable objects
    to reduce memory allocation overhead.
    """

    def __init__(self, create_fn: Callable[[], Any], max_size: Optional[int] = None):
        """
        Initialize the object pool

        Args:
            create_fn: Function to create new objects when pool is empty
            max_size: Maximum number of objects to store in pool (None for unlimited)
        """
        self.create_fn = create_fn
        self.pool = Queue() if max_size is None else Queue(maxsize=max_size)

    def acquire(self) -> Any:
        """Get an object from the pool or create a new one if pool is empty"""
        try:
            return self.pool.get_nowait()
        except Empty:
            return self.create_fn()

    def release(self, obj: Any) -> None:
        """Return an object to the pool"""
        try:
            self.pool.put_nowait(obj)
        except Full:
            pass  # Discard object if pool is full


def assertBytes(*args: Any, allowNone: bool = False) -> None:
    """
    assert any given args is bytes type

    if allowNone is True, allow None value
    """
    for value in args:
        if allowNone and value is None:
            continue
        assert isinstance(value, bytes), f"expected bytes, got {type(value)}"
