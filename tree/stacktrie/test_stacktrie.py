import pytest

from common import hexToBytes
from tree import <PERSON><PERSON><PERSON>
from tree.stacktrie.stacktrie import <PERSON><PERSON><PERSON><PERSON>


def test_stacktrie_insert_and_hash():
    tests = [
        (
            (
                "0x00",
                "v_______________________0___0",
                "0x5cb26357b95bb9af08475be00243ceb68ade0b66b5cd816b0c18a18c612d2d21",
            ),
            (
                "0x70",
                "v_______________________0___1",
                "0x8ff64309574f7a437a7ad1628e690eb7663cfde10676f8a904a8c8291dbc1603",
            ),
            (
                "0xf0",
                "v_______________________0___2",
                "9e3a01bd8d43efb8e9d4b5506648150b8e3ed1caea596f84ee28e01a72635470",
            ),
        ),
        (
            (
                "10cc",
                "v_______________________1___0",
                "233e9b257843f3dfdb1cce6676cdaf9e595ac96ee1b55031434d852bc7ac9185",
            ),
            (
                "e1fc",
                "v_______________________1___1",
                "39c5e908ae83d0c78520c7c7bda0b3782daf594700e44546e93def8f049cca95",
            ),
            (
                "eefc",
                "v_______________________1___2",
                "d789567559fd76fe5b7d9cc42f3750f942502ac1c7f2a466e2f690ec4b6c2a7c",
            ),
        ),
        (
            (
                "baac",
                "v_______________________2___0",
                "8be1c86ba7ec4c61e14c1a9b75055e0464c2633ae66a055a24e75450156a5d42",
            ),
            (
                "bbac",
                "v_______________________2___1",
                "8495159b9895a7d88d973171d737c0aace6fe6ac02a4769fff1bc43bcccce4cc",
            ),
            (
                "dacc",
                "v_______________________2___2",
                "9bcfc5b220a27328deb9dc6ee2e3d46c9ebc9c69e78acda1fa2c7040602c63ca",
            ),
        ),
        (
            (
                "00cccc",
                "v_______________________3___0",
                "e57dc2785b99ce9205080cb41b32ebea7ac3e158952b44c87d186e6d190a6530",
            ),
            (
                "245600",
                "v_______________________3___1",
                "0335354adbd360a45c1871a842452287721b64b4234dfe08760b243523c998db",
            ),
            (
                "245622",
                "v_______________________3___2",
                "9e6832db0dca2b5cf81c0e0727bfde6afc39d5de33e5720bccacc183c162104e",
            ),
        ),
        (
            (
                "1456711c",
                "v_______________________4___0",
                "f2389e78d98fed99f3e63d6d1623c1d4d9e8c91cb1d585de81fbc7c0e60d3529",
            ),
            (
                "1456733c",
                "v_______________________4___1",
                "101189b3fab852be97a0120c03d95eefcf984d3ed639f2328527de6def55a9c0",
            ),
            (
                "30cccccc",
                "v_______________________4___2",
                "3780ce111f98d15751dfde1eb21080efc7d3914b429e5c84c64db637c55405b3",
            ),
        ),
        (
            (
                "88001f",
                "v_______________________5___0",
                "e817db50d84f341d443c6f6593cafda093fc85e773a762421d47daa6ac993bd5",
            ),
            (
                "88002e",
                "v_______________________5___1",
                "d6e3e6047bdc110edd296a4d63c030aec451bee9d8075bc5a198eee8cda34f68",
            ),
            (
                "88003d",
                "v_______________________5___2",
                "b6bdf8298c703342188e5f7f84921a402042d0e5fb059969dd53a6b6b1fb989e",
            ),
        ),
        (
            (
                "01fc",
                "v_______________________6___0",
                "693268f2ca80d32b015f61cd2c4dba5a47a6b52a14c34f8e6945fad684e7a0d5",
            ),
            (
                "02ec",
                "v_______________________6___1",
                "e24ddd44469310c2b785a2044618874bf486d2f7822603a9b8dce58d6524d5de",
            ),
            (
                "04dc",
                "v_______________________6___2",
                "33fc259629187bbe54b92f82f0cd8083b91a12e41a9456b84fc155321e334db7",
            ),
        ),
        (
            (
                "f0fccc",
                "v_______________________7___0",
                "b0966b5aa469a3e292bc5fcfa6c396ae7a657255eef552ea7e12f996de795b90",
            ),
            (
                "ffff0f",
                "v_______________________7___1",
                "3b1ca154ec2a3d96d8d77bddef0abfe40a53a64eb03cecf78da9ec43799fa3d0",
            ),
            (
                "ffffff",
                "v_______________________7___2",
                "e75463041f1be8252781be0ace579a44ea4387bf5b2739f4607af676f7719678",
            ),
        ),
        (
            (
                "ff0f0f",
                "v_______________________8___0",
                "0928af9b14718ec8262ab89df430f1e5fbf66fac0fed037aff2b6767ae8c8684",
            ),
            (
                "ff0fff",
                "v_______________________8___1",
                "d870f4d3ce26b0bf86912810a1960693630c20a48ba56be0ad04bc3e9ddb01e6",
            ),
            (
                "ffffcc",
                "v_______________________8___2",
                "4239f10dd9d9915ecf2e047d6a576bdc1733ed77a30830f1bf29deaf7d8e966f",
            ),
        ),
        (
            (
                "123d",
                "x___________________________0",
                "fc453d88b6f128a77c448669710497380fa4588abbea9f78f4c20c80daa797d0",
            ),
            (
                "123e",
                "x___________________________1",
                "5af48f2d8a9a015c1ff7fa8b8c7f6b676233bd320e8fb57fd7933622badd2cec",
            ),
            (
                "123f",
                "x___________________________2",
                "1164d7299964e74ac40d761f9189b2a3987fae959800d0f7e29d3aaf3eae9e15",
            ),
        ),
        (
            (
                "123d",
                "x___________________________0",
                "fc453d88b6f128a77c448669710497380fa4588abbea9f78f4c20c80daa797d0",
            ),
            (
                "123e",
                "x___________________________1",
                "5af48f2d8a9a015c1ff7fa8b8c7f6b676233bd320e8fb57fd7933622badd2cec",
            ),
            (
                "124a",
                "x___________________________2",
                "661a96a669869d76b7231380da0649d013301425fbea9d5c5fae6405aa31cfce",
            ),
        ),
        (
            (
                "123d",
                "x___________________________0",
                "fc453d88b6f128a77c448669710497380fa4588abbea9f78f4c20c80daa797d0",
            ),
            (
                "123e",
                "x___________________________1",
                "5af48f2d8a9a015c1ff7fa8b8c7f6b676233bd320e8fb57fd7933622badd2cec",
            ),
            (
                "13aa",
                "x___________________________2",
                "6590120e1fd3ffd1a90e8de5bb10750b61079bb0776cca4414dd79a24e4d4356",
            ),
        ),
        (
            (
                "123d",
                "x___________________________0",
                "fc453d88b6f128a77c448669710497380fa4588abbea9f78f4c20c80daa797d0",
            ),
            (
                "123e",
                "x___________________________1",
                "5af48f2d8a9a015c1ff7fa8b8c7f6b676233bd320e8fb57fd7933622badd2cec",
            ),
            (
                "2aaa",
                "x___________________________2",
                "f869b40e0c55eace1918332ef91563616fbf0755e2b946119679f7ef8e44b514",
            ),
        ),
        (
            (
                "1234da",
                "x___________________________0",
                "1c4b4462e9f56a80ca0f5d77c0d632c41b0102290930343cf1791e971a045a79",
            ),
            (
                "1234ea",
                "x___________________________1",
                "2f502917f3ba7d328c21c8b45ee0f160652e68450332c166d4ad02d1afe31862",
            ),
            (
                "1234fa",
                "x___________________________2",
                "4f4e368ab367090d5bc3dbf25f7729f8bd60df84de309b4633a6b69ab66142c0",
            ),
        ),
        (
            (
                "1234da",
                "x___________________________0",
                "1c4b4462e9f56a80ca0f5d77c0d632c41b0102290930343cf1791e971a045a79",
            ),
            (
                "1234ea",
                "x___________________________1",
                "2f502917f3ba7d328c21c8b45ee0f160652e68450332c166d4ad02d1afe31862",
            ),
            (
                "1235aa",
                "x___________________________2",
                "21840121d11a91ac8bbad9a5d06af902a5c8d56a47b85600ba813814b7bfcb9b",
            ),
        ),
        (
            (
                "1234da",
                "x___________________________0",
                "1c4b4462e9f56a80ca0f5d77c0d632c41b0102290930343cf1791e971a045a79",
            ),
            (
                "1234ea",
                "x___________________________1",
                "2f502917f3ba7d328c21c8b45ee0f160652e68450332c166d4ad02d1afe31862",
            ),
            (
                "124aaa",
                "x___________________________2",
                "ea4040ddf6ae3fbd1524bdec19c0ab1581015996262006632027fa5cf21e441e",
            ),
        ),
        (
            (
                "1234da",
                "x___________________________0",
                "1c4b4462e9f56a80ca0f5d77c0d632c41b0102290930343cf1791e971a045a79",
            ),
            (
                "1234ea",
                "x___________________________1",
                "2f502917f3ba7d328c21c8b45ee0f160652e68450332c166d4ad02d1afe31862",
            ),
            (
                "13aaaa",
                "x___________________________2",
                "e4beb66c67e44f2dd8ba36036e45a44ff68f8d52942472b1911a45f886a34507",
            ),
        ),
        (
            (
                "1234da",
                "x___________________________0",
                "1c4b4462e9f56a80ca0f5d77c0d632c41b0102290930343cf1791e971a045a79",
            ),
            (
                "1234ea",
                "x___________________________1",
                "2f502917f3ba7d328c21c8b45ee0f160652e68450332c166d4ad02d1afe31862",
            ),
            (
                "2aaaaa",
                "x___________________________2",
                "5f5989b820ff5d76b7d49e77bb64f26602294f6c42a1a3becc669cd9e0dc8ec9",
            ),
        ),
    ]
    for test in tests:
        for i in range(len(test)):
            trie = StackTrie()
            for j in range(i + 1):
                key, value, _ = test[j]
                trie.update(hexToBytes(key), value.encode())
            expected_hash = test[i][2]
            assert trie.hash() == hexToBytes(expected_hash)


def test_stacktrie_correctness():
    stacktrie = StackTrie()
    memorytrie = MemoryTrie()
    for i in range(256):
        # notice that the key should be in ascending order
        stacktrie.update(f"{chr(i)}".encode(), f"{i}".encode())
        memorytrie.update(f"{chr(i)}".encode(), f"{i}".encode())
    assert stacktrie.hash() == memorytrie.hash()


def test_stacktrie_error():
    trie = StackTrie()
    with pytest.raises(AssertionError):
        # try to insert None value
        trie.update(b"a", None)

    with pytest.raises(AssertionError):
        # try to insert None key
        trie.update(None, bytearray())

    with pytest.raises(ValueError):
        # try to insert empty value
        trie.update(b"a", b"")

    # insert same key
    trie.update(b"a", b"v")
    with pytest.raises(ValueError):
        trie.update(b"a", b"v")
        trie.update(b"a", b"w")

    # insert non-ascending key
    trie.update(b"c", b"v")
    with pytest.raises(ValueError):
        trie.update(b"b", b"v")
        trie.update(b"a", b"w")
