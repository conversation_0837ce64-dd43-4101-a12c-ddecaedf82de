# Implement StackTrie in Python, inspired by ethereum's St<PERSON><PERSON><PERSON>

from __future__ import annotations

from typing import Callable, Optional

from common import Empty<PERSON><PERSON><PERSON>ash, <PERSON><PERSON><PERSON>ash<PERSON>

from ..encode import hexToCompact, keybytesToHex
from ..hasher import Hasher
from ..node import FullN<PERSON>, Hash<PERSON>ode, RawNode, ShortNode, ValueNode
from ..utils import ObjectPool, assertBytes
from .stacktrie_node import NodeType, StackTrieNode

# Create a global node pool for reusing StackTrieNode objects
nodePool = ObjectPool(lambda: StackTrieNode())

# pre-allocate commonly used values
EMPTY_VALUE_NODE = ValueNode(None)
SINGLE_BYTE_CACHE = [bytes([i]) for i in range(17)]  # 0-16 for hex digits


class StackTrie(TrieHasher):
    """
    StackTrie is a trie implementation that expects keys to be inserted
    in order. Once it determines that a subtree will no longer be inserted
    into, it will hash it and free up the memory it uses.

    Attributes:
        root (StackTrieNode): The root node of the trie
        hasher (Hasher): The hasher implementation
        lastKey (Optional[bytes]): The last inserted key for ordering validation
        onTrieNode (Optional[Callable]): Callback for node commitment
    """

    def __init__(self):
        """Initialize an empty StackTrie"""
        self.root: StackTrieNode = nodePool.acquire()
        self.hasher = Hasher()
        self.lastKey: Optional[bytes] = None
        self.onTrieNode: Optional[Callable[[bytes, bytes, bytes]]] = None  # param: (path, hash, blob)

    def reset(self) -> None:
        """Reset the trie to its initial empty state"""
        if self.root:
            nodePool.release(self.root.reset())  # Release current root
        self.root = nodePool.acquire()
        self.lastKey = None

    def trieKey(self, key: bytes) -> bytes:
        """Convert a user key to its internal trie representation"""
        assertBytes(key)

        k = keybytesToHex(key)
        return k[: len(k) - 1]

    def update(self, key: bytes, value: bytes) -> None:
        """
        Insert a (key, value) pair into the trie.
        Keys must be inserted in ascending order.
        """
        assertBytes(key, value)

        if len(value) == 0:
            raise ValueError("trying to insert empty value")

        k = self.trieKey(key)
        if self.lastKey is not None and k <= self.lastKey:
            raise ValueError("keys must be inserted in ascending order")
        # update last key
        self.lastKey = k

        self._insert(self.root, k, value, None)

    def _insert(self, node: StackTrieNode, key: bytes, value: bytes, path: Optional[bytes]) -> None:
        """
        Internal method to insert a (key, value) pair into the trie.
        The path parameter tracks the full path to the current node for hashing purposes.
        """
        assertBytes(key, value)
        assertBytes(path, allowNone=True)

        if node.nodeType == NodeType.BRANCH_NODE:
            index = key[0]

            # Hash elder siblings that won't be modified anymore
            for i in range(index - 1, -1, -1):
                child = node.children[i]
                if child is not None and child.nodeType != NodeType.HASHED_NODE:
                    self._hash(child, path + SINGLE_BYTE_CACHE[i] if path else SINGLE_BYTE_CACHE[i])
                    break

            # Add new child
            if node.children[index] is None:
                node.children[index] = StackTrieNode.newLeafNode(key[1:], value)
            else:
                child_path = path + SINGLE_BYTE_CACHE[index] if path else SINGLE_BYTE_CACHE[index]
                self._insert(node.children[index], key[1:], value, child_path)

        elif node.nodeType == NodeType.EXTENSION_NODE:
            diffIndex = node.getDiffIndex(key)
            prefixPath = path + key[:diffIndex] if path else key[:diffIndex] if diffIndex > 0 else path

            if diffIndex == len(node.key):
                self._insert(node.children[0], key[diffIndex:], value, prefixPath)
                return

            n: StackTrieNode = None
            if diffIndex < len(node.key) - 1:
                n = StackTrieNode.newExtensionNode(node.key[diffIndex + 1 :], node.children[0])
                self._hash(
                    n,
                    path + node.key[: diffIndex + 1] if path else node.key[: diffIndex + 1],
                )
            else:
                n = node.children[0]
                self._hash(n, path + node.key if path else node.key)

            p: StackTrieNode = None
            if diffIndex == 0:
                node.children[0] = None
                p = node
                node.nodeType = NodeType.BRANCH_NODE
            else:
                node.children[0] = nodePool.acquire()
                node.children[0].nodeType = NodeType.BRANCH_NODE
                p = node.children[0]

            o = StackTrieNode.newLeafNode(key[diffIndex + 1 :], value)

            originIndex = node.key[diffIndex]
            newIndex = key[diffIndex]
            p.children[originIndex] = n
            p.children[newIndex] = o
            node.key = node.key[:diffIndex]

        elif node.nodeType == NodeType.LEAF_NODE:
            diffIndex = node.getDiffIndex(key)

            if diffIndex >= len(node.key):
                raise ValueError("Insertion failed, key already exists")

            p: StackTrieNode = None
            if diffIndex == 0:
                node.nodeType = NodeType.BRANCH_NODE
                p = node
                node.children[0] = None
            else:
                node.nodeType = NodeType.EXTENSION_NODE
                node.children[0] = nodePool.acquire()
                node.children[0].nodeType = NodeType.BRANCH_NODE
                p = node.children[0]

            originIndex = node.key[diffIndex]
            p.children[originIndex] = StackTrieNode.newLeafNode(node.key[diffIndex + 1 :], node.value)
            self._hash(
                p.children[originIndex],
                path + node.key[: diffIndex + 1] if path else node.key[: diffIndex + 1],
            )

            newIndex = key[diffIndex]
            p.children[newIndex] = StackTrieNode.newLeafNode(key[diffIndex + 1 :], value)

            node.key = node.key[:diffIndex]
            node.value = None

        elif node.nodeType == NodeType.HASHED_NODE:
            raise ValueError("hash node should not be inserted")

        elif node.nodeType == NodeType.EMPTY_NODE:
            node.nodeType = NodeType.LEAF_NODE
            node.key = key
            node.value = value

        else:
            raise ValueError("invalid node type")

    def _hash(self, node: StackTrieNode, path: Optional[bytes]) -> None:
        """
        Convert a node into a 'hashedNode' if possible.
        This method also handles the callback for node commitment.
        """
        if node.nodeType == NodeType.HASHED_NODE:
            return

        if node.nodeType == NodeType.EMPTY_NODE:
            node.value = EmptyRootHash
            node.key = b""
            node.nodeType = NodeType.HASHED_NODE
            return

        blob: Optional[bytes] = None

        if node.nodeType == NodeType.BRANCH_NODE:
            nodes = FullNode()
            # Reuse empty value node instead of creating new ones
            nodes.children = [EMPTY_VALUE_NODE] * 17

            for i, child in enumerate(node.children):
                if child is None:
                    continue

                child_path = path + SINGLE_BYTE_CACHE[i] if path else SINGLE_BYTE_CACHE[i]
                self._hash(child, child_path)

                if len(child.value) < 32:
                    nodes.children[i] = RawNode(child.value)
                else:
                    nodes.children[i] = HashNode(child.value)

                node.children[i] = None
                nodePool.release(child.reset())

            nodes.encode(self.hasher.encodeBuffer)
            blob = self.hasher.popBufferValue()

        elif node.nodeType == NodeType.EXTENSION_NODE:
            # Hash the child first
            self._hash(node.children[0], path + node.key if path else node.key)

            # Encode the extension node
            n = ShortNode(
                hexToCompact(node.key),  # key without terminator
                None,
            )
            if len(node.children[0].value) < 32:
                n.val = RawNode(node.children[0].value)
            else:
                n.val = HashNode(node.children[0].value)

            n.encode(self.hasher.encodeBuffer)
            blob = self.hasher.popBufferValue()

            nodePool.release(node.children[0].reset())
            node.children[0] = None

        elif node.nodeType == NodeType.LEAF_NODE:
            node.key = node.key + bytes([16])
            n = ShortNode(
                hexToCompact(node.key),  # key without terminator
                ValueNode(node.value),
            )
            n.encode(self.hasher.encodeBuffer)
            blob = self.hasher.popBufferValue()

        # Convert node to hashed form
        node.nodeType = NodeType.HASHED_NODE
        node.key = b""
        # For non-root small nodes (<32 bytes), store raw encoding
        if len(blob) < 32 and path is not None and len(path) > 0:
            node.value = blob
            return

        # Hash the node for larger nodes or root
        node.value = self.hasher.hashData(blob).value

        # Invoke callback if provided
        if self.onTrieNode is not None:
            self.onTrieNode(path, node.value, blob)

    def hash(self) -> bytes:
        """
        Hash the entire trie and return the root hash.
        This will commit all remaining nodes.
        """
        root = self.root
        self._hash(root, None)
        return root.value
