from __future__ import annotations

from enum import IntEnum
from typing import List, Optional

from ..utils import assertBytes


class NodeType(IntEnum):
    """NodeType representing different types of StackTrieNode

    EMPTY_NODE (0): Represents a newly initialized, empty node
    BR<PERSON>CH_NODE (1): Represents a node with up to 16 children (hexadecimal branches)
    EXTENSION_NODE (2): Represents a node with a partial key and single child
    LEAF_NODE (3): Represents a terminal node containing a value
    HASHED_NODE (4): Represents a node that has been hashed and committed to storage
    """

    EMPTY_NODE = 0
    BRANCH_NODE = 1
    EXTENSION_NODE = 2
    LEAF_NODE = 3
    HASHED_NODE = 4


class StackTrieNode:
    """A node in the StackTrie structure implementing a Modified Merkle Patricia Trie.

    The StackTrie is a data structure used primarily in blockchain implementations
    for efficient storage and verification of state data.

    Attributes:
        nodeType (NodeType): The type of this node (empty, branch, extension, leaf, or hashed)
        key (bytes): The partial or full key stored in this node
        value (Optional[bytes]): The value stored in leaf nodes, None for other node types
        children (List[Optional[StackTrieNode]]): List of child nodes, up to 16 for branch nodes
    """

    def __init__(self):
        self.nodeType: NodeType = NodeType.EMPTY_NODE
        self.key: bytes = b""  # Key chunk covered by this node
        self.value: Optional[bytes] = None  # Value for leaf nodes
        self.children: List[Optional[StackTrieNode]] = [None] * 16  # Children for branch/ext nodes

    def reset(self) -> StackTrieNode:
        """Reset the node to its initial empty state"""
        self.key = b""
        self.value = None
        self.children = [None] * 16
        self.nodeType = NodeType.EMPTY_NODE
        return self

    def getDiffIndex(self, key: bytes) -> int:
        """
        Find the index at which the node's key differs from the provided key
        Returns the length of the common prefix
        """
        assertBytes(key)
        for index, nibble in enumerate(self.key):
            if nibble != key[index]:
                return index
        return len(self.key)

    @staticmethod
    def newLeafNode(key: bytes, val: bytes) -> StackTrieNode:
        """Create a new leaf node with the given key and value"""
        assertBytes(key)
        assertBytes(val)

        node = StackTrieNode()
        node.nodeType = NodeType.LEAF_NODE
        node.key = key
        node.value = val
        return node

    @staticmethod
    def newExtensionNode(key: bytes, child: StackTrieNode) -> StackTrieNode:
        """Create a new extension node with the given key and child node"""
        assertBytes(key)

        node = StackTrieNode()
        node.nodeType = NodeType.EXTENSION_NODE
        node.key = key
        node.children[0] = child
        return node
