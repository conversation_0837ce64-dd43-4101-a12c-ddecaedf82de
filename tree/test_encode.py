import pytest

from tree.encode import compactToHex, hexToCompact, hexToKeybytes, keybytesToHex


@pytest.mark.parametrize(
    "hexKey,compact",
    [
        # empty keys, with and without terminator
        (bytes([]), bytes([0x00])),
        (bytes([16]), bytes([0x20])),
        # odd length, no terminator
        (bytes([1, 2, 3, 4, 5]), bytes([0x11, 0x23, 0x45])),
        # even length, no terminator
        (bytes([0, 1, 2, 3, 4, 5]), bytes([0x00, 0x01, 0x23, 0x45])),
        # odd length, terminator
        (bytes([15, 1, 12, 11, 8, 16]), bytes([0x3F, 0x1C, 0xB8])),
        # even length, terminator
        (bytes([0, 15, 1, 12, 11, 8, 16]), bytes([0x20, 0x0F, 0x1C, 0xB8])),
    ],
)
def test_hex_compact(hexKey, compact):
    # Test hexToCompact
    assert hexToCompact(hexKey) == compact
    # Test compactToHex
    assert compactToHex(compact) == hexKey


@pytest.mark.parametrize(
    "key,hexIn,hexOut",
    [
        (bytes([]), bytes([16]), bytes([16])),
        (bytes([]), bytes([]), bytes([16])),
        (
            bytes([0x12, 0x34, 0x56]),
            bytes([1, 2, 3, 4, 5, 6, 16]),
            bytes([1, 2, 3, 4, 5, 6, 16]),
        ),
        (
            bytes([0x12, 0x34, 0x5]),
            bytes([1, 2, 3, 4, 0, 5, 16]),
            bytes([1, 2, 3, 4, 0, 5, 16]),
        ),
        (
            bytes([0x12, 0x34, 0x56]),
            bytes([1, 2, 3, 4, 5, 6]),
            bytes([1, 2, 3, 4, 5, 6, 16]),
        ),
    ],
)
def test_hex_keybytes(key, hexIn, hexOut):
    # Test keybytesToHex
    assert keybytesToHex(key) == hexOut
    # Test hexToKeybytes
    assert hexToKeybytes(hexIn) == key
