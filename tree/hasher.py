from typing import <PERSON><PERSON>

from rlp import encode as rlp_encode

from common import keccak256

from .encode import hexToCompact
from .node import FullNode, HashNode, Node, ShortNode
from .stacktrie.stacktrie_node import StackTrieNode
from .utils import assertBytes


class Hasher:
    """
    Handles hashing operations for the trie
    """

    def __init__(self):
        self.encodeBuffer = bytearray()

    def reset(self) -> None:
        """Clear the internal buffer"""
        self.encodeBuffer.clear()

    def hashData(self, data: bytes) -> HashNode:
        """Hash the provided data using Keccak-256, and return a HashNode including the hash"""
        assertBytes(data)
        return HashNode(keccak256(data))

    def popBufferValue(self) -> bytes:
        """
        Return the value in the buffer and clear the buffer
        """
        value = bytes(self.encodeBuffer)
        self.encodeBuffer.clear()

        result = bytearray(rlp_encode(value))
        result[0] += 64
        return bytes(result)

    def hash(self, node: StackTrieNode) -> Tuple[Node, Node]:
        """
        Hash the provided node and return a tuple containing the hashed node and the cached node
        """
        if (hash := node.cache()[0]) is not None:
            return hash, node

        if isinstance(node, FullNode):
            collapsed, cached = self.hashFullNodeChildren(node)
            hashed = self.fullnodeToHash(collapsed)

            if isinstance(hashed, HashNode):
                cached.flags.hash = hashed
            else:
                cached.flags.hash = None
            return hashed, cached

        if isinstance(node, ShortNode):
            collapsed, cached = self.hashShortNodeChildren(node)
            hashed = self.shortnodeToHash(collapsed)

            if isinstance(hashed, HashNode):
                cached.flags.hash = hashed
            else:
                cached.flags.hash = None
            return hashed, cached
        else:
            return node, node

    def hashFullNodeChildren(self, node: FullNode) -> Tuple[FullNode, FullNode]:
        collapsed = node.copy()
        cached = node.copy()

        for i, child in enumerate(node.children):
            if child is not None:
                collapsed.children[i], cached.children[i] = self.hash(child)
            else:
                collapsed.children[i] = None
        return collapsed, cached

    def fullnodeToHash(self, node: FullNode) -> Node:
        node.encode(self.encodeBuffer)
        encoded = self.popBufferValue()

        if len(encoded) < 32:
            return node
        return self.hashData(encoded)

    def hashShortNodeChildren(self, node: ShortNode) -> Tuple[ShortNode, ShortNode]:
        collapsed = node.copy()
        cached = node.copy()

        collapsed.key = hexToCompact(node.key)
        if isinstance(node.val, (FullNode, ShortNode)):
            collapsed.val, cached.val = self.hash(node.val)
        return collapsed, cached

    def shortnodeToHash(self, node: ShortNode) -> Node:
        node.encode(self.encodeBuffer)
        encoded = self.popBufferValue()

        if len(encoded) < 32:
            return node
        return self.hashData(encoded)
