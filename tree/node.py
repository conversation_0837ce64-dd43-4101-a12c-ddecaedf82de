from __future__ import annotations

import copy
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Tuple

from rlp import encode as rlp_encode

# Constants
NIL_VALUE_NODE = None  # Used when collapsing internal trie nodes for hashing
RLP_EMPTY_STRING = rlp_encode(b"")  # RLP encoded empty string
RLP_EMPTY_LIST = rlp_encode([])  # RLP encoded empty list


class Node(ABC):
    """Base class for all trie nodes"""

    @abstractmethod
    def encode(self, buffer: bytearray) -> None:
        """Encode the node into RLP format"""
        raise NotImplementedError

    @abstractmethod
    def cache(self) -> Tuple[Optional[HashNode], bool]:
        """Return node's hash and dirty flag"""
        raise NotImplementedError

    @abstractmethod
    def copy(self) -> Node:
        """Return a copy of the node"""
        raise NotImplementedError


@dataclass
class NodeFlag:
    """Contains caching-related metadata about a node"""

    hash: Optional[HashNode] = None  # cached hash of the node
    dirty: bool = False  # whether the node has changes that must be written to the database


class FullNode(Node):
    """
    A full node in the trie with up to 17 children
    (16 nibbles + 1 value for leaf nodes)
    """

    def __init__(self):
        self.children: List[Optional[Node]] = [None] * 17
        self.flag = NodeFlag()

    def encode(self, buffer: bytearray) -> None:
        """
        Encode fullNode into RLP format
        """
        # Encode each child
        for child in self.children:
            if child is not None:
                child.encode(buffer)
            else:
                buffer.extend(RLP_EMPTY_STRING)

    def copy(self) -> FullNode:
        """Return a deep copy of the node"""
        new_node = FullNode()
        new_node.children = copy.deepcopy(self.children)
        new_node.flag = copy.deepcopy(self.flag)
        return new_node

    def cache(self) -> Tuple[Optional[HashNode], bool]:
        return self.flag.hash, self.flag.dirty


class ShortNode(Node):
    """
    A short node in the trie (extension or leaf node)
    Contains a key and a value
    """

    def __init__(self, key: bytes, val: Optional[Node] = None):
        self.key = key
        self.val = val
        self.flag = NodeFlag()

    def encode(self, buffer: bytearray) -> None:
        """
        Encode shortNode into RLP format
        """
        buffer.extend(list(rlp_encode(self.key)))
        if self.val is not None:
            self.val.encode(buffer)
        else:
            buffer.extend(RLP_EMPTY_STRING)

    def copy(self) -> ShortNode:
        """Return a deep copy of the node"""
        new_node = ShortNode(self.key, copy.deepcopy(self.val))
        new_node.flag = copy.deepcopy(self.flag)
        return new_node

    def cache(self) -> Tuple[Optional[HashNode], bool]:
        return self.flag.hash, self.flag.dirty


class HashNode(Node):
    """A node represented by its hash"""

    def __init__(self, hash_value: bytes):
        self.value = hash_value

    def encode(self, buffer: bytearray) -> None:
        """
        Encode hashNode into RLP format
        """
        buffer.extend(rlp_encode(self.value))

    def copy(self) -> HashNode:
        """Return a copy of the node"""
        return HashNode(self.value)

    def cache(self) -> Tuple[Optional[HashNode], bool]:
        return None, True


class ValueNode(Node):
    """A leaf node containing a value"""

    def __init__(self, value: Optional[bytes] = None):
        self.value = value

    def encode(self, buffer: bytearray) -> None:
        """
        Encode valueNode into RLP format
        """
        if self.value is not None:
            buffer.extend(rlp_encode(self.value))
        else:
            buffer.extend(RLP_EMPTY_STRING)

    def copy(self) -> ValueNode:
        """Return a copy of the node"""
        return ValueNode(self.value)

    def cache(self) -> Tuple[Optional[HashNode], bool]:
        return None, True


class RawNode(Node):
    """A node represented by its raw RLP encoding"""

    def __init__(self, raw_value: bytes):
        self.value = raw_value

    def encode(self, buffer: bytearray) -> None:
        """
        Encode rawNode into RLP format
        """
        buffer.extend(self.value)  # Already RLP encoded

    def copy(self) -> RawNode:
        """Return a copy of the node"""
        return RawNode(self.value)

    def cache(self) -> Tuple[Optional[HashNode], bool]:
        raise ValueError("RawNode does not support caching")
