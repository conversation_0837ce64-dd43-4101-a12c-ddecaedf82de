from .utils import assertBytes


def keybytesToHex(key: bytes) -> bytes:
    """
    Key hex encoding.

    Convert a byte string to its hexadecimal nibble representation.
    Each byte is split into two nibbles and a terminator (16) is added.
    """
    assertBytes(key)

    length = len(key) * 2 + 1
    nibbles = bytearray(length)
    for index, byte in enumerate(key):
        nibbles[index * 2] = byte >> 4  # High nibble
        nibbles[index * 2 + 1] = byte & 0x0F  # Low nibble
    nibbles[length - 1] = 16  # Add terminator
    return bytes(nibbles)


def hexToKeybytes(hex_key: bytes) -> bytes:
    """
    Key hex decoding.

    Convert hex nibbles back to key bytes.
    This can only be used for keys of even length.
    """
    assertBytes(hex_key)

    if hasTerminator(hex_key):
        hex_key = hex_key[:-1]
    if len(hex_key) % 2 != 0:
        raise ValueError("hex key must have even length")

    key = bytearray(len(hex_key) // 2)
    for i in range(0, len(hex_key), 2):
        key[i // 2] = (hex_key[i] << 4) | hex_key[i + 1]
    return bytes(key)


def hexToCompact(hex: bytes) -> bytes:
    """
    Hex-Prefix Encoding.

    Convert hex nibbles to compact encoding.
    The compact encoding may contain a terminator marker.
    """
    assertBytes(hex)

    hasTerm = hasTerminator(hex)
    if hasTerm:
        hex = hex[:-1]

    buf = bytearray(len(hex) // 2 + 1)
    buf[0] = 16 * (len(hex) % 2)  # Set odd flag if length is odd
    if hasTerm:
        buf[0] += 32  # Set terminator flag

    if len(hex) % 2 == 1:
        buf[0] += hex[0]  # Include first nibble in flag byte
        hex = hex[1:]

    # Pack remaining nibbles
    for i in range(0, len(hex), 2):
        buf[i // 2 + 1] = (hex[i] << 4) | hex[i + 1]

    return bytes(buf)


def compactToHex(compact: bytes) -> bytes:
    """
    Hex-Prefix Decoding.

    Convert compact encoding back to hex nibbles.
    """
    assertBytes(compact)

    if len(compact) == 0:
        return compact

    base = keybytesToHex(compact)
    # Delete terminator flag
    if base[0] < 2:
        base = base[:-1]
    # Apply odd flag
    chop = 2 - (base[0] & 1)
    return base[chop:]


def hasTerminator(s: bytes) -> bool:
    """Check if a hex key has the terminator flag."""
    assertBytes(s)
    return len(s) > 0 and s[-1] == 16
