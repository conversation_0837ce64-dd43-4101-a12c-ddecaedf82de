from __future__ import annotations

import copy
import traceback
import uuid
from typing import Any, AnyStr, Dict, Optional, Tuple, Type

import wasmtime
from kivy import Logger
from wasmtime import Config, Engine, Instance, Linker, Module, Store, WasiConfig

import common
import rawdb
import state.statedb
import vgraphdb

# Import our memory management system
from cache_manager import Cache<PERSON>anager
from cache_manager.core.cache import AbstractCache
from common import EmptyRootHash, bytesToHex, fromJson, hexToAddress, hexToBytes, toJson

from . import contract_code, types, utils

enableModuleCache = True
moduleCacheSize = 50  # the quantity of cache module


class GlueEngine:
    """
    this is a singleton wasmtime engine instance for all wasm contract
    wasm module can be cached in the engine to improve performance
    if engine instance is not same instance, module cache will not work
    """

    _instance = None  # the singleton instance

    def __new__(cls):
        # Use Singleton pattern, return the same instance
        if cls._instance is None:
            cls._instance = super(GlueEngine, cls).__new__(cls)
            engineConfig = Config()
            # enable cache for wasm module
            engineConfig.cache = True
            # enable consume fuel
            engineConfig.consume_fuel = True
            engine = Engine(engineConfig)
            cls._instance.engine = engine
        return cls._instance


class ContractExecutor:
    """
    A class used to execute smart contracts.

    Attributes
    ----------
    address : bytes
        The address of the contract.
    """

    moduleCache: AbstractCache[bytes, Module] = CacheManager.createCache(
        name="module_cache", cacheType="lru", maxSize=moduleCacheSize
    )

    def __init__(self, address: AnyStr):
        """
        Initialize a ContractExecutor instance.

        Parameters
        ----------
        address : AnyStr
            The address of the contract.
        """
        if isinstance(address, str):
            address = hexToBytes(address)
        self.address: bytes = address
        self.addressHex: str = bytesToHex(address)

    @staticmethod
    def __invokeWasm(
        envs: Dict[str, str], store: Store, instance: Instance, funcName: str, *args: Any
    ) -> Tuple[Optional[Any], Optional[str]]:
        """
        Invoke a specified function within a WebAssembly module,
        handling the serialization and deserialization of arguments and the return value.

        This function serializes arguments into a JSON string format, passes them to the specified WASM function,
        and then deserializes the response back into a common json c-char pointer.

        Parameters
        ----------
        envs : Dict[str, str]
            The environment variables for the WASM function.
        store : Store
            The WebAssembly Store object.
        instance : Instance
            The WebAssembly Instance object containing exported functions.
        funcName : str
            The name of the function to be invoked in the WASM module.
        args : Any
            The arguments to be passed to the function.

        Returns
        -------
        Tuple[Optional[Any], Optional[str], Dict[str, Any]]
            The result from the WASM function, is a json str.

            An optional error message from the WASM function.

            A dictionary containing the changes made to the state during the function call.
        """
        exports = instance.exports(store)
        func = exports[funcName]

        # helper functions
        allocateCChar = exports["allocate_c_char"]
        deallocateCChar = exports["deallocate_c_char"]

        memory = exports["memory"]

        # deserialize args tuple to json string
        argsJson = toJson(types.FuncArgs(args, envs))
        # allocate memory for json string
        argsJsonPtr = allocateCChar(store, len(argsJson) + 1)
        # write json string into memory
        utils.writeStr(memory, store, argsJson, argsJsonPtr)

        # invoke function with args json string
        resultPtr = func(store, argsJsonPtr)

        # no need to deallocate memory for arg json string
        # because it auto managed by the rust code

        # read result json string from memory
        resultJson = utils.getStr(memory, store, resultPtr)
        # deallocate memory for resultPtr
        deallocateCChar(store, resultPtr)

        serializableResult: types.SerializableResult = fromJson(types.SerializableResult, resultJson)

        return serializableResult.value, serializableResult.err

    def _executeRaw(
        self,
        state: state.StateDB,
        fuel: int,
        readonly: bool,
        envs: Optional[Dict[str, str]],
        funcName: str,
        returnType: Optional[Type],
        *args: Any,
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a raw function call within a WebAssembly module.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        fuel : int
            Set fuel for the function call.
        readonly : bool
            Whether the function call is read-only.
        envs : Optional[Dict[str, str]], optional
            The environment variables for the function call, by default None.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type]
            The expected return type of the function.
        args : Any
            The arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        code = state.getCode(self.address)
        if code is None:
            # If the contract code is not found, return an error message
            return None, f"Contract code not found for address {bytesToHex(self.address)}"
        self.code: bytes = code
        if enableModuleCache and ContractExecutor.moduleCache.contains(self.address):
            self.module = ContractExecutor.moduleCache.get(self.address)
        else:
            self.module: Module = Module(GlueEngine().engine, self.code)
            if enableModuleCache:
                ContractExecutor.moduleCache.put(self.address, self.module)

        finalEnv: Dict = {}

        if envs is not None:
            for k, v in envs.items():
                finalEnv[k] = v

        if readonly:
            finalEnv["readonly"] = "true"

        # if not define the sender, set it to default_sender
        if "sender" not in finalEnv:
            finalEnv["sender"] = "default_sender"

        if "callers" not in finalEnv:
            finalEnv["callers"] = finalEnv["sender"]

        # wasm runtime context
        store = None
        memory = None
        allocateCChar = None
        deallocateCChar = None
        isReadOnlyPanic = False

        def readonlyPanic():
            """
            This function raises a panic when trying to call an atomic function in read-only mode.
            It sets the global flag `isReadOnlyPanic` to True, indicating that a read-only violation occurred.
            """
            nonlocal isReadOnlyPanic
            isReadOnlyPanic = True

        def getSlot(keyPtr: int) -> int:
            """
            Retrieves the value of a data slot from the state using a key pointer.
            If the key exists in the state, it returns a pointer to the value.
            If the key does not exist, it returns a pointer to an empty string.

            Parameters:
            keyPtr: The pointer to the key for which the value is being fetched.

            Returns:
            valuePtr: A pointer to the value of the key in the state or 0 if not found.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            key = utils.getStr(memory, store, keyPtr).encode()
            deallocateCChar(store, keyPtr)
            valueBytes = state.getState(self.address, key)
            if valueBytes is None:
                return 0
            value = valueBytes.decode()
            valuePtr = allocateCChar(store, len(value) + 1)
            utils.writeStr(memory, store, value, valuePtr)
            return valuePtr

        def putSlot(keyPtr: int, valuePtr: int):
            """
            Puts a key-value pair into the state data slot.
            Takes two pointers: one for the key and one for the value.

            Parameters:
            keyPtr: The pointer to the key to be inserted.
            valuePtr: The pointer to the value to be inserted.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            key = utils.getStr(memory, store, keyPtr).encode()
            value = utils.getStr(memory, store, valuePtr).encode()
            deallocateCChar(store, keyPtr)
            deallocateCChar(store, valuePtr)
            state.setState(self.address, key, value)

        def deleteSlot(keyPtr: int):
            """
            Deletes a key-value pair from the state data slot using the key pointer.

            Parameters:
            keyPtr: The pointer to the key that needs to be deleted from the state.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            key = utils.getStr(memory, store, keyPtr).encode()
            deallocateCChar(store, keyPtr)
            state.setState(self.address, key, b"")

        def debugger(ptr: int):
            """
            Debugger function to print wasm code debug info.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            debuggerString = utils.getStr(memory, store, ptr)
            deallocateCChar(store, ptr)
            Logger.debug(f"WASM DEBUGGER: {debuggerString}")

        def delegateCall(callPtr: int) -> int:
            """
            Executes a delegate call to another contract.
            This function takes a pointer to a serialized JSON object containing the call details.
            It deserializes the JSON, sets up the environment, and invokes the delegate contract.

            Parameters:
            callPtr: The pointer to the serialized JSON object representing the delegate call.

            Returns:
            resultPtr: A pointer to the serialized JSON result of the delegate call.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar, finalEnv, readonly
            # get rest fuel from store and pass fuel param to delegate call _executeRaw
            restFuel = store.get_fuel()
            callJson = utils.getStr(memory, store, callPtr)
            call = fromJson(types.DelegateCall, callJson)
            deallocateCChar(store, callPtr)
            delegateContract = ContractExecutor(call.contract_address)

            # copy the finalEnv and override the contract_address, sender, callers
            callEnvs = {
                **copy.deepcopy(finalEnv),
                "contract_address": call.contract_address,
                "sender": finalEnv["sender"],
                # callers(caller stack) = original callers + "," + contract_address who delegate call current contract
                "callers": finalEnv["callers"] + "," + finalEnv["contract_address"],
            }

            result, err = delegateContract._executeRaw(
                state, restFuel, readonly, callEnvs, call.function_name, Any, *call.args
            )
            resultJson = toJson(types.DelegateResult(result, err))
            resultPtr = allocateCChar(store, len(resultJson) + 1)
            utils.writeStr(memory, store, resultJson, resultPtr)
            return resultPtr

        # Index layer related functions
        indexIterCache: Dict[str, vgraphdb.DBIterator] = {}

        def putIndex(keyPtr: int, valuePtr: int):
            """
            Puts a key-value pair into the index layer.
            Takes two pointers: one for the key and one for the value.

            Parameters:
            keyPtr: The pointer to the key to be inserted.
            valuePtr: The pointer to the value to be inserted.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            key = utils.getStr(memory, store, keyPtr).encode()
            value = utils.getStr(memory, store, valuePtr).encode()
            deallocateCChar(store, keyPtr)
            deallocateCChar(store, valuePtr)
            # No need to pad the key to fixed length anymore

            state.setContractDataIndex(self.address, key, value)

        def deleteIndex(keyPtr: int):
            """
            Deletes a key-value pair from the index layer using the key pointer.

            Parameters:
            keyPtr: The pointer to the key that needs to be deleted from the index layer.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar
            key = utils.getStr(memory, store, keyPtr).encode()
            deallocateCChar(store, keyPtr)

            state.removeContractDataIndex(self.address, key)

        def getIndexIter(reverse: bool, startPtr: int, endPtr: int) -> int:
            """
            Retrieves an iterator for the index layer.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar, indexIterCache
            start = utils.getStr(memory, store, startPtr).encode()
            end = utils.getStr(memory, store, endPtr).encode()
            deallocateCChar(store, startPtr)
            deallocateCChar(store, endPtr)

            # The underlying LmdbReverseIterator and LmdbIterator classes can handle empty start and end values correctly
            # For reverse iteration:
            # - Empty start means start from the last key (with the prefix if specified)
            # - Empty end means iterate all the way to the first key (with the prefix if specified)
            # For forward iteration:
            # - Empty start means start from the first key (with the prefix if specified)
            # - Empty end means iterate all the way to the last key (with the prefix if specified)
            # No need to set special values for empty start or end

            iterId = common.keccak256Hex(start + end)
            indexIterCache[iterId] = state.iterateContractDataIndexes(self.address, reverse, start, end)
            resultPtr = allocateCChar(store, len(iterId) + 1)
            utils.writeStr(memory, store, iterId, resultPtr)
            return resultPtr

        def getIterKey(iterIdPtr: int) -> int:
            """
            Retrieves the key of the current index iterator.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar, indexIterCache
            iterId = utils.getStr(memory, store, iterIdPtr)
            deallocateCChar(store, iterIdPtr)
            iter = indexIterCache.get(iterId, None)
            if iter is None:
                return 0
            iterKey = iter.key()
            if iterKey is None:
                return 0
            indexKey = iterKey[common.AddressLength + 1 :]
            keyPtr = allocateCChar(store, len(indexKey) + 1)
            utils.writeStr(memory, store, indexKey, keyPtr)
            return keyPtr

        def getIterValue(iterIdPtr: int) -> int:
            """
            Retrieves the value of the current index iterator.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar, indexIterCache
            iterId = utils.getStr(memory, store, iterIdPtr)
            deallocateCChar(store, iterIdPtr)
            iter = indexIterCache.get(iterId, None)
            if iter is None:
                return 0
            key = iter.value()
            if key is None:
                return 0
            valueBytes = state.getState(self.address, key)
            if valueBytes is None:
                return 0
            value = valueBytes.decode()
            valuePtr = allocateCChar(store, len(value) + 1)
            utils.writeStr(memory, store, value, valuePtr)
            return valuePtr

        def iterNext(iterIdPtr: int) -> int:
            """
            Moves the index iterator to the next key-value pair.
            """
            nonlocal memory, store, allocateCChar, deallocateCChar, indexIterCache
            iterId = utils.getStr(memory, store, iterIdPtr)
            deallocateCChar(store, iterIdPtr)
            iter = indexIterCache.get(iterId, None)
            if iter is None:
                return 0
            if not iter.next():
                indexIterCache.pop(iterId, None)
                return 0
            return 1

        def emitEvent(eventPtr: int):
            nonlocal memory, store, allocateCChar, deallocateCChar
            eventJson = utils.getStr(memory, store, eventPtr)
            event = fromJson(types.SerializedEvent, eventJson)
            deallocateCChar(store, eventPtr)
            state.addLog(event.toLog(self.address))

        # init wasmtime
        linker = Linker(GlueEngine().engine)
        linker.define_wasi()

        # define host functions
        getSlotFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [wasmtime.ValType.i32()])
        putSlotFuncType = wasmtime.FuncType([wasmtime.ValType.i32(), wasmtime.ValType.i32()], [])
        deleteSlotFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [])

        readonlyPanicFuncType = wasmtime.FuncType([], [])
        delegateCallFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [wasmtime.ValType.i32()])

        # define index layer functions
        putIndexFuncType = wasmtime.FuncType([wasmtime.ValType.i32(), wasmtime.ValType.i32()], [])
        deleteIndexFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [])
        debuggerFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [])
        getIndexIterFuncType = wasmtime.FuncType(
            [wasmtime.ValType.i32(), wasmtime.ValType.i32(), wasmtime.ValType.i32()], [wasmtime.ValType.i32()]
        )
        getIterValueFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [wasmtime.ValType.i32()])
        iterNextFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [wasmtime.ValType.i32()])

        # define event functions
        emitEventFuncType = wasmtime.FuncType([wasmtime.ValType.i32()], [])

        # put the relative host functions into linker
        linker.define_func("env", "get_slot", getSlotFuncType, getSlot)
        linker.define_func("env", "put_slot", putSlotFuncType, putSlot)
        linker.define_func("env", "delete_slot", deleteSlotFuncType, deleteSlot)

        linker.define_func("env", "readonly_panic", readonlyPanicFuncType, readonlyPanic)
        linker.define_func("env", "delegate_call", delegateCallFuncType, delegateCall)
        linker.define_func("env", "debugger", debuggerFuncType, debugger)

        linker.define_func("env", "put_index", putIndexFuncType, putIndex)
        linker.define_func("env", "delete_index", deleteIndexFuncType, deleteIndex)
        linker.define_func("env", "get_index_iter", getIndexIterFuncType, getIndexIter)
        linker.define_func("env", "get_iter_key", getSlotFuncType, getIterKey)
        linker.define_func("env", "get_iter_value", getIterValueFuncType, getIterValue)
        linker.define_func("env", "iter_next", iterNextFuncType, iterNext)

        linker.define_func("env", "emit", emitEventFuncType, emitEvent)

        wasi = WasiConfig()
        wasi.inherit_stdout()

        store = Store(GlueEngine().engine)
        store.set_wasi(wasi)

        store.set_fuel(fuel)

        instance = linker.instantiate(store, self.module)

        memory = instance.exports(store)["memory"]
        allocateCChar = instance.exports(store)["allocate_c_char"]
        deallocateCChar = instance.exports(store)["deallocate_c_char"]

        try:
            resultJsonStr, err = self.__invokeWasm(finalEnv, store, instance, funcName, *args)

            # calculate the fuel used
            usedFuel = fuel - store.get_fuel()
            Logger.info(
                f"Contract Executor: execute {'readonly ' if readonly else ''}function {funcName} consume fuel: {usedFuel}"
            )
            # deserialize result json string to result object
            result = None
            if returnType is not None and resultJsonStr is not None:
                result = fromJson(returnType, resultJsonStr)

            if err is not None:
                Logger.warning(f"Contract Executor: Contract error: {err}")
                return None, err

            return result, None
        except Exception as e:  # noqa
            if isReadOnlyPanic:
                e = "Cannot execute non-readonly contract function."
                Logger.warning(f"Contract Executor: Contract error: {e}")
            else:
                tb = "".join(traceback.format_exception(type(e), e, e.__traceback__))
                Logger.error(f"Contract Executor: Internal error: {e}, traceback: {tb}")
            return None, str(e)
        finally:
            # explicit cleanup references, otherwise will cause memory leak
            store = None
            memory = None
            allocateCChar = None
            deallocateCChar = None
            indexIterCache = {}

    def executeWithEnv(
        self,
        state: state.StateDB,
        fuel: int,
        envs: Optional[Dict[str, str]],
        funcName: str,
        returnType: Optional[Type],
        *args: Any,
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a specified function within a WebAssembly module (with one instance and store),
        handling the serialization and deserialization of arguments and the return value.

        If you want to execute the function in block level,
        you should pass the block envs and transaction envs by using mergeEnv.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        fuel : int
            Set fuel for the function call.
        envs : Optional[Dict[str, str]], optional
            The environment variables for the function call, by default None.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type]
            The expected return type of the function.
        args : Any
            The arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        if envs is None:
            envs = {}
        envs.update({"contract_address": self.addressHex})
        return self._executeRaw(state, fuel, False, envs, funcName, returnType, *args)

    def executeReadOnlyWithEnv(
        self,
        state: state.StateDB,
        envs: Optional[Dict[str, str]],
        funcName: str,
        returnType: Optional[Type],
        *args: Any,
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a read-only function within a WebAssembly module (with one instance and store),
        handling the serialization and deserialization of arguments and the return value.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        envs : Optional[Dict[str, str]], optional
            The environment variables for the function call, by default None.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type]
            The expected return type of the function.
        args : Any
            The arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        if envs is None:
            envs = {}
        envs.update({"contract_address": self.addressHex, "readonly": "true"})
        # readonly function is free and set default fuel to 10000000
        fuel = 100000000
        return self._executeRaw(state, fuel, True, envs, funcName, returnType, *args)

    def executeReadOnly(
        self, state: state.StateDB, funcName: str, returnType: Optional[Type] = None, *args
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a read-only function within a WebAssembly module.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        args : Any
            The arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return self.executeReadOnlyWithEnv(state, None, funcName, returnType, *args)

    def constructor(self, state: state.StateDB, *args: Any) -> Tuple[Any, Optional[str]]:
        """
        Call the constructor of the contract.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        args : Any
            Additional arguments to be passed to the constructor.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the constructor call and an optional error message.
        """
        return self.constructorWithEnv(state, None, None, *args)

    def constructorWithEnv(
        self, state: state.StateDB, fuel: Optional[int], envs: Optional[Dict[str, str]], *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Call the constructor of the contract with environment variables.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        fuel : Optional[int]
            Set fuel for the constructor call.
        envs : Optional[Dict[str, str]]
            The environment variables for the constructor call.
        args : Any
            Additional arguments to be passed to the constructor.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the constructor call and an optional error message.
        """
        if envs is None:
            envs = {}
        envs.update({"contract_address": self.addressHex, "register": "true"})
        # set default fuel to execute contract constructor
        if fuel is None or fuel <= 0:
            fuel = 1000000
        funcName = "new"
        return self._executeRaw(state, fuel, False, envs, funcName, None, *args)

    def execute(
        self, state: state.StateDB, fuel: int, funcName: str, returnType: Optional[Type] = None, *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a function in the contract.

        Parameters
        ----------
        state : state.StateDB
            The state database used for contract execution.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        args : Any
            Additional arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return self.executeWithEnv(state, fuel, None, funcName, returnType, *args)


def resetTesterState():
    """
    Reset the global state of the ContractTester. This is only used for testing purposes.
    """
    if ContractTester.state is not None:
        db = ContractTester.state.db
    else:
        db = state.newDatabase(rawdb.newMemorydb())
    ContractTester.state = state.statedb.StateDB(EmptyRootHash, db)


class ContractTester(ContractExecutor):
    """
    A class used to test smart contracts by inheriting from ContractExecutor.

    Attributes
    ----------
    state : state.StateDB
        The state database used for testing contracts.
    wasmName : str
        The file name of the wasm contract.
    """

    state: state.StateDB = None

    def __init__(self, wasmName: str, address: Optional[AnyStr] = None):
        """
        Initialize a ContractTester instance.

        Parameters
        ----------
        wasmName : str
            The name of the contract.
        address : Optional[AnyStr], optional
            The address of the contract, by default None. If None, it will be set to a random address.
        """
        if ContractTester.state is None:
            resetTesterState()
        if address is None:
            address = hexToAddress(uuid.uuid4().hex)
        elif isinstance(address, str):
            address = hexToBytes(address)
        self.wasmName = wasmName
        self.sender = "0x2228b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733111"
        self.state.setTxContext(
            txHash="0x02fc6598504e032b39711f0e2fb2356d8f8d0f88738c00a1fba67ba15e164cd0",
            txIndex=1,
        )
        super().__init__(address)

    def constructor(self, *args: Any) -> Tuple[Any, Optional[str]]:
        """
        Call the constructor of the contract.

        Parameters
        ----------
        *args
            Additional arguments to be passed to the constructor.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return self.constructorWithEnv(None, None, *args)

    def constructorWithEnv(
        self, fuel: Optional[int], envs: Optional[Dict[str, str]], *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Call the constructor of the contract with environment variables.

        Parameters
        ----------
        envs : Optional[Dict[str, str]], optional
            Environment variables to be passed to the constructor, by default None.
        fuel : Optional[int]
            Set fuel for the tester constructor call.
        *args
            Additional arguments to be passed to the constructor.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        snapshot = ContractTester.state.snapshot()
        contract_code.setTestContractCode(ContractTester.state, self.address, self.wasmName)
        resultTuple = super().constructorWithEnv(ContractTester.state, fuel, envs, *args)
        if resultTuple[1] is not None:
            ContractTester.state.revertToSnapshot(snapshot)
        return resultTuple

    def execute(self, funcName: str, returnType: Optional[Type] = None, *args: Any) -> Tuple[Any, Optional[str]]:
        """
        Execute a function in the contract.

        Parameters
        ----------
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        *args
            Additional arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return self.executeWithEnv(None, funcName, returnType, *args)

    def executeWithEnv(
        self, envs: Optional[Dict[str, str]], funcName: str, returnType: Optional[Type] = None, *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a function in the contract with environment variables.

        Parameters
        ----------
        envs : Optional[Dict[str, str]], optional
            Environment variables to be passed to the function, by default None.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        *args
            Additional arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        snapshot = ContractTester.state.snapshot()
        # set default fuel to execute contract function in tester
        fuel = 1_000_000_000_000

        resultTuple = super().executeWithEnv(ContractTester.state, fuel, envs, funcName, returnType, *args)
        if resultTuple[1] is not None:
            ContractTester.state.revertToSnapshot(snapshot)
        return resultTuple

    def executeReadOnly(
        self, funcName: str, returnType: Optional[Type] = None, *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a read-only function in the contract.

        Parameters
        ----------
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        *args
            Additional arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return self.executeReadOnlyWithEnv(None, funcName, returnType, *args)

    def executeReadOnlyWithEnv(
        self, envs: Optional[Dict[str, str]], funcName: str, returnType: Optional[Type] = None, *args: Any
    ) -> Tuple[Any, Optional[str]]:
        """
        Execute a read-only function in the contract with environment variables.

        Parameters
        ----------
        envs : Optional[Dict[str, str]], optional
            Environment variables to be passed to the function, by default None.
        funcName : str
            The name of the function to be executed.
        returnType : Optional[Type], optional
            The expected return type of the function, by default None.
        *args
            Additional arguments to be passed to the function.

        Returns
        -------
        Tuple[Any, Optional[str]]
            The result of the function call and an optional error message.
        """
        return super().executeReadOnlyWithEnv(ContractTester.state, envs, funcName, returnType, *args)
