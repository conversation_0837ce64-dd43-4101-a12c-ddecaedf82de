[project]
name = "vgraph"
version = "0.1.0"
description = ""
authors = []
readme = "README.md"
requires-python = ">=3.11, <3.12"
dependencies = [
    "aiorpcx>=0.25.0",
    "attrs>=25.3.0",
    "base58>=2.1.1",
    "cachetools>=6.1.0",
    "cython>=3.1.2",
    "fastapi>=0.115.14",
    "kivy==2.2.1",
    "lmdb>=1.6.2",
    "ntplib>=0.4.0",
    "orjson>=3.10.18",
    "py-vgraph",
    "pycryptodome>=3.23.0",
    "pyserde>=0.24.0",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "readerwriterlock>=1.0.9",
    "rlp>=4.1.0",
    "ruamel-yaml>=0.18.14",
    "rusty-rlp",
    "trie>=3.1.0",
    "uvicorn>=0.35.0",
    "uvloop>=0.21.0",
    "wasmtime>=34.0.0",
    "websockets>=15.0.1",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "pytest-benchmark>=5.1.0",
    "ruff>=0.12.1",
]

[tool.uv.sources]
rusty-rlp = { git = "https://github.com/zshimonz/rusty-rlp.git" }
py-vgraph = { git = "git+ssh://**************/virtualeconomy/py-vgraph.git", tag = "0.1.1" }


# A non-priority source for resolving Package docutils (0.21.post1) not found issue
# See issue: https://github.com/python-poetry/poetry/issues/9293
[[tool.poetry.source]]
name = "pypi-public"
url = "https://pypi.org/simple/"

[tool.poetry.dependencies]
kivy = "2.2.1"
cython = "*"
wasmtime = "*"
pyserde = "*"
orjson = "*"
aiorpcx = "*"
pyyaml = "*"
lmdb = "*"
pycryptodome = "*"
ntplib = "*"
base58 = "*"
attrs = "*"
rlp = "*"
trie = "*"
readerwriterlock = "*"
cachetools = "*"
python-multipart = "*"
fastapi = "*"
uvicorn = "*"
uvloop = "*"
websockets = "*"

# Add git dependencies
rusty-rlp = { git = "https://github.com/zshimonz/rusty-rlp.git" }
py-vgraph = { git = "git+ssh://**************/virtualeconomy/py-vgraph.git", tag = "0.1.1" }

# for vgraph watcher
ruamel-yaml = "^0.18.10"

[tool.poetry.group.dev.dependencies]
pytest = "*"
pytest-benchmark = "*"
pytest-asyncio = "*"
ruff = "*"

# pytest configuration
[tool.pytest.ini_options]
addopts = "--ignore=wasm/ --ignore=storage/vgraphdb/testsuite.py --ignore=.history/ --benchmark-skip"
asyncio_default_fixture_loop_scope = "function"
# TODO: `filterwarnings = ignore::DeprecationWarning:beartype` should be remove when all deprecation warnings are fixed
filterwarnings = ["ignore::DeprecationWarning:beartype"]

# ruff configuration
# See: https://docs.astral.sh/ruff/
# Usage:
#   - format code: `ruff format`
#   - lint code: `ruff check`
#   - lint code and fix: `ruff check --fix`
[tool.ruff]
target-version = "py311"  # The minimum Python version
line-length = 120  # The maximum line length
exclude = ["wasm", "udpHolePunching"] #  In addition to the standard set of exclusions

[tool.ruff.format]
docstring-code-format = true  # Enable reformatting of code snippets in docstring.
indent-style = "space"  # Use 4 space indentation.
quote-style = "double"  # Prefer double quotes for strings.

# Lint rule selection
# See: https://docs.astral.sh/ruff/rules/
# Select standard: open-source, GitHub star >= 1k, under maintenance
[tool.ruff.lint]
select = [
    "E", # pycodestyle(PEP8, Error)
    "W", # pycodestyle(PEP8, Warning)
    "F", # Pyflakes
    "I", # isort
    "B", # flake8-bugbear
]

# Skipped rules
ignore = [
    "E501", # Line length
    "B024", # Abstract base class without abstract methods
]
