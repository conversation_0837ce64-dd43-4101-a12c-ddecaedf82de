from typing import List, Optional, Tuple

from py_vgraph.signature import generate_keypair, private_key_to_public_key, sign_message, verify_batch_signature

from .models import SPOSHeader, Transaction
from .utils import bytesToHex, hexToBytes, publicKeyToAddress

SIGNING_CONTEXT = "ccbacbdc-e033-4c37-bd37-a1c69e159d9f"


def generateKeys() -> Tuple[str, str]:
    """
    generate a new key pair
    return the private key and public key
    """
    (publicKeyBytes, privateKeyBytes) = generate_keypair()
    publicKey = bytesToHex(publicKeyBytes)
    privateKey = bytesToHex(privateKeyBytes)
    return publicKey, privateKey


def privateKeyToPublicKey(privateKey: str) -> str:
    """
    Generate the public key from the given private key.

    Args:
        privateKey: A string representing the private key

    Returns:
        str: The corresponding public key

    Raises:
        Exception: If the public key generation process fails
    """
    publicKeyBytes = private_key_to_public_key(hexToBytes(privateKey))
    publicKey = bytesToHex(publicKeyBytes)
    return publicKey


def signMessage(message: str, privateKey: str) -> str:
    """
    Sign the message with the given private key.

    Params:
        message: message string to sign
        privateKey: hex string of the private key

    Returns:
        str: hex string of the signature

    Raises:
        Exception: If the message signing process fails
    """
    signatureBytes = sign_message(message.encode(), hexToBytes(privateKey), SIGNING_CONTEXT.encode())
    signature = bytesToHex(signatureBytes)
    return signature


def verifyBatchSignatures(messages: List[str], signatures: List[str], publicKeys: List[str]) -> bool:
    """
    Verify the signatures of multiple messages.

    Params:
        messages: list of encoded messages to verify
        signatures: list of hex strings of the signatures to verify
        publicKeys: list of hex strings of the public keys to verify

    Returns:
        bool: True if all signatures are valid, False otherwise

    Raises:
        Exception: If the batch signature verification process fails
    """
    result = verify_batch_signature(
        [message.encode() for message in messages],
        [hexToBytes(signature) for signature in signatures],
        [hexToBytes(publicKey) for publicKey in publicKeys],
        SIGNING_CONTEXT.encode(),
    )
    return result


def signTransactionWithPrivateKey(transaction: Transaction, privateKey: str) -> Transaction:
    """
    Sign the transaction with the given private key.

    Args:
        transaction: Transaction object to be signed
        privateKey: Private key to sign the transaction with

    Returns:
        Transaction: The signed transaction with signature and public key added

    Raises:
        Exception: If the transaction signing process fails

    Note: This function modifies the input transaction by adding the signature
    and corresponding public key to its signature lists
    """
    message = transaction.toSignableMessage()

    signature = signMessage(message, privateKey)
    # pack
    publicKey = privateKeyToPublicKey(privateKey)
    transaction.signatures.append(signature)
    transaction.public_keys.append(publicKey)
    return transaction


def verifyTransactionSignatures(transactions: List[Transaction]) -> bool:
    """
    Verify the signatures of multiple transactions.

    Args:
        transactions: List of Transaction objects to verify

    Returns:
        bool: True if all signatures are valid, False otherwise

    Raises:
        Exception: If the signature verification process fails

    Note: Does not support multi-signature with key aggregation or signature aggregation
    """
    if not transactions:
        return False

    messages: List[str] = []
    publicKeys: List[str] = []
    signatures: List[str] = []

    def _validateTransaction(transaction: Optional[Transaction]) -> bool:
        if not transaction:
            return False

        if (
            # public keys is required
            not transaction.public_keys
            # signatures is required
            or not transaction.signatures
            # public keys and signatures must have the same length
            or len(transaction.public_keys) != len(transaction.signatures)
        ):
            return False

        # public keys and signatures must not contain None
        if None in transaction.public_keys or None in transaction.signatures:
            return False

        # check if sender signs the transaction
        if transaction.sender not in map(publicKeyToAddress, transaction.public_keys):
            return False

        return True

    for transaction in transactions:
        if not _validateTransaction(transaction):
            return False

        message = transaction.toSignableMessage()  # Calculate once per transaction
        for publicKey, signature in zip(transaction.public_keys, transaction.signatures, strict=False):
            messages.append(message)
            publicKeys.append(publicKey)
            signatures.append(signature)

    # batch validation
    result = verifyBatchSignatures(messages, signatures, publicKeys)
    return result


def signBlockHeaderWithPrivateKey(header: SPOSHeader, privateKey: str) -> SPOSHeader:
    """
    Sign the block header with the given private key.

    Args:
        blockHeader: SPOSHeader object to be signed
        privateKey: Private key to sign the block header with

    Returns:
        SPOSHeader: The signed block header with signature and public key added

    Raises:
        Exception: If the block header signing process fails

    Note: This function modifies the input block header by adding the signature
    and corresponding public key to its signature lists
    """
    message = header.toSignableMessage()

    signature = signMessage(message, privateKey)

    # pack
    header.signatures.append(signature)
    header.public_keys.append(privateKeyToPublicKey(privateKey))
    return header


def verifyBlockHeaderSignature(blockHeader: SPOSHeader) -> bool:
    """
    Verify the signature of the block header.

    Args:
        blockHeader: SPOSHeader object to verify

    Returns:
        bool: True if all signatures are valid, False otherwise

    Raises:
        Exception: If the signature verification process fails

    Note: Verifies that the block header has matching numbers of public keys and signatures
    before performing batch signature verification
    """
    publicKeys = blockHeader.public_keys
    signatures = blockHeader.signatures

    if (
        # public keys is required
        not publicKeys
        # signatures is required
        or not signatures
        # public keys and signatures must have the same length
        or len(publicKeys) != len(signatures)
    ):
        return False

    message = blockHeader.toSignableMessage()
    messages = [message] * len(publicKeys)
    result = verifyBatchSignatures(messages, signatures, publicKeys)
    return result
