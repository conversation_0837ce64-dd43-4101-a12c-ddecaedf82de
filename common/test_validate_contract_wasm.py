import pytest
import wasmtime

from common.utils import (
    detectWasiFromBytes,
    detectWasiFromFile,
    getContractBytecode,
)

WASM_FILE_PATH = "contract/base/spos.wasm"


def test_detect_wasi_from_bytes_true():
    wasm_bytes = b"""
    (module
  (import "wasi_snapshot_preview1" "fd_write" (func $fd_write (param i32 i32 i32 i32) (result i32)))
  (func (export "main") (result i32) i32.const 42)
)
    """
    assert detectWasiFromBytes(wasm_bytes) is True


def test_detect_wasi_from_bytes_false():
    wat = b'(module (func (export "main") (result i32) i32.const 42))'
    wasm_bytes = wasmtime.wat2wasm(wat)
    assert detectWasiFromBytes(wasm_bytes) is False


def test_detect_wasi_from_bytes_exception():
    # invalid wasm bytecode
    test_bytes = b'(module (ffff (export "main") (result i32) i32.const 42))'
    with pytest.raises(Exception) as excinfo:
        detectWasiFromBytes(test_bytes)
    assert "Validate WASM bytecode error" in str(excinfo.value)


@pytest.mark.benchmark(group="get_contract_bytecode")
def test_get_contract_bytecode_benchmark(benchmark):
    """
    Benchmark: Only test the performance of reading wasm bytecode from file.
    """

    def get_wasm_bytes():
        getContractBytecode(WASM_FILE_PATH)

    benchmark(get_wasm_bytes)


@pytest.mark.benchmark(group="wasmtime_detect_wasi_from_bytes")
def test_wasmtime_detect_wasi_from_bytes_benchmark(benchmark):
    """
    Benchmark: Use wasmtime to detect WASI imports from bytes.
    """
    wasm_bytes = getContractBytecode(WASM_FILE_PATH)

    def check_wasi_wasmtime():
        result = detectWasiFromBytes(wasm_bytes)
        return result

    result = benchmark(check_wasi_wasmtime)
    assert result is False


@pytest.mark.benchmark(group="wasmtime_detect_wasi_from_file")
def test_wasmtime_detect_wasi_from_file_benchmark(benchmark):
    """
    Benchmark: Use wasmtime to detect WASI imports from file.
    """

    def check_wasi_wasmtime():
        result = detectWasiFromFile(WASM_FILE_PATH)
        return result

    result = benchmark(check_wasi_wasmtime)
    assert result is False
