import pytest

from chains.tests import TRANSACTIONS_200
from tree import <PERSON><PERSON><PERSON>
from tree.stacktrie.stacktrie import <PERSON><PERSON><PERSON><PERSON>

from . import deriveSha
from .models import Transactions


def test_derive_sha_correctness():
    transactions = Transactions(TRANSACTIONS_200)
    test = deriveSha(transactions, StackTrie())
    expected = deriveSha(transactions, MemoryTrie())
    assert expected == test


@pytest.mark.benchmark(group="encode")
def test_derive_sha_memory(benchmark):
    transactions = Transactions(TRANSACTIONS_200)
    benchmark(deriveSha, transactions, MemoryTrie())


@pytest.mark.benchmark(group="encode")
def test_derive_sha_stack(benchmark):
    transactions = Transactions(TRANSACTIONS_200)
    benchmark(deriveSha, transactions, StackTrie())
