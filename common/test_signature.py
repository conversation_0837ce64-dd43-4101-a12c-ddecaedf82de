import pytest

from chains.tests import TRANSACTIONS_200
from common.encode import hexToBytes
from common.models import Bloom<PERSON>ilter, OperationCallContract, OperationType, SPOSHeader, Transaction
from common.signature import (
    generateKeys,
    privateKeyToPublicKey,
    signBlockHeaderWithPrivateKey,
    signTransactionWithPrivateKey,
    verifyBlockHeaderSignature,
    verifyTransactionSignatures,
)
from common.utils import publicKeyToAddress


@pytest.mark.benchmark(group="signature")
def test_verify_transaction_signatures_benchmark(benchmark):
    """
    Benchmark the verifyTransactionSignatures function

    This test now exclusively uses py_vgraph.signature for all cryptographic operations.
    The legacy getSchnorrClient implementation has been removed for better performance and maintainability.
    """
    benchmark(verifyTransactionSignatures, TRANSACTIONS_200)


def test_generate_keys():
    publicKey, privateKey = generateKeys()
    assert publicKey is not None
    assert privateKey is not None
    # length of public key bytes should be 32 (64 hex chars)
    assert len(publicKey) == 66  # with "0x" prefix
    # length of private key bytes should be 64 (128 hex chars)
    assert len(privateKey) == 130  # with "0x" prefix


def test_private_key_to_public_key():
    # First generate a keypair
    publicKey, privateKey = generateKeys()
    # Then derive public key from private key
    derivedPublicKey = privateKeyToPublicKey(privateKey)
    # Should match original public key
    assert derivedPublicKey == publicKey


def test_sign_and_verify_transaction():
    # Generate a keypair for testing
    publicKey, privateKey = generateKeys()
    address = publicKeyToAddress(publicKey)

    transaction = Transaction(
        dependent_transaction_hash="",
        sender=address,
        op_data=OperationCallContract(
            op_type=OperationType.CALL_CONTRACT,
            contract_address="0x09794417023c56f1eaf06996fa1937f8fc541966ac2c21bd2bd94007fdb3f7f3",
            function_name="issue",
            # issue enough fuels to ADDRESS1 to pay for the `transfer` transaction
            parameters=[address, 1000000],
        ),
        public_keys=[],
        signatures=[],
        timestamp=1,
        fuel=300000,
    )

    # Create and sign transaction
    signedTransaction = signTransactionWithPrivateKey(transaction, privateKey)

    # Verify the transaction has signature and public key
    assert len(signedTransaction.signatures) == 1
    assert len(signedTransaction.public_keys) == 1
    assert signedTransaction.public_keys[0] == publicKey

    # Verify the signature
    assert verifyTransactionSignatures([signedTransaction]) is True

    # Test invalid signature
    signedTransaction.signatures[0] = "0x" + "00" * 64
    with pytest.raises(ValueError):
        verifyTransactionSignatures([signedTransaction])


def test_sign_and_verify_block_header():
    # Generate a keypair for testing
    publicKey, privateKey = generateKeys()

    # Create and sign block header
    header = SPOSHeader(
        parent_hash=hexToBytes("0x1234567890123456789012345678901234567890123456789012345678901234"),
        height=0,
        state_root="0x1234567890123456789012345678901234567890123456789012345678901234",
        transactions_root="0x1234567890123456789012345678901234567890123456789012345678901234",
        receipts_root="0x1234567890123456789012345678901234567890123456789012345678901234",
        bloom=BloomFilter(),
        local_timestamp=0,
        protocol_timestamp=0,
        slot_id=0,
        proposer_address=hexToBytes("0x1234567890abcdef"),
        public_keys=[],
        signatures=[],
    )
    signedHeader = signBlockHeaderWithPrivateKey(header, privateKey)

    # Verify the header has signature and public key
    assert len(signedHeader.signatures) == 1
    assert len(signedHeader.public_keys) == 1
    assert signedHeader.public_keys[0] == publicKey

    # Verify the signature
    assert verifyBlockHeaderSignature(signedHeader) is True

    # Test invalid signature
    signedHeader.signatures[0] = "0x" + "00" * 64
    with pytest.raises(ValueError):
        verifyBlockHeaderSignature(signedHeader)
