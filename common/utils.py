from hashlib import new as hashlib_new
from hashlib import sha256
from typing import AnyStr, Optional

import wasmtime
from Crypto.Hash import keccak

from .encode import bytesToHex, hexToBytes

wasi_namespaces = [
    "wasi_snapshot_preview1",
    "wasi_unstable",
    "wasi",
    "wasi_http",
    "wasi_cli",
    "wasi_io",
    "wasi_filesystem",
    "wasi_random",
    "wasi_time",
    "wasi_socket",
    "wasi_poll",
    "wasi_stdout",
    "wasi_stderr",
    "wasi_stdin",
]

# ----- hash functions -----


def keccak256(message: AnyStr) -> bytes:
    """
    return the keccak 256 hash of the message
    """
    if not isinstance(message, bytes):
        message = message.encode()
    keccak_hash = keccak.new(digest_bits=256)
    keccak_hash.update(message)
    return keccak_hash.digest()


def keccak256Hex(message: AnyStr) -> str:
    """
    return the keccak 256 hash of the message
    """
    if not isinstance(message, bytes):
        message = message.encode()
    keccak_hash = keccak.new(digest_bits=256)
    keccak_hash.update(message)
    return keccak_hash.hexdigest()


def doubleSha256(message: bytes) -> bytes:
    """
    return the double sha256 hash of the message
    """
    return sha256(sha256(message).digest()).digest()


def doubleSha256Hex(message: AnyStr) -> str:
    """
    return the hex hash of the message
    """
    if not isinstance(message, bytes):
        message = message.encode()
    return sha256(sha256(message).digest()).digest().hex()


def publicKeyToAddress(publicKey: str) -> Optional[str]:
    """
    Generate the address from the given public key.

    Args:
        publicKey: A string representing the public key

    Returns:
        Optional[str]: The generated address, or None if the public key is invalid

    Note: This is a temporary implementation. The real address generation needs to be implemented.
    The current implementation follows these steps:
    1. Perform SHA-256 hash on the public key
    2. Perform RIPEMD-160 hashing on the result of SHA-256
    3. Add network byte (0x00 for VGraph mainnet)
    4. Add checksum to the result of step 3
    5. Encode the result of step 4 with base58 encoding
    """
    try:
        publicKeyBytes = hexToBytes(publicKey)
    except Exception:
        return None

    addressSha256 = sha256(publicKeyBytes).digest()
    addressRipemd160 = hashlib_new("ripemd160", addressSha256).digest()
    networkByte = b"\x00"

    checksum = sha256(sha256(networkByte + addressRipemd160).digest()).digest()[:4]

    address = bytesToHex(addressRipemd160 + networkByte + checksum)
    return address


def getContractBytecode(filePath: str) -> bytes:
    """
    Reads a file and returns its content as bytes.

    Args:
        filePath (str): Path to the file.

    Returns:
        bytes: Bytes of the file's content.
    """
    with open(filePath, "rb") as f:
        contractCode = f.read()
    return contractCode


def validateWasmFromBytes(wasm_bytecode: bytes) -> wasmtime.Module:
    """
    This function is used to validate the WASM bytecode, and return the validated WASM module.
    It will raise an exception if the WASM bytecode is invalid.

    Args:
        wasm_bytecode (bytes): The bytecode of the WASM file.

    Returns:
        wasmtime.Module: The validated WASM module.
    """
    try:
        # Create a Store. Store is the context for Wasmtime runtime environment.
        store = wasmtime.Store()
        # Load WASM module from bytecode. This only parses and validates the module, doesn't execute it.
        module = wasmtime.Module(store.engine, wasm_bytecode)
        return module
    except Exception as e:
        raise Exception(f"Validate WASM bytecode error: {e}") from e


def detectWasiFromBytes(wasm_bytecode: bytes) -> bool:
    """
    This function is used to detect if the WASM bytecode uses WASI interfaces.

    Args:
        wasm_bytecode (bytes): The bytecode of the WASM file.

    Returns:
        bool: True if WASI interfaces are used, False otherwise.
    """
    # validate wasm bytecode
    module = validateWasmFromBytes(wasm_bytecode)

    # check if any import in the module uses a WASI namespace
    result = any(import_type.module in wasi_namespaces for import_type in module.imports)
    return result


def validateWasmFromFile(wasm_filepath: str) -> wasmtime.Module:
    """
    This function is used to validate the WASM file, and return the validated WASM module.
    It will raise an exception if the WASM file is invalid.
    """
    try:
        # Create a Store. Store is the context for Wasmtime runtime environment.
        store = wasmtime.Store()
        # Load WASM module from file. This only parses and validates the module, doesn't execute it.
        module = wasmtime.Module.from_file(store.engine, wasm_filepath)
        return module
    except Exception as e:
        raise Exception(f"Validate WASM file error: {e}") from e


def detectWasiFromFile(wasm_filepath: str) -> bool:
    """
    Detect if a WASM file uses WASI interfaces.

    Args:
        wasm_filepath (str): Path to the WASM file.

    Returns:
        bool: True if WASI interfaces are used, False otherwise.
    """
    # validate wasm file
    module = validateWasmFromFile(wasm_filepath)
    # check if any import in the module uses a WASI namespace
    result = any(import_type.module in wasi_namespaces for import_type in module.imports)
    return result


# ----- misc -----


def getRequiredKey(d: dict, key: str):
    """
    Return the value of the key in the dictionary.
    If the key is not found, raise an exception.
    """
    if key not in d:
        raise Exception(f"Key {key} not found in the dictionary")
    return d[key]
