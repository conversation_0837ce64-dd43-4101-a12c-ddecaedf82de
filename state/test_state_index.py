"""
Test suite for contract data index operations in the state package.

This module contains comprehensive tests for the contract data index functionality,
including basic CRUD operations, snapshots, undo logs, and rollbacks. It verifies
the correctness of index operations across multiple contracts and blocks, as well
as edge cases and error recovery scenarios.

The tests use a memory database to simulate the blockchain state and verify that
all operations behave as expected, particularly focusing on the ability to roll
back changes to previous states.

Key areas tested:
- Basic index operations (set, get, remove)
- Index iteration
- Snapshots and reverts
- Undo log generation and application
- Multi-block and multi-contract rollbacks
- Edge cases and error handling
- Performance with large data volumes
"""

from __future__ import annotations

from typing import Tuple

import eth_utils
import pytest

import common
import rawdb
from rawdb.accessors_data_indexes import readContractDataIndex
from state import index_undo_log, journal, newDatabase, statedb
from state.index_undo_log import IndexOperationType


@pytest.fixture
def setupState():
    """
    Setup a clean state environment for testing.
    Returns a tuple of (state, contractAddress, db).
    """
    # Create memory database
    db = rawdb.newMemorydb()

    # Create database wrapper
    sdb = newDatabase(db)

    # Create empty StateDB
    state = statedb.StateDB(common.EmptyRootHash, sdb)

    # Test contract address
    contractAddress = eth_utils.keccak(b"test_contract")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20

    return state, contractAddress, sdb


def createIndex(indexNum: int) -> Tuple[bytes, bytes]:
    """
    Create test index and data key.

    Args:
        indexNum: Index number to use

    Returns:
        Tuple of (index, dataKey). Index is variable length but less than 255 bytes.
    """
    # Index key can be any length up to 255 bytes
    indexPrefix = b"test_index_"  # 11 bytes
    # Convert indexNum to string
    indexSuffix = str(indexNum).encode()

    # No need to pad to fixed length anymore
    index = indexPrefix + indexSuffix
    dataKey = b"data_key_" + str(indexNum).encode()

    assert len(index) < 255, f"Index must be less than 255 bytes, got {len(index)}"
    return index, dataKey


def testMultipleStateInstances(setupState):
    """
    Test that multiple state instances can modify indexes independently without affecting each other.
    This test verifies that when multiple state instances are created and modify the same or different
    indexes, the changes remain isolated to each instance until committed.
    """
    # Get the initial state and contract address
    originalState, contractAddress, db = setupState

    # Create two more state instances from the same root
    state1 = statedb.StateDB(originalState.originalRoot, db)
    state2 = statedb.StateDB(originalState.originalRoot, db)

    # Create test indexes
    numIndexes = 5
    indexes = []
    dataKeys1 = []
    dataKeys2 = []

    # Set up indexes with different values in each state
    for i in range(numIndexes):
        index, _ = createIndex(i)
        indexes.append(index)

        # Create different data keys for each state
        dataKey1 = b"state1_data_" + str(i).encode()
        dataKey2 = b"state2_data_" + str(i).encode()

        dataKeys1.append(dataKey1)
        dataKeys2.append(dataKey2)

        # Set indexes in different order to test isolation
        if i % 2 == 0:
            # Even indexes: set state1 first, then state2
            state1.setContractDataIndex(contractAddress, index, dataKey1)
            state2.setContractDataIndex(contractAddress, index, dataKey2)
        else:
            # Odd indexes: set state2 first, then state1
            state2.setContractDataIndex(contractAddress, index, dataKey2)
            state1.setContractDataIndex(contractAddress, index, dataKey1)

    # Verify each state has its own independent view of the indexes
    for i, index in enumerate(indexes):
        # Check state1
        value1 = state1.getContractDataIndex(contractAddress, index)
        assert value1 == dataKeys1[i], f"State1 index {i} should have value {dataKeys1[i]}, got {value1}"

        # Check state2
        value2 = state2.getContractDataIndex(contractAddress, index)
        assert value2 == dataKeys2[i], f"State2 index {i} should have value {dataKeys2[i]}, got {value2}"

        # Original state should not have these indexes
        valueOrig = originalState.getContractDataIndex(contractAddress, index)
        assert valueOrig is None, f"Original state should not have index {i}, but got {valueOrig}"

    # Test removing indexes in one state doesn't affect others
    removeIndex = indexes[2]  # Remove the third index
    state1.removeContractDataIndex(contractAddress, removeIndex)

    # Verify it's removed from state1 but still exists in state2
    assert state1.getContractDataIndex(contractAddress, removeIndex) is None, "Index should be removed from state1"
    assert state2.getContractDataIndex(contractAddress, removeIndex) == dataKeys2[2], (
        "Index should still exist in state2"
    )

    # Test committing changes from one state
    blockNum = 123
    state1.commit(blockNum)

    # Create a new state from the updated root
    newState = statedb.StateDB(state1.originalRoot, db)

    # Verify the new state has the committed changes from state1
    for i, index in enumerate(indexes):
        if i == 2:  # The index we removed
            assert newState.getContractDataIndex(contractAddress, index) is None, (
                f"Removed index {i} should not exist in new state"
            )
        else:
            value = newState.getContractDataIndex(contractAddress, index)
            assert value == dataKeys1[i], f"New state index {i} should have value {dataKeys1[i]}, got {value}"

    # Verify state2 is still independent and unaffected
    for i, index in enumerate(indexes):
        value2 = state2.getContractDataIndex(contractAddress, index)
        assert value2 == dataKeys2[i], f"State2 index {i} should still have value {dataKeys2[i]}, got {value2}"

    # Test iterating through indexes in different states
    # State1 (after commit)
    iterator1 = state1.iterateContractDataIndexes(contractAddress)
    retrieved1 = []
    while iterator1.next():
        retrieved1.append(iterator1.value())

    # Should have all indexes except the removed one
    assert len(retrieved1) == numIndexes - 1, f"State1 should have {numIndexes - 1} indexes, got {len(retrieved1)}"

    # State2 (uncommitted)
    iterator2 = state2.iterateContractDataIndexes(contractAddress)
    retrieved2 = []
    while iterator2.next():
        retrieved2.append(iterator2.value())

    # Should have all indexes
    assert len(retrieved2) == numIndexes, f"State2 should have {numIndexes} indexes, got {len(retrieved2)}"
    for dataKey in dataKeys2:
        assert dataKey in retrieved2, f"Data key {dataKey} should be in state2 iteration results"


def testMultipleStateInstancesIsolation(setupState):
    """
    Test that multiple state instances can modify indexes independently without affecting each other.
    This test focuses specifically on the isolation between state instances, ensuring that changes in one
    state do not affect another state until explicitly committed.
    """
    # Get the initial state and contract address
    state, contractAddress, db = setupState

    # Create test indexes and data
    index1, _ = createIndex(1)
    index2, _ = createIndex(2)

    # Set initial data in the original state
    originalData1 = b"original_data_1"
    originalData2 = b"original_data_2"
    state.setContractDataIndex(contractAddress, index1, originalData1)
    state.setContractDataIndex(contractAddress, index2, originalData2)

    # Create two independent state instances from the same root
    state1 = state.copy()
    state2 = state.copy()

    # Modify state1
    modifiedData1 = b"modified_data_1"
    state1.setContractDataIndex(contractAddress, index1, modifiedData1)

    # Modify state2 differently
    modifiedData2 = b"modified_data_2"
    state2.setContractDataIndex(contractAddress, index2, modifiedData2)

    # Verify each state has its own view of the data
    # State1 should have modified index1 but original index2
    assert state1.getContractDataIndex(contractAddress, index1) == modifiedData1, (
        "State1 should have modified data for index1"
    )
    assert state1.getContractDataIndex(contractAddress, index2) == originalData2, (
        "State1 should have original data for index2"
    )

    # State2 should have original index1 but modified index2
    assert state2.getContractDataIndex(contractAddress, index1) == originalData1, (
        "State2 should have original data for index1"
    )
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, (
        "State2 should have modified data for index2"
    )

    # Original state should be unchanged
    assert state.getContractDataIndex(contractAddress, index1) == originalData1, (
        "Original state should be unchanged for index1"
    )
    assert state.getContractDataIndex(contractAddress, index2) == originalData2, (
        "Original state should be unchanged for index2"
    )

    # Commit state1 and create a new state from its root
    state1Root = state1.commit(100)
    newState1 = statedb.StateDB(state1Root, db)

    # Verify the new state has state1's changes
    assert newState1.getContractDataIndex(contractAddress, index1) == modifiedData1, (
        "New state from state1 root should have modified data for index1"
    )
    assert newState1.getContractDataIndex(contractAddress, index2) == originalData2, (
        "New state from state1 root should have original data for index2"
    )

    # State2 should still be unchanged by state1's commit
    assert state2.getContractDataIndex(contractAddress, index1) == originalData1, (
        "State2 should still have original data for index1 after state1 commit"
    )
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, (
        "State2 should still have modified data for index2 after state1 commit"
    )

    # Test removing an index in one state doesn't affect others
    state1.removeContractDataIndex(contractAddress, index2)
    assert state1.getContractDataIndex(contractAddress, index2) is None, "Index should be removed from state1"
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, "Index should still exist in state2"

    # Test iteration in different states
    # State1 should have only index1 after removing index2
    iterator1 = state1.iterateContractDataIndexes(contractAddress)
    retrieved1 = []
    while iterator1.next():
        retrieved1.append(iterator1.value())

    assert len(retrieved1) == 1, "State1 should have only one index after removal"
    assert modifiedData1 in retrieved1, "Modified data should be in state1 iteration results"

    # State2 should have both indexes with its own modifications
    iterator2 = state2.iterateContractDataIndexes(contractAddress)
    retrieved2 = []
    while iterator2.next():
        retrieved2.append(iterator2.value())

    assert len(retrieved2) == 2, "State2 should have both indexes"
    assert originalData1 in retrieved2, "Original data for index1 should be in state2 iteration results"
    assert modifiedData2 in retrieved2, "Modified data for index2 should be in state2 iteration results"


def testSetGetContractDataIndex(setupState):
    """
    Test setting and getting contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert dataKey == retrievedDataKey, "Index value should match the set value"

    # Verify journal has one ContractDataIndexChange entry
    assert len(state.journal.entries) == 1, "Journal should have one entry"
    assert isinstance(state.journal.entries[0], journal.ContractDataIndexChange), (
        "Journal entry should be of type ContractDataIndexChange"
    )

    # Verify journal entry contents
    entry = state.journal.entries[0]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert entry.prevValue is None, "Previous value should be None"


def testUpdateContractDataIndex(setupState):
    """
    Test updating contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)
    newDataKey = dataKey + b"_updated"

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Update index
    state.setContractDataIndex(contractAddress, index, newDataKey)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert newDataKey == retrievedDataKey, "Index value should be the updated value"

    # Verify journal has two ContractDataIndexChange entries
    assert len(state.journal.entries) == 2, "Journal should have two entries"

    # Verify second journal entry contents
    entry = state.journal.entries[1]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert dataKey == entry.prevValue, "Previous value should be the first set value"


def testRemoveContractDataIndex(setupState):
    """
    Test removing contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Remove index
    state.removeContractDataIndex(contractAddress, index)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert retrievedDataKey is None, "Index value should be None after removal"

    # Verify journal has two ContractDataIndexChange entries
    assert len(state.journal.entries) == 2, "Journal should have two entries"

    # Verify second journal entry contents
    entry = state.journal.entries[1]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert dataKey == entry.prevValue, "Previous value should be the set value"


def testIterateContractDataIndexes(setupState):
    """
    Test iterating contract data indexes.
    """
    state, contractAddress, _ = setupState

    # Create multiple test indexes
    numIndexes = 5
    indexes = []
    dataKeys = []

    for i in range(numIndexes):
        index, dataKey = createIndex(i)
        indexes.append(index)
        dataKeys.append(dataKey)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Forward iterate all indexes
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=True)
    retrievedPairs = []

    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        # The key returned by the iterator includes the contract address and possibly
        # some internal prefix bytes. We only need to verify the dataKey matches.
        retrievedPairs.append(value)

    # Verify iteration results
    assert numIndexes == len(retrievedPairs), "Should iterate through all indexes"

    # Verify each value is in the retrieved values
    for dataKey in dataKeys:
        assert dataKey in retrievedPairs, f"Data key {dataKey} should be in iteration results"

    # Test reverse iteration
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=False)
    reversedPairs = []

    while iterator.next():
        value = iterator.value()
        reversedPairs.append(value)

    # Verify reverse iteration results
    assert numIndexes == len(reversedPairs), "Should iterate through all indexes"

    # Verify all values are present in both forward and reverse iterations
    assert set(retrievedPairs) == set(reversedPairs), "Forward and reverse iterations should contain the same values"

    # Since we can't rely on the exact order due to internal key formatting,
    # we'll just verify that the values are different in the two iterations
    # if there are multiple values
    if numIndexes > 1:
        assert retrievedPairs != reversedPairs, "Forward and reverse iterations should be in different orders"


def testSnapshotAndRevert(setupState):
    """
    Test snapshot and revert functionality.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set first index
    state.setContractDataIndex(contractAddress, index1, dataKey1)

    # Create snapshot
    snapshotId = state.snapshot()

    # Set second index and update first index
    state.setContractDataIndex(contractAddress, index2, dataKey2)
    state.setContractDataIndex(contractAddress, index1, dataKey1 + b"_updated")

    # Revert to snapshot
    state.revertToSnapshot(snapshotId)

    # Verify first index is restored to original value
    retrievedDataKey1 = state.getContractDataIndex(contractAddress, index1)
    assert dataKey1 == retrievedDataKey1, "First index should be restored to original value"

    # Verify second index doesn't exist
    retrievedDataKey2 = state.getContractDataIndex(contractAddress, index2)
    assert retrievedDataKey2 is None, "Second index should not exist"


def testCollectIndexChanges(setupState):
    """
    Test collecting index changes to generate undo log.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Store original values for verification
    originalDataKey1 = dataKey1
    updatedDataKey1 = dataKey1 + b"_updated"

    # Set indexes
    state.setContractDataIndex(contractAddress, index1, dataKey1)  # Create
    state.setContractDataIndex(contractAddress, index2, dataKey2)  # Create
    state.setContractDataIndex(contractAddress, index1, updatedDataKey1)  # Update
    state.setContractDataIndex(contractAddress, index3, dataKey3)  # Create
    state.removeContractDataIndex(contractAddress, index2)  # Delete

    # Collect index changes
    undoLog = state._collectIndexChanges()

    # Verify undo log entry count
    assert 5 == len(undoLog.entries), "Undo log should have 5 entries"

    # Count operations by type
    createCount = 0
    updateCount = 0
    deleteCount = 0

    # Track which operations we've seen
    seenUpdateForIndex1 = False
    seenDeleteForIndex2 = False

    for entry in undoLog.entries:
        # Verify contract address
        assert contractAddress == entry.contractAddress, "Contract address should match"

        # Count by operation type
        if entry.operationType == IndexOperationType.CREATE:
            createCount += 1
        elif entry.operationType == IndexOperationType.UPDATE:
            updateCount += 1
            # Verify the update operation for index1
            if entry.index == index1:
                assert entry.previousValue == originalDataKey1, (
                    "Update operation's previous value should be the original value"
                )
                seenUpdateForIndex1 = True
        elif entry.operationType == IndexOperationType.DELETE:
            deleteCount += 1
            # Verify the delete operation for index2
            if entry.index == index2:
                assert entry.previousValue == dataKey2, "Delete operation's previous value should be the original value"
                seenDeleteForIndex2 = True

    # Verify operation counts
    assert 3 == createCount, "Should have 3 create operations"
    assert 1 == updateCount, "Should have 1 update operation"
    assert 1 == deleteCount, "Should have 1 delete operation"

    # Verify we saw the specific operations we expected
    assert seenUpdateForIndex1, "Should have seen an update operation for index1"
    assert seenDeleteForIndex2, "Should have seen a delete operation for index2"


def testCommitAndUndoLog(setupState):
    """
    Test committing and generating undo log.
    """
    state, contractAddress, db = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set indexes
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Commit state changes
    blockNumber = 123
    state.commit(blockNumber)

    # Read undo log from database
    undoLog = index_undo_log.readBlockUndoLog(db.diskDB(), blockNumber)

    # Verify undo log is not None
    assert undoLog is not None, "Undo log should not be None"

    # Verify undo log entry count
    assert 2 == len(undoLog.entries), "Undo log should have 2 entries"

    # Verify undo log entry contents
    for entry in undoLog.entries:
        assert contractAddress == entry.contractAddress, "Contract address should match"
        # We can't directly compare the index due to potential internal formatting,
        # but we can verify it's one of our indexes by checking the operation type and previous value
        assert IndexOperationType.CREATE == entry.operationType, "Operation type should be CREATE"
        assert entry.previousValue is None, "Previous value should be None"

    # Store the current values for verification
    value1Before = readContractDataIndex(db.diskDB(), contractAddress, index1)
    value2Before = readContractDataIndex(db.diskDB(), contractAddress, index2)

    # Verify values exist before applying undo log
    assert value1Before is not None, "First index should exist before applying undo log"
    assert value2Before is not None, "Second index should exist before applying undo log"

    # Apply undo log to revert changes
    index_undo_log.applyUndoLog(db.diskDB(), undoLog)

    # Verify indexes are deleted
    value1After = readContractDataIndex(db.diskDB(), contractAddress, index1)
    value2After = readContractDataIndex(db.diskDB(), contractAddress, index2)

    assert value1After is None, "First index should be deleted after applying undo log"
    assert value2After is None, "Second index should be deleted after applying undo log"


def testUndologRollbackToBlock(setupState):
    """
    Test rolling back to a specific block using undo logs.
    This test simulates multiple block commits and tests the ability to roll back to a specific block.
    """
    _, contractAddress, db = setupState
    diskDb = db.diskDB()

    # Create test indexes for different blocks
    # Block 100: Create index1
    # Block 101: Create index2
    # Block 102: Update index1, Create index3
    # Block 103: Delete index2, Update index3
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Values for updates
    updatedDataKey1 = dataKey1 + b"_updated"
    updatedDataKey3 = dataKey3 + b"_updated"

    # Block 100: Create index1
    block100 = 100
    state100 = statedb.StateDB(common.EmptyRootHash, db)
    state100.setContractDataIndex(contractAddress, index1, dataKey1)
    stateRoot100 = state100.commit(block100)

    # Verify index1 exists after block 100
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should exist after block 100"

    # Block 101: Create index2
    block101 = 101
    state101 = statedb.StateDB(stateRoot100, db)
    state101.setContractDataIndex(contractAddress, index2, dataKey2)
    stateRoot101 = state101.commit(block101)

    # Verify indexes after block 101
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should still exist after block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should exist after block 101"

    # Block 102: Update index1, Create index3
    block102 = 102
    state102 = statedb.StateDB(stateRoot101, db)
    state102.setContractDataIndex(contractAddress, index1, updatedDataKey1)
    state102.setContractDataIndex(contractAddress, index3, dataKey3)
    stateRoot102 = state102.commit(block102)

    # Verify indexes after block 102
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1, (
        "Index1 should be updated after block 102"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, (
        "Index2 should still exist after block 102"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) == dataKey3, "Index3 should exist after block 102"

    # Block 103: Delete index2, Update index3
    block103 = 103
    state103 = statedb.StateDB(stateRoot102, db)
    state103.removeContractDataIndex(contractAddress, index2)
    state103.setContractDataIndex(contractAddress, index3, updatedDataKey3)
    state103.commit(block103)

    # Verify indexes after block 103
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1, (
        "Index1 should still be updated after block 103"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, "Index2 should be deleted after block 103"
    assert readContractDataIndex(diskDb, contractAddress, index3) == updatedDataKey3, (
        "Index3 should be updated after block 103"
    )

    # Now roll back to block 101
    # 1. Undo block 103 changes: Restore index2, Restore index3 to original value
    # 2. Undo block 102 changes: Restore index1 to original value, Delete index3
    index_undo_log.rollbackToBlock(diskDb, block101)

    # Verify state after rolling back to block 101
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should be restored to original value after rolling back to block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, (
        "Index2 should still exist after rolling back to block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, (
        "Index3 should be deleted after rolling back to block 101"
    )

    # Optionally, we could continue rolling back to block 100 or even earlier
    # For demonstration, let's roll back to block 100
    index_undo_log.rollbackToBlock(diskDb, block100)

    # Verify state after rolling back to block 100
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should still exist after rolling back to block 100"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, (
        "Index2 should be deleted after rolling back to block 100"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, (
        "Index3 should still be deleted after rolling back to block 100"
    )


def testUndologRollbackMultipleContracts(setupState):
    """
    Test rolling back indexes for multiple contracts using undo logs.
    This test simulates changes to indexes across multiple contracts and tests
    the ability to roll back all changes correctly.
    """
    _, _, db = setupState
    diskDb = db.diskDB()

    # Create multiple contract addresses
    contractA = eth_utils.keccak(b"contract_a")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20
    contractB = eth_utils.keccak(b"contract_b")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20
    contractC = eth_utils.keccak(b"contract_c")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Values for updates
    updatedDataKey1 = dataKey1 + b"_updated"
    updatedDataKey2 = dataKey2 + b"_updated"

    # Block 200: Set initial indexes for contracts A and B
    block200 = 200
    state200 = statedb.StateDB(common.EmptyRootHash, db)
    state200.setContractDataIndex(contractA, index1, dataKey1)
    state200.setContractDataIndex(contractB, index2, dataKey2)
    stateRoot200 = state200.commit(block200)

    # Verify initial state
    assert readContractDataIndex(diskDb, contractA, index1) == dataKey1, "Contract A index1 should be set"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, "Contract B index2 should be set"

    # Block 201: Update contract A index, add index to contract C
    block201 = 201
    state201 = statedb.StateDB(stateRoot200, db)
    state201.setContractDataIndex(contractA, index1, updatedDataKey1)
    state201.setContractDataIndex(contractC, index3, dataKey3)
    stateRoot201 = state201.commit(block201)

    # Verify state after block 201
    assert readContractDataIndex(diskDb, contractA, index1) == updatedDataKey1, "Contract A index1 should be updated"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, "Contract B index2 should remain unchanged"
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should be set"

    # Block 202: Update contract B index, remove contract A index
    block202 = 202
    state202 = statedb.StateDB(stateRoot201, db)
    state202.setContractDataIndex(contractB, index2, updatedDataKey2)
    state202.removeContractDataIndex(contractA, index1)
    state202.commit(block202)

    # Verify state after block 202
    assert readContractDataIndex(diskDb, contractA, index1) is None, "Contract A index1 should be removed"
    assert readContractDataIndex(diskDb, contractB, index2) == updatedDataKey2, "Contract B index2 should be updated"
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should remain unchanged"

    # Roll back to block 200
    index_undo_log.rollbackToBlock(diskDb, block200)

    # Verify state after rolling back to block 200
    assert readContractDataIndex(diskDb, contractA, index1) == dataKey1, (
        "Contract A index1 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, (
        "Contract B index2 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractC, index3) is None, "Contract C index3 should be removed"

    # Test partial rollback - only roll back one block
    # First, reapply blocks 201 and 202
    state201New = statedb.StateDB(stateRoot200, db)
    state201New.setContractDataIndex(contractA, index1, updatedDataKey1)
    state201New.setContractDataIndex(contractC, index3, dataKey3)
    stateRoot201New = state201New.commit(block201)

    state202New = statedb.StateDB(stateRoot201New, db)
    state202New.setContractDataIndex(contractB, index2, updatedDataKey2)
    state202New.removeContractDataIndex(contractA, index1)
    state202New.commit(block202)

    # Now roll back just block 202
    index_undo_log.rollbackToBlock(diskDb, block201)

    # Verify state after partial rollback
    assert readContractDataIndex(diskDb, contractA, index1) == updatedDataKey1, "Contract A index1 should be restored"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, (
        "Contract B index2 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should remain unchanged"


def testBatchRollback(setupState):
    """
    Test batch rollback of multiple blocks in a single operation.
    This test verifies that rollbackToBlock correctly reverts all changes after the target block,
    including changes to multiple indexes across multiple blocks.
    """
    _, contractAddress, db = setupState
    diskDb = db.diskDB()

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)
    index4, dataKey4 = createIndex(4)

    # Create a sequence of blocks with different changes:
    # Block 300: Create index1, index2
    # Block 301: Update index1, Create index3
    # Block 302: Update index2, Create index4
    # Block 303: Delete index3, Update index4
    # Block 304: Update index1, Delete index2

    # Block 300: Initial state - Create index1, index2
    block300 = 300
    state300 = statedb.StateDB(common.EmptyRootHash, db)
    state300.setContractDataIndex(contractAddress, index1, dataKey1)
    state300.setContractDataIndex(contractAddress, index2, dataKey2)
    stateRoot300 = state300.commit(block300)

    # Verify state after block 300
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should be created"
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should be created"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should not exist"
    assert readContractDataIndex(diskDb, contractAddress, index4) is None, "Index4 should not exist"

    # Block 301: Update index1, Create index3
    block301 = 301
    updatedDataKey1V1 = dataKey1 + b"_v1"
    state301 = statedb.StateDB(stateRoot300, db)
    state301.setContractDataIndex(contractAddress, index1, updatedDataKey1V1)
    state301.setContractDataIndex(contractAddress, index3, dataKey3)
    stateRoot301 = state301.commit(block301)

    # Block 302: Update index2, Create index4
    block302 = 302
    updatedDataKey2V1 = dataKey2 + b"_v1"
    state302 = statedb.StateDB(stateRoot301, db)
    state302.setContractDataIndex(contractAddress, index2, updatedDataKey2V1)
    state302.setContractDataIndex(contractAddress, index4, dataKey4)
    stateRoot302 = state302.commit(block302)

    # Block 303: Delete index3, Update index4
    block303 = 303
    updatedDataKey4V1 = dataKey4 + b"_v1"
    state303 = statedb.StateDB(stateRoot302, db)
    state303.removeContractDataIndex(contractAddress, index3)
    state303.setContractDataIndex(contractAddress, index4, updatedDataKey4V1)
    stateRoot303 = state303.commit(block303)

    # Block 304: Update index1, Delete index2
    block304 = 304
    updatedDataKey1V2 = updatedDataKey1V1 + b"_v2"
    state304 = statedb.StateDB(stateRoot303, db)
    state304.setContractDataIndex(contractAddress, index1, updatedDataKey1V2)
    state304.removeContractDataIndex(contractAddress, index2)
    state304.commit(block304)

    # Verify final state before rollback
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1V2, "Index1 should be at v2"
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, "Index2 should be deleted"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should be deleted"
    assert readContractDataIndex(diskDb, contractAddress, index4) == updatedDataKey4V1, "Index4 should be at v1"

    # Test case 1: Roll back to block 302
    # This should revert:
    # - Block 304 changes (index1 update to v2, index2 deletion)
    # - Block 303 changes (index3 deletion, index4 update to v1)
    index_undo_log.rollbackToBlock(diskDb, block302)

    # Verify state after rolling back to block 302
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1V1, "Index1 should be at v1"
    assert readContractDataIndex(diskDb, contractAddress, index2) == updatedDataKey2V1, "Index2 should be at v1"
    assert readContractDataIndex(diskDb, contractAddress, index3) == dataKey3, "Index3 should be restored"
    assert readContractDataIndex(diskDb, contractAddress, index4) == dataKey4, "Index4 should be at original value"

    # Test case 2: Roll back to block 300
    # This should revert all changes after block 300
    index_undo_log.rollbackToBlock(diskDb, block300)

    # Verify state after rolling back to block 300
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should be at original value"
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should be at original value"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should not exist"
    assert readContractDataIndex(diskDb, contractAddress, index4) is None, "Index4 should not exist"


def testEdgeCases(setupState):
    """
    Test edge cases for index operations including:
    - Empty values
    - Maximum size values
    - Special characters
    - Error conditions
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, _ = createIndex(1)

    # Test empty value
    emptyValue = b""
    state.setContractDataIndex(contractAddress, index, emptyValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == emptyValue, "Empty value should be stored and retrieved correctly"

    # Test maximum size value (example: 1MB)
    maxValue = b"x" * (1024 * 1024)  # 1MB
    state.setContractDataIndex(contractAddress, index, maxValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == maxValue, "Large value should be stored and retrieved correctly"

    # Test special characters
    specialValue = bytes([0x00, 0xFF, 0x7F, 0x80])  # Include edge case bytes
    state.setContractDataIndex(contractAddress, index, specialValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == specialValue, "Special characters should be handled correctly"


def testConcurrentOperations(setupState):
    """
    Test handling of concurrent operations on the same contract.
    Simulates parallel operations by interleaving them.
    """
    state, contractAddress, _ = setupState

    # Create multiple indexes
    numOperations = 5
    indexes = []
    values = []

    # Prepare test data
    for i in range(numOperations):
        index, value = createIndex(i)
        indexes.append(index)
        values.append(value)

    # Simulate concurrent operations
    for i in range(numOperations):
        # Set index i
        state.setContractDataIndex(contractAddress, indexes[i], values[i])

        # Update previous index if it exists
        if i > 0:
            state.setContractDataIndex(contractAddress, indexes[i - 1], values[i] + b"_updated")

    # Verify final state
    for i in range(numOperations):
        value = state.getContractDataIndex(contractAddress, indexes[i])
        if i < numOperations - 1:
            expected = values[i + 1] + b"_updated"
        else:
            expected = values[i]
        assert value == expected, f"Index {i} should have correct final value"


def testErrorRecovery(setupState):
    """
    Test system recovery after errors during index operations.
    """
    state, contractAddress, db = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set initial state
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Take snapshot
    snapshotId = state.snapshot()

    try:
        # Simulate an error during operation
        state.setContractDataIndex(contractAddress, index1, dataKey1 + b"_updated")
        raise Exception("Simulated error")
    except Exception:
        # Revert to snapshot
        state.revertToSnapshot(snapshotId)

    # Verify state was properly restored
    value1 = state.getContractDataIndex(contractAddress, index1)
    assert value1 == dataKey1, "Index1 should be restored to original value after error"

    value2 = state.getContractDataIndex(contractAddress, index2)
    assert value2 == dataKey2, "Index2 should be unaffected by error recovery"


def testDataValidation(setupState):
    """
    Test validation of index data and handling of invalid inputs.
    """
    state, contractAddress, _ = setupState

    # Calculate the maximum allowed index length based on the contract address
    from rawdb.accessors_data_indexes import INDEX_KEY_LENGTH_LIMIT
    from rawdb.scheme import ContractDataIndexPrefix

    prefix_len = len(ContractDataIndexPrefix)
    contract_address_len = len(contractAddress)
    max_index_len = INDEX_KEY_LENGTH_LIMIT - prefix_len - contract_address_len

    # Test index must not exceed the maximum allowed length
    with pytest.raises(ValueError):
        # Create an index that exceeds the limit
        index = b"x" * (max_index_len + 1)
        state.setContractDataIndex(contractAddress, index, b"value")

    # Test index creation with various sizes
    test_sizes = list(range(1, max_index_len + 50, 50))  # Test a range of sizes
    for i in test_sizes:
        if i <= max_index_len:
            # This should succeed
            index = b"x" * i
            state.setContractDataIndex(contractAddress, index, b"value")
            value = state.getContractDataIndex(contractAddress, index)
            assert value == b"value", f"Index of size {i} should work"
        else:
            # This should fail
            with pytest.raises(ValueError):
                index = b"x" * i
                state.setContractDataIndex(contractAddress, index, b"value")


def testLargeScaleOperations(setupState):
    """
    Test system performance and correctness with large numbers of operations.
    """
    state, contractAddress, _ = setupState

    # Create large number of indexes
    numIndexes = 1000
    testData = {}

    # Set many indexes
    for i in range(numIndexes):
        index, value = createIndex(i)
        testData[index] = value
        state.setContractDataIndex(contractAddress, index, value)

    # Verify all indexes
    for index, expectedValue in testData.items():
        value = state.getContractDataIndex(contractAddress, index)
        assert value == expectedValue, f"Index {index} should have correct value"

    # Update many indexes
    for index in list(testData.keys())[: numIndexes // 2]:
        updatedValue = testData[index] + b"_updated"
        testData[index] = updatedValue
        state.setContractDataIndex(contractAddress, index, updatedValue)

    # Delete many indexes
    for index in list(testData.keys())[numIndexes // 2 :]:
        state.removeContractDataIndex(contractAddress, index)
        testData.pop(index)

    # Verify final state
    for index, expectedValue in testData.items():
        value = state.getContractDataIndex(contractAddress, index)
        assert value == expectedValue, f"Index {index} should have correct final value"

    # Verify deleted indexes
    for i in range(numIndexes // 2, numIndexes):
        index, _ = createIndex(i)
        value = state.getContractDataIndex(contractAddress, index)
        assert value is None, f"Index {i} should be deleted"


@pytest.mark.parametrize(
    "indexSizeRatio,shouldSucceed", [(0.1, True), (0.5, True), (0.9, True), (1.0, True), (1.1, False)]
)
def testIndexSizeValidationParametrized(setupState, indexSizeRatio, shouldSucceed):
    """
    Test index size validation with parameterized inputs.
    Tests various index sizes to ensure only sizes up to the maximum allowed length are accepted.

    Args:
        indexSizeRatio: Ratio of the maximum allowed index length to use
        shouldSucceed: Whether the operation should succeed or fail
    """
    state, contractAddress, _ = setupState

    # Calculate the maximum allowed index length based on the contract address
    from rawdb.accessors_data_indexes import INDEX_KEY_LENGTH_LIMIT
    from rawdb.scheme import ContractDataIndexPrefix

    prefix_len = len(ContractDataIndexPrefix)
    contract_address_len = len(contractAddress)
    max_index_len = INDEX_KEY_LENGTH_LIMIT - prefix_len - contract_address_len

    # Calculate the index size based on the ratio
    index_size = int(max_index_len * indexSizeRatio)

    # Create an index of the specified size
    index = b"x" * index_size

    if shouldSucceed:
        # This should succeed
        state.setContractDataIndex(contractAddress, index, b"value")
        value = state.getContractDataIndex(contractAddress, index)
        assert value == b"value", f"Index of size {index_size} should be accepted"
    else:
        # This should fail
        with pytest.raises(ValueError):
            state.setContractDataIndex(contractAddress, index, b"value")
            raise AttributeError(f"Index of size {index_size} should be rejected")


def testPerformanceScaling(setupState):
    """
    Test system performance scaling with different data volumes.
    Measures time taken to perform operations with increasing numbers of indexes.
    """
    import time

    state, contractAddress, _ = setupState

    # Test different scales of data
    scales = [10, 100, 1000]
    times = []

    for scale in scales:
        # Record start time
        start = time.time()

        # Create specified number of indexes
        for i in range(scale):
            index, value = createIndex(i)
            state.setContractDataIndex(contractAddress, index, value)

        # Record end time
        end = time.time()
        times.append(end - start)

        # Clean up state for next test
        state.revertToSnapshot(state.snapshot())

    # Log performance results
    for i, scale in enumerate(scales):
        print(f"Time for {scale} operations: {times[i]:.4f} seconds")

    # Optional: Verify performance scales reasonably
    # This is a simple check - in practice you might want more sophisticated analysis
    for i in range(1, len(scales)):
        ratio = times[i] / times[i - 1]
        scaleRatio = scales[i] / scales[i - 1]
        # Performance tests can be affected by various factors, especially with small data volumes, so use a more relaxed limit
        assert ratio < scaleRatio * 100, f"Performance scaling issue: {ratio:.2f}x time for {scaleRatio:.2f}x data"


def testTransactionConsistency(setupState):
    """
    Test transaction-like consistency with snapshots.
    Ensures that a series of operations can be committed or rolled back atomically.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Start "transaction"
    snapshotId = state.snapshot()

    # Perform multiple operations
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Verify intermediate state
    assert state.getContractDataIndex(contractAddress, index1) == dataKey1, "First index should be set"
    assert state.getContractDataIndex(contractAddress, index2) == dataKey2, "Second index should be set"

    # Roll back "transaction"
    state.revertToSnapshot(snapshotId)

    # Verify all changes were undone
    assert state.getContractDataIndex(contractAddress, index1) is None, "First index should be None after rollback"
    assert state.getContractDataIndex(contractAddress, index2) is None, "Second index should be None after rollback"

    # Start new "transaction" and commit
    _snapshotId = state.snapshot()
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    # Simulate commit by not rolling back
    blockNumber = 999
    state.commit(blockNumber)

    # Verify changes persisted
    assert state.getContractDataIndex(contractAddress, index1) == dataKey1, "Changes should persist after commit"


def testCachedIndexIteratorMerge(setupState):
    """
    Test that CachedIndexIterator correctly merges cache-only entries with database entries.
    This test specifically verifies the fix for the issue where the iterator would not
    transition to database entries after exhausting cache entries.
    """
    state, contractAddress, _ = setupState

    # Create some database entries (these will be committed to the database)
    dbEntries = 3
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create some cache-only entries (these will not be committed)
    cacheEntries = 2
    for i in range(dbEntries, dbEntries + cacheEntries):
        index, dataKey = createIndex(i)
        newState.setContractDataIndex(contractAddress, index, dataKey)

    # Get an iterator over all entries
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all entries (both from cache and database)
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results
    for i in range(dbEntries + cacheEntries):
        _, dataKey = createIndex(i)
        assert dataKey in entries, f"Data key {dataKey} should be in iteration results"


def testCachedIndexIteratorWithModifications(setupState):
    """
    Test that CachedIndexIterator correctly handles modifications to database entries.
    """
    state, contractAddress, _ = setupState

    # Create some database entries
    dbEntries = 3
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Modify some database entries in the cache
    modifiedEntries = 2
    for i in range(modifiedEntries):
        index, _ = createIndex(i)
        modifiedDataKey = f"modified_data_key_{i}".encode()
        newState.setContractDataIndex(contractAddress, index, modifiedDataKey)

    # Delete one database entry
    deleteIndex = 2
    index, _ = createIndex(deleteIndex)
    newState.removeContractDataIndex(contractAddress, index)

    # Add some new cache-only entries
    newEntries = 2
    for i in range(dbEntries, dbEntries + newEntries):
        index, dataKey = createIndex(i)
        newState.setContractDataIndex(contractAddress, index, dataKey)

    # Get an iterator over all entries
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # We should have: (dbEntries - deleted_entries) + newEntries = 4 unique entries
    # - 2 modified entries (modified_data_key_0, modified_data_key_1)
    # - 2 new entries (data_key_3, data_key_4)
    expectedCount = dbEntries - 1 + newEntries
    assert len(entries) == expectedCount, f"Expected {expectedCount} entries, got {len(entries)}"

    # Verify modified entries
    for i in range(modifiedEntries):
        modifiedDataKey = f"modified_data_key_{i}".encode()
        assert modifiedDataKey in entries, f"Modified data key {modifiedDataKey} should be in iteration results"

    # Verify deleted entry is not present
    _, deletedDataKey = createIndex(deleteIndex)
    assert deletedDataKey not in entries, f"Deleted data key {deletedDataKey} should not be in iteration results"

    # Verify new entries
    for i in range(dbEntries, dbEntries + newEntries):
        _, dataKey = createIndex(i)
        assert dataKey in entries, f"New data key {dataKey} should be in iteration results"


def testCachedIndexIteratorEmptyCache(setupState):
    """
    Test that CachedIndexIterator works correctly when there are no cache entries.
    Tests both forward and reverse iteration.
    """
    state, contractAddress, _ = setupState

    # Create some database entries
    dbEntries = 5
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root (no cache entries)
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test forward iteration (reverse=False)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all database entries
    assert len(entries) == dbEntries, f"Expected {dbEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in the correct order
    for i in range(dbEntries):
        _, dataKey = createIndex(i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"

    # Test reverse iteration (reverse=True)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all database entries
    assert len(entries) == dbEntries, f"Expected {dbEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in reverse order
    for i in range(dbEntries):
        _, dataKey = createIndex(dbEntries - 1 - i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"


def testCachedIndexIteratorEmptyDb(setupState):
    """
    Test that CachedIndexIterator works correctly when there are no database entries.
    Tests both forward and reverse iteration.
    """
    state, contractAddress, _ = setupState

    # Create some cache-only entries (no database entries)
    cacheEntries = 5
    for i in range(cacheEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Test forward iteration (reverse=False)
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all cache entries
    assert len(entries) == cacheEntries, f"Expected {cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in the correct order
    for i in range(cacheEntries):
        _, dataKey = createIndex(i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"

    # Test reverse iteration (reverse=True)
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all cache entries
    assert len(entries) == cacheEntries, f"Expected {cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in reverse order
    for i in range(cacheEntries):
        _, dataKey = createIndex(cacheEntries - 1 - i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"


def testCachedIndexIteratorOrder(setupState):
    """
    Test that CachedIndexIterator correctly orders entries from both cache and database.
    Specifically tests the scenario where:
    - DB has entries with keys 10 and 30
    - Cache has an entry with key 20
    - The iterator should return them in order: 10, 20, 30 (for forward iteration)
    - The iterator should return them in order: 30, 20, 10 (for reverse iteration)
    """
    state, contractAddress, _ = setupState

    # Create database entries for keys 10 and 30
    index10, dataKey10 = createIndex(10)
    index30, dataKey30 = createIndex(30)

    # Set the database entries
    state.setContractDataIndex(contractAddress, index10, dataKey10)
    state.setContractDataIndex(contractAddress, index30, dataKey30)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create a cache-only entry with key 20
    index20, dataKey20 = createIndex(20)
    newState.setContractDataIndex(contractAddress, index20, dataKey20)

    # Test forward iteration (should be 10, 20, 30)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        entries.append(value)

    # Verify the order is correct for forward iteration
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0] == dataKey10, f"First entry should be dataKey10, got {entries[0]}"
    assert entries[1] == dataKey20, f"Second entry should be dataKey20, got {entries[1]}"
    assert entries[2] == dataKey30, f"Third entry should be dataKey30, got {entries[2]}"

    # Test reverse iteration (should be 30, 20, 10)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        entries.append(value)

    # Verify the order is correct for reverse iteration
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0] == dataKey30, f"First entry should be dataKey30, got {entries[0]}"
    assert entries[1] == dataKey20, f"Second entry should be dataKey20, got {entries[1]}"
    assert entries[2] == dataKey10, f"Third entry should be dataKey10, got {entries[2]}"


def testCachedIndexIteratorWithModifiedDbEntries(setupState):
    """
    Test that CachedIndexIterator correctly handles modified DB entries.
    """
    state, contractAddress, _ = setupState

    # Create database entries
    indices = []
    dataKeys = []
    for i in range(10, 60, 10):  # 10, 20, 30, 40, 50
        index, dataKey = createIndex(i)
        indices.append(index)
        dataKeys.append(f"original_value_{i}".encode())
        state.setContractDataIndex(contractAddress, index, dataKeys[-1])

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Modify some DB entries in the cache
    newState.setContractDataIndex(contractAddress, indices[1], b"modified_value_20")  # Modify 20
    newState.setContractDataIndex(contractAddress, indices[3], b"modified_value_40")  # Modify 40

    # Delete one DB entry
    newState.removeContractDataIndex(contractAddress, indices[2])  # Delete 30

    # Add a new cache-only entry
    index25, _ = createIndex(25)
    newState.setContractDataIndex(contractAddress, index25, b"new_value_25")

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        value = iterator.value()
        entries.append(value)

    # Verify we have the correct number of entries (5 original - 1 deleted + 1 new = 5)
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the values in the correct order (10, 20, 25, 40, 50)
    expectedValues = [
        b"original_value_10",
        b"modified_value_20",
        b"new_value_25",
        b"modified_value_40",
        b"original_value_50",
    ]

    for i, val in enumerate(entries):
        assert val == expectedValues[i], f"Entry {i} should have value {expectedValues[i]}, got {val}"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        value = iterator.value()
        entries.append(value)

    # Verify we have the correct number of entries (5 original - 1 deleted + 1 new = 5)
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the values in the correct order (50, 40, 25, 20, 10) - reverse of the forward order
    expectedValuesReverse = [
        b"original_value_50",
        b"modified_value_40",
        b"new_value_25",
        b"modified_value_20",
        b"original_value_10",
    ]

    for i, val in enumerate(entries):
        assert val == expectedValuesReverse[i], f"Entry {i} should have value {expectedValuesReverse[i]}, got {val}"


def testCachedIndexIteratorInterleavedEntries(setupState):
    """
    Test CachedIndexIterator with interleaved entries from both cache and database.
    This tests a more realistic scenario where keys from both sources are mixed together.
    """
    state, contractAddress, _ = setupState

    # Helper function to create padded index to ensure numeric sorting
    def createPaddedIndex(num: int) -> bytes:
        # Create a padded index with fixed-width numeric part to ensure numeric sorting
        indexPrefix = b"test_index_"
        # Pad the number to 2 digits to ensure proper sorting (01, 02, ..., 10, 11, ...)
        paddedNum = str(num).zfill(2).encode()
        return indexPrefix + paddedNum

    # Create database entries with even numbers
    dbEntries = 10
    for i in range(0, dbEntries * 2, 2):  # 0, 2, 4, 6, 8, 10, 12, 14, 16, 18
        # Use our custom padded index instead of createIndex
        index = createPaddedIndex(i)
        state.setContractDataIndex(contractAddress, index, f"db_value_{i}".encode())

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create cache entries with odd numbers
    cacheEntries = 10
    for i in range(1, cacheEntries * 2, 2):  # 1, 3, 5, 7, 9, 11, 13, 15, 17, 19
        # Use our custom padded index instead of createIndex
        index = createPaddedIndex(i)
        newState.setContractDataIndex(contractAddress, index, f"cache_value_{i}".encode())

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify we have the correct number of entries
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify the values are in the correct order (0, 1, 2, 3, ..., 19)
    for i in range(dbEntries + cacheEntries):
        if i % 2 == 0:
            expectedValue = f"db_value_{i}".encode()
        else:
            expectedValue = f"cache_value_{i}".encode()
        assert entries[i] == expectedValue, f"Entry {i} should be {expectedValue}, got {entries[i]}"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify we have the correct number of entries
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify the values are in the correct order (19, 18, 17, ..., 0)
    for i in range(dbEntries + cacheEntries):
        j = (dbEntries + cacheEntries - 1) - i
        if j % 2 == 0:
            expectedValue = f"db_value_{j}".encode()
        else:
            expectedValue = f"cache_value_{j}".encode()
        assert entries[i] == expectedValue, f"Entry {i} should be {expectedValue}, got {entries[i]}"


def testCachedIndexIteratorPrefixMatching(setupState):
    """
    Test that CachedIndexIterator correctly handles prefix matching with both short and full keys.
    """
    state, contractAddress, _ = setupState

    # Create test indexes with different prefixes
    prefixes = [b"a", b"b", b"c", b"d", b"e"]
    for _i, prefix in enumerate(prefixes):
        # Create both short and full keys with the same prefix
        shortKey = prefix
        fullKey = b"test_index_" + prefix

        # Set values for both short and full keys
        state.setContractDataIndex(contractAddress, shortKey, b"short_" + prefix)
        state.setContractDataIndex(contractAddress, fullKey, b"full_" + prefix)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test iteration with short prefix
    shortPrefix = b"a"
    iterator = newState.iterateContractDataIndexes(contractAddress, start=shortPrefix)
    entries = []
    while iterator.next():
        key = iterator.key()
        value = iterator.value()
        entries.append((key, value))

    # Verify that both short and full keys with the prefix are returned
    assert len(entries) >= 2, f"Expected at least 2 entries, got {len(entries)}"
    shortKeyFound = False
    fullKeyFound = False
    for key, value in entries:
        if key.endswith(b"a") and value == b"short_a":
            shortKeyFound = True
        elif key.endswith(b"test_index_a") and value == b"full_a":
            fullKeyFound = True

    assert shortKeyFound, "Short key with prefix 'a' not found"
    assert fullKeyFound, "Full key with prefix 'a' not found"


def testCachedIndexIteratorKeyConflicts(setupState):
    """
    Test CachedIndexIterator with key conflicts between cache and database.
    """
    state, contractAddress, _ = setupState

    # Create database entries
    keys = [b"a", b"b", b"c", b"d", b"e"]
    for key in keys:
        state.setContractDataIndex(contractAddress, key, b"db_value_" + key)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Modify some keys in cache
    newState.setContractDataIndex(contractAddress, b"b", b"cache_value_b")
    newState.setContractDataIndex(contractAddress, b"d", b"cache_value_d")

    # Delete a key in cache
    newState.removeContractDataIndex(contractAddress, b"c")

    # Add a new key in cache
    newState.setContractDataIndex(contractAddress, b"f", b"cache_value_f")

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify results
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"
    assert b"db_value_a" in entries, "Expected 'db_value_a' in results"
    assert b"cache_value_b" in entries, "Expected 'cache_value_b' in results"
    assert b"db_value_c" not in entries, "Unexpected 'db_value_c' in results"
    assert b"cache_value_d" in entries, "Expected 'cache_value_d' in results"
    assert b"db_value_e" in entries, "Expected 'db_value_e' in results"
    assert b"cache_value_f" in entries, "Expected 'cache_value_f' in results"


def testCachedIndexIteratorReuse(setupState):
    """
    Test reusing CachedIndexIterator after it has been exhausted.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    keys = [b"a", b"b", b"c", b"d", b"e"]
    for key in keys:
        state.setContractDataIndex(contractAddress, key, b"value_" + key)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Get an iterator
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # First iteration
    entries1 = []
    while iterator.next():
        entries1.append(iterator.value())

    # Verify first iteration
    assert len(entries1) == 5, f"Expected 5 entries, got {len(entries1)}"

    # Try to reuse the iterator (should not work)
    entries2 = []
    while iterator.next():
        entries2.append(iterator.value())

    # Verify second iteration (should be empty)
    assert len(entries2) == 0, f"Expected 0 entries, got {len(entries2)}"

    # Get a new iterator
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # Third iteration with new iterator
    entries3 = []
    while iterator.next():
        entries3.append(iterator.value())

    # Verify third iteration
    assert len(entries3) == 5, f"Expected 5 entries, got {len(entries3)}"
    assert entries1 == entries3, "First and third iterations should have the same results"


def testCachedIndexIteratorErrorHandling(setupState):
    """
    Test CachedIndexIterator error handling with invalid parameters.
    """
    state, contractAddress, _ = setupState

    # Test with invalid index length (exceeding 255 bytes)
    longIndex = b"x" * 256
    with pytest.raises(ValueError):
        state.setContractDataIndex(contractAddress, longIndex, b"value")

    # Test with empty values (should work)
    state.setContractDataIndex(contractAddress, b"empty_key", b"")
    value = state.getContractDataIndex(contractAddress, b"empty_key")
    assert value == b"", "Empty value should be stored and retrieved correctly"


def testCachedIndexIteratorResourceManagement(setupState):
    """
    Test CachedIndexIterator resource management.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    keys = [b"a", b"b", b"c", b"d", b"e"]
    for key in keys:
        state.setContractDataIndex(contractAddress, key, b"value_" + key)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create multiple iterators
    iterators = []
    for _i in range(10):
        iterator = newState.iterateContractDataIndexes(contractAddress)
        iterators.append(iterator)

    # Use each iterator
    for i, iterator in enumerate(iterators):
        entries = []
        while iterator.next():
            entries.append(iterator.value())
        assert len(entries) == 5, f"Iterator {i} should have 5 entries, got {len(entries)}"

    # Explicitly release resources
    for iterator in iterators:
        iterator.release()

    # Create a new iterator after releasing resources
    iterator = newState.iterateContractDataIndexes(contractAddress)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    assert len(entries) == 5, f"Expected 5 entries after releasing resources, got {len(entries)}"


def testCachedIndexIteratorSortOrder(setupState):
    """
    Test that CachedIndexIterator correctly sorts entries in both forward and reverse iteration.
    This test specifically verifies that:
    1. Forward iteration returns entries in ascending order of keys
    2. Reverse iteration returns entries in descending order of keys
    3. The order is consistent regardless of whether entries come from cache or database
    """
    state, contractAddress, _ = setupState

    # Create a mix of database and cache entries with keys that will test sorting
    # We'll use a mix of numeric and alphabetic keys to ensure proper lexicographic sorting
    dbKeys = [b"a", b"c", b"e", b"g", b"i"]
    cacheKeys = [b"b", b"d", b"f", b"h", b"j"]

    # Add database entries
    for _i, key in enumerate(dbKeys):
        index = b"test_index_" + key
        value = b"db_value_" + key
        state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Add cache entries
    for _i, key in enumerate(cacheKeys):
        index = b"test_index_" + key
        value = b"cache_value_" + key
        newState.setContractDataIndex(contractAddress, index, value)

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect entries
    forwardEntries = []
    while iterator.next():
        key = iterator.key()
        value = iterator.value()
        # Extract the key suffix for easier verification
        keySuffix = key.split(b"_")[-1]
        forwardEntries.append((keySuffix, value))

    # Verify we have all entries
    assert len(forwardEntries) == len(dbKeys) + len(cacheKeys), (
        f"Expected {len(dbKeys) + len(cacheKeys)} entries, got {len(forwardEntries)}"
    )

    # Verify forward order (a through j)
    expectedOrder = sorted(dbKeys + cacheKeys)
    for i, (keySuffix, _) in enumerate(forwardEntries):
        assert keySuffix == expectedOrder[i], f"Entry {i} should have key suffix {expectedOrder[i]}, got {keySuffix}"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect entries
    reverseEntries = []
    while iterator.next():
        key = iterator.key()
        value = iterator.value()
        # Extract the key suffix for easier verification
        keySuffix = key.split(b"_")[-1]
        reverseEntries.append((keySuffix, value))

    # Verify we have all entries
    assert len(reverseEntries) == len(dbKeys) + len(cacheKeys), (
        f"Expected {len(dbKeys) + len(cacheKeys)} entries, got {len(reverseEntries)}"
    )

    # Verify reverse order (j through a)
    expectedReverseOrder = sorted(dbKeys + cacheKeys, reverse=True)
    for i, (keySuffix, _) in enumerate(reverseEntries):
        assert keySuffix == expectedReverseOrder[i], (
            f"Entry {i} should have key suffix {expectedReverseOrder[i]}, got {keySuffix}"
        )

    # Verify that forward and reverse orders are exact opposites
    forwardKeySuffixes = [k for k, _ in forwardEntries]
    reverseKeySuffixes = [k for k, _ in reverseEntries]
    assert forwardKeySuffixes == list(reversed(reverseKeySuffixes)), (
        "Forward and reverse iterations should be exact opposites"
    )


def testCachedIndexIteratorBoundaryConditions(setupState):
    """
    Test CachedIndexIterator with various boundary conditions for start and end parameters.
    This test verifies correct behavior with:
    1. start=None, end=None (all entries)
    2. start=value, end=None (entries >= start)
    3. start=None, end=value (entries < end)
    4. start=value1, end=value2 (entries between start and end)
    5. start=end (empty range)
    6. In reverse iteration, start > end
    7. In forward iteration, start < end
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with keys 'a' through 'j'
    keys = [chr(ord("a") + i).encode() for i in range(10)]  # a, b, c, ..., j

    # Add entries to database
    for key in keys:
        index = b"test_index_" + key
        value = b"value_" + key
        state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test case 1: start=None, end=None (all entries)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    assert len(entries) == len(keys), f"Expected {len(keys)} entries, got {len(entries)}"

    # Test case 2: start=value, end=None (entries >= start)
    # Use 'test_index_e' as start (the full key)
    startKey = b"test_index_e"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include e, f, g, h, i, j (6 entries)
    assert len(entries) == 6, f"Expected 6 entries, got {len(entries)}"
    for _i, key in enumerate(keys[4:]):
        expectedValue = b"value_" + key
        assert expectedValue in entries, f"Expected {expectedValue} in results"

    # Test case 3: start=None, end=value (entries < end)
    # Use 'test_index_f' as end (the full key)
    endKey = b"test_index_f"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include a, b, c, d, e, f (6 entries)
    # 'f' is included because end is inclusive
    assert len(entries) == 6, f"Expected 6 entries, got {len(entries)}"
    for _i, key in enumerate(keys[:6]):
        expectedValue = b"value_" + key
        assert expectedValue in entries, f"Expected {expectedValue} in results"

    # Test case 4: start=value1, end=value2 (entries between start and end)
    # Use 'test_index_c' as start and 'test_index_g' as end (the full keys)
    startKey = b"test_index_c"
    endKey = b"test_index_g"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include c, d, e, f, g (5 entries)
    # 'g' is included because end is inclusive
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"
    for _i, key in enumerate(keys[2:7]):
        expectedValue = b"value_" + key
        assert expectedValue in entries, f"Expected {expectedValue} in results"

    # Test case 5: start=end (should include the entry at start/end)
    startKey = endKey = b"test_index_e"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include 'e' because both start and end are inclusive
    assert len(entries) == 1, f"Expected 1 entry, got {len(entries)}"
    assert b"value_e" in entries, "Expected 'value_e' in results"

    # Test case 6: In reverse iteration, start > end
    # Use 'test_index_h' as start and 'test_index_c' as end (the full keys)
    startKey = b"test_index_h"
    endKey = b"test_index_c"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include h, g, f, e, d, c (6 entries)
    # 'c' is included because end is inclusive
    assert len(entries) == 6, f"Expected 6 entries, got {len(entries)}"
    # In reverse order: h, g, f, e, d, c
    expectedKeys = keys[7:1:-1]
    for _i, key in enumerate(expectedKeys):
        expectedValue = b"value_" + key
        assert expectedValue in entries, f"Expected {expectedValue} in results"

    # Test case 7: In forward iteration, start < end
    # Use 'test_index_c' as start and 'test_index_h' as end (the full keys)
    startKey = b"test_index_c"
    endKey = b"test_index_h"
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())
    # Should include c, d, e, f, g, h (6 entries)
    # 'h' is included because end is inclusive
    assert len(entries) == 6, f"Expected 6 entries, got {len(entries)}"
    # In forward order: c, d, e, f, g, h
    expectedKeys = keys[2:8]
    for _i, key in enumerate(expectedKeys):
        expectedValue = b"value_" + key
        assert expectedValue in entries, f"Expected {expectedValue} in results"


def testCachedIndexIteratorExactBoundaries(setupState):
    """
    Test CachedIndexIterator behavior when keys exactly match start or end boundaries.
    This test verifies:
    1. When a key exactly equals the start value, it is included in the results
    2. When a key exactly equals the end value, it is excluded from the results
    3. This behavior is consistent in both forward and reverse iteration
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with keys 'a' through 'j'
    keys = [chr(ord("a") + i).encode() for i in range(10)]  # a, b, c, ..., j

    # Add entries to database
    for key in keys:
        index = b"test_index_" + key
        value = b"value_" + key
        state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test forward iteration with exact boundary matches
    startKey = b"test_index_c"  # Exactly matches an existing key
    endKey = b"test_index_g"  # Exactly matches an existing key

    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Should include c, d, e, f, g (5 entries)
    # 'c' is included because it equals start
    # 'g' is included because end is inclusive
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the exact entries
    expectedValues = [b"value_c", b"value_d", b"value_e", b"value_f", b"value_g"]
    for value in expectedValues:
        assert value in entries, f"Expected {value} in results"

    # Test reverse iteration with exact boundary matches
    startKey = b"test_index_g"  # Exactly matches an existing key
    endKey = b"test_index_c"  # Exactly matches an existing key

    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Should include g, f, e, d, c (5 entries)
    # 'g' is included because it equals start
    # 'c' is included because end is inclusive
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the exact entries
    expectedValues = [b"value_g", b"value_f", b"value_e", b"value_d", b"value_c"]
    for value in expectedValues:
        assert value in entries, f"Expected {value} in results"


def testCachedIndexIteratorMixedData(setupState):
    """
    Test CachedIndexIterator with a mix of cache and database data, focusing on boundary conditions.
    This test verifies correct behavior when:
    1. Some entries are in the database and some are in the cache
    2. Cache entries override database entries
    3. Some entries are deleted in the cache
    4. Range boundaries fall on both cache and database entries
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with keys 'a' through 'j'
    keys = [chr(ord("a") + i).encode() for i in range(10)]  # a, b, c, ..., j

    # Add odd entries to database (a, c, e, g, i)
    for i, key in enumerate(keys):
        if i % 2 == 0:  # 0, 2, 4, 6, 8
            index = b"test_index_" + key
            value = b"db_value_" + key
            state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Add even entries to cache (b, d, f, h, j)
    # Modify one database entry (e)
    # Delete one database entry (g)
    for i, key in enumerate(keys):
        index = b"test_index_" + key
        if i % 2 == 1:  # 1, 3, 5, 7, 9 (b, d, f, h, j)
            value = b"cache_value_" + key
            newState.setContractDataIndex(contractAddress, index, value)
        elif key == b"e":  # Modify 'e'
            value = b"modified_value_" + key
            newState.setContractDataIndex(contractAddress, index, value)
        elif key == b"g":  # Delete 'g'
            newState.removeContractDataIndex(contractAddress, index)

    # Test forward iteration with mixed data and range boundaries
    # Use 'test_index_c' (in DB) as start and 'test_index_h' (in cache) as end
    startKey = b"test_index_c"
    endKey = b"test_index_h"

    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Should include c, d, e, f, h (5 entries)
    # 'g' was deleted, 'h' is included because end is inclusive
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the exact entries and their sources
    expectedEntries = [
        b"db_value_c",  # From database
        b"cache_value_d",  # From cache
        b"modified_value_e",  # Modified in cache
        b"cache_value_f",  # From cache
        b"cache_value_h",  # From cache
    ]

    for i, expected in enumerate(expectedEntries):
        assert entries[i] == expected, f"Entry {i} should be {expected}, got {entries[i]}"

    # Test reverse iteration with mixed data and range boundaries
    # Use 'test_index_h' (in cache) as start and 'test_index_c' (in DB) as end
    startKey = b"test_index_h"
    endKey = b"test_index_c"

    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Should include h, f, e, d, c (5 entries)
    # 'g' was deleted, 'c' is included because end is inclusive
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the exact entries and their sources in reverse order
    expectedEntries = [
        b"cache_value_h",  # From cache
        b"cache_value_f",  # From cache
        b"modified_value_e",  # Modified in cache
        b"cache_value_d",  # From cache
        b"db_value_c",  # From database
    ]

    for i, expected in enumerate(expectedEntries):
        assert entries[i] == expected, f"Entry {i} should be {expected}, got {entries[i]}"

    # Test with a boundary that falls on a deleted entry
    # Use 'test_index_e' as start and 'test_index_i' as end
    startKey = b"test_index_e"
    endKey = b"test_index_i"

    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Should include e, f, h, i (4 entries)
    # 'g' was deleted, 'i' is included because end is inclusive
    assert len(entries) == 4, f"Expected 4 entries, got {len(entries)}"

    # Verify the exact entries
    expectedEntries = [
        b"modified_value_e",  # Modified in cache
        b"cache_value_f",  # From cache
        b"cache_value_h",  # From cache
        b"db_value_i",  # From database
    ]

    for i, expected in enumerate(expectedEntries):
        assert entries[i] == expected, f"Entry {i} should be {expected}, got {entries[i]}"


def testCachedIndexIteratorComplexRanges(setupState):
    """
    Test CachedIndexIterator with complex range scenarios where start and end parameters
    point to indices in the middle of the dataset.

    This test verifies:
    1. Behavior when start and end point to middle indices
    2. Boundary conditions when start and end exactly match existing indices
    3. Consistent behavior in both forward and reverse iteration modes
    4. Consistent behavior with both short keys and full keys
    5. Correct handling of mixed cache and database data
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with keys 'a' through 'z'
    keys = [chr(ord("a") + i).encode() for i in range(26)]  # a, b, c, ..., z

    # Add odd-indexed entries to database (a, c, e, ...)
    for i, key in enumerate(keys):
        if i % 2 == 0:  # 0, 2, 4, ...
            index = b"test_index_" + key
            value = b"db_value_" + key
            state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Add even-indexed entries to cache (b, d, f, ...)
    # Modify some database entries (e, m)
    # Delete some database entries (i, q)
    for i, key in enumerate(keys):
        index = b"test_index_" + key
        if i % 2 == 1:  # 1, 3, 5, ... (b, d, f, ...)
            value = b"cache_value_" + key
            newState.setContractDataIndex(contractAddress, index, value)
        elif key == b"e" or key == b"m":  # Modify 'e' and 'm'
            value = b"modified_value_" + key
            newState.setContractDataIndex(contractAddress, index, value)
        elif key == b"i" or key == b"q":  # Delete 'i' and 'q'
            newState.removeContractDataIndex(contractAddress, index)

    # Test Case 1: Start and end in the middle of the dataset (full keys)
    # Use 'test_index_d' as start and 'test_index_n' as end
    print("\nTest Case 1: Start and end in the middle (full keys)")
    startKey = b"test_index_d"
    endKey = b"test_index_n"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include d, e, f, g, h, j, k, l, m, n (10 entries)
    # 'i' and 'q' were deleted
    assert len(entries) == 10, f"Expected 10 entries, got {len(entries)}"

    # Verify the exact entries and their sources
    expectedEntries = [
        (b"d", b"cache_value_d"),  # From cache
        (b"e", b"modified_value_e"),  # Modified in cache
        (b"f", b"cache_value_f"),  # From cache
        (b"g", b"db_value_g"),  # From database
        (b"h", b"cache_value_h"),  # From cache
        (b"j", b"cache_value_j"),  # From cache
        (b"k", b"db_value_k"),  # From database
        (b"l", b"cache_value_l"),  # From cache
        (b"m", b"modified_value_m"),  # Modified in cache
        (b"n", b"cache_value_n"),  # From cache
    ]

    for i, (key, value) in enumerate(expectedEntries):
        assert entries[i][0] == key, f"Entry {i} should have key {key}, got {entries[i][0]}"
        assert entries[i][1] == value, f"Entry {i} should have value {value}, got {entries[i][1]}"

    # Reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=endKey, end=startKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include n, m, l, k, j, h, g, f, e, d (10 entries in reverse order)
    assert len(entries) == 10, f"Expected 10 entries, got {len(entries)}"

    # Verify the exact entries in reverse order
    expectedEntries.reverse()
    for i, (key, value) in enumerate(expectedEntries):
        assert entries[i][0] == key, f"Entry {i} should have key {key}, got {entries[i][0]}"
        assert entries[i][1] == value, f"Entry {i} should have value {value}, got {entries[i][1]}"

    # Test Case 2: Start and end in the middle of the dataset (full keys again)
    # Use 'test_index_d' as start and 'test_index_n' as end
    print("\nTest Case 2: Start and end in the middle (full keys again)")
    startKey = b"test_index_d"
    endKey = b"test_index_n"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include d, e, f, g, h, j, k, l, m, n (10 entries)
    assert len(entries) == 10, f"Expected 10 entries, got {len(entries)}"

    # Verify the exact entries and their sources
    expectedEntries = [
        (b"d", b"cache_value_d"),  # From cache
        (b"e", b"modified_value_e"),  # Modified in cache
        (b"f", b"cache_value_f"),  # From cache
        (b"g", b"db_value_g"),  # From database
        (b"h", b"cache_value_h"),  # From cache
        (b"j", b"cache_value_j"),  # From cache
        (b"k", b"db_value_k"),  # From database
        (b"l", b"cache_value_l"),  # From cache
        (b"m", b"modified_value_m"),  # Modified in cache
        (b"n", b"cache_value_n"),  # From cache
    ]

    for i, (key, value) in enumerate(expectedEntries):
        assert entries[i][0] == key, f"Entry {i} should have key {key}, got {entries[i][0]}"
        assert entries[i][1] == value, f"Entry {i} should have value {value}, got {entries[i][1]}"

    # Test Case 3: Exact boundary matches
    # Use 'test_index_e' as start and 'test_index_e' as end (same value)
    print("\nTest Case 3: Exact boundary matches (same value)")
    startKey = b"test_index_e"
    endKey = b"test_index_e"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include only 'e'
    assert len(entries) == 1, f"Expected 1 entry, got {len(entries)}"
    assert entries[0][0] == b"e", f"Entry should have key 'e', got {entries[0][0]}"
    assert entries[0][1] == b"modified_value_e", f"Entry should have value 'modified_value_e', got {entries[0][1]}"

    # Test Case 4: Boundary conditions with deleted entries
    # Use 'test_index_h' as start and 'test_index_j' as end (skipping deleted 'i')
    print("\nTest Case 4: Boundary conditions with deleted entries")
    startKey = b"test_index_h"
    endKey = b"test_index_j"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include h, j (2 entries, 'i' was deleted)
    assert len(entries) == 2, f"Expected 2 entries, got {len(entries)}"
    assert entries[0][0] == b"h", f"First entry should have key 'h', got {entries[0][0]}"
    assert entries[1][0] == b"j", f"Second entry should have key 'j', got {entries[1][0]}"

    # Test Case 5: Mixed cache and database with complex range
    # Use 'test_index_c' as start and 'test_index_r' as end (spanning multiple modified/deleted entries)
    print("\nTest Case 5: Mixed cache and database with complex range")
    startKey = b"test_index_c"
    endKey = b"test_index_r"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include c, d, e, f, g, h, j, k, l, m, n, o, p, r (14 entries)
    # 'i' and 'q' were deleted
    assert len(entries) == 14, f"Expected 14 entries, got {len(entries)}"

    # Verify the first and last entries
    assert entries[0][0] == b"c", f"First entry should have key 'c', got {entries[0][0]}"
    assert entries[-1][0] == b"r", f"Last entry should have key 'r', got {entries[-1][0]}"

    # Verify that deleted entries are not included
    for entry in entries:
        assert entry[0] != b"i" and entry[0] != b"q", f"Deleted entry found: {entry[0]}"

    # Test Case 6: Reverse iteration with complex range
    # Use 'test_index_r' as start and 'test_index_c' as end
    print("\nTest Case 6: Reverse iteration with complex range")
    startKey = b"test_index_r"
    endKey = b"test_index_c"

    # Reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include r, p, o, n, m, l, k, j, h, g, f, e, d, c (14 entries in reverse order)
    assert len(entries) == 14, f"Expected 14 entries, got {len(entries)}"

    # Verify the first and last entries
    assert entries[0][0] == b"r", f"First entry should have key 'r', got {entries[0][0]}"
    assert entries[-1][0] == b"c", f"Last entry should have key 'c', got {entries[-1][0]}"

    # Verify that deleted entries are not included
    for entry in entries:
        assert entry[0] != b"i" and entry[0] != b"q", f"Deleted entry found: {entry[0]}"


def testCachedIndexIteratorEdgeCases(setupState):
    """
    Test CachedIndexIterator with edge cases and corner conditions.

    This test verifies:
    1. Behavior when start is greater than end in forward iteration
    2. Behavior when end is greater than start in reverse iteration
    3. Behavior with empty ranges and non-existent keys
    4. Behavior with keys that are lexicographically close to each other
    5. Behavior with very large ranges that include all data
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with specific keys to test edge cases
    keys = [b"aaa", b"aab", b"aac", b"aba", b"abb", b"abc", b"baa", b"bab", b"bac"]

    # Add all entries to database
    for _i, key in enumerate(keys):
        index = b"test_index_" + key
        value = b"value_" + key
        state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test Case 1: Start greater than end in forward iteration
    print("\nTest Case 1: Start greater than end in forward iteration")
    startKey = b"test_index_baa"
    endKey = b"test_index_aac"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include no entries since start > end in forward iteration
    assert len(entries) == 0, f"Expected 0 entries, got {len(entries)}"

    # Test Case 2: End greater than start in reverse iteration
    print("\nTest Case 2: End greater than start in reverse iteration")
    startKey = b"test_index_aac"
    endKey = b"test_index_baa"

    # Reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include no entries since end > start in reverse iteration
    assert len(entries) == 0, f"Expected 0 entries, got {len(entries)}"

    # Test Case 3: Non-existent keys
    print("\nTest Case 3: Non-existent keys")
    startKey = b"test_index_aad"  # Does not exist
    endKey = b"test_index_azy"  # Does not exist

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include aba, abb, abc (3 entries)
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0][0] == b"aba", f"First entry should have key 'aba', got {entries[0][0]}"
    assert entries[-1][0] == b"abc", f"Last entry should have key 'abc', got {entries[-1][0]}"

    # Test Case 4: Lexicographically close keys
    print("\nTest Case 4: Lexicographically close keys")
    startKey = b"test_index_aaa"
    endKey = b"test_index_aac"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include aaa, aab, aac (3 entries)
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0][0] == b"aaa", f"First entry should have key 'aaa', got {entries[0][0]}"
    assert entries[1][0] == b"aab", f"Second entry should have key 'aab', got {entries[1][0]}"
    assert entries[2][0] == b"aac", f"Third entry should have key 'aac', got {entries[2][0]}"

    # Test Case 5: Very large range (include all data)
    print("\nTest Case 5: Very large range (include all data)")
    startKey = b"test_index_"
    endKey = b"test_index_z"

    # Forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=startKey, end=endKey)
    entries = []
    while iterator.next():
        entries.append((iterator.key().split(b"_")[-1], iterator.value()))

    # Should include all 9 entries
    assert len(entries) == 9, f"Expected 9 entries, got {len(entries)}"

    # Verify the entries are in correct order
    expectedKeys = sorted(keys)
    for i, key in enumerate(expectedKeys):
        assert entries[i][0] == key, f"Entry {i} should have key {key}, got {entries[i][0]}"


def testCachedIndexIteratorWithDelimiters(setupState):
    """
    Test CachedIndexIterator with keys containing various delimiters (-, _, ., etc.)
    to ensure correct sorting behavior.

    This test verifies that:
    1. Keys with different delimiters are sorted correctly in lexicographical order
    2. Sorting works correctly in both forward and reverse iteration
    3. Range queries with delimiter-containing keys work correctly
    """
    state, contractAddress, _ = setupState

    # Create test indexes with various delimiters
    keys = [
        b"a-1",  # Hyphen
        b"a.2",  # Dot
        b"a_3",  # Underscore
        b"a/4",  # Slash
        b"a:5",  # Colon
        b"a+6",  # Plus
        b"a#7",  # Hash
        b"a@8",  # At sign
        b"a&9",  # Ampersand
        b"a=10",  # Equals
    ]

    # Expected order in lexicographical sorting
    # Note: This is based on ASCII/UTF-8 ordering of the delimiter characters
    expected_order = [
        b"a#7",  # # (ASCII 35)
        b"a&9",  # & (ASCII 38)
        b"a+6",  # + (ASCII 43)
        b"a-1",  # - (ASCII 45)
        b"a.2",  # . (ASCII 46)
        b"a/4",  # / (ASCII 47)
        b"a:5",  # : (ASCII 58)
        b"a=10",  # = (ASCII 61)
        b"a@8",  # @ (ASCII 64)
        b"a_3",  # _ (ASCII 95)
    ]

    # Set values for all keys
    for key in keys:
        state.setContractDataIndex(contractAddress, key, b"value_for_" + key)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test forward iteration (should be in lexicographical order)
    iterator = newState.iterateContractDataIndexes(contractAddress)
    entries = []
    while iterator.next():
        key = iterator.key()
        entries.append(key)

    # Extract just the key part (without any prefixes)
    extracted_keys = []
    for entry in entries:
        for key in keys:
            if entry.endswith(key):
                extracted_keys.append(key)
                break

    # Verify the order matches expected lexicographical order
    assert len(extracted_keys) == len(expected_order), (
        f"Expected {len(expected_order)} entries, got {len(extracted_keys)}"
    )

    # Check if the extracted keys are in the expected order
    for _i, key in enumerate(expected_order):
        assert key in extracted_keys, f"Key {key} not found in iteration results"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)
    reverse_entries = []
    while iterator.next():
        key = iterator.key()
        reverse_entries.append(key)

    # Extract just the key part (without any prefixes)
    reverse_extracted_keys = []
    for entry in reverse_entries:
        for key in keys:
            if entry.endswith(key):
                reverse_extracted_keys.append(key)
                break

    # Verify reverse order is opposite of forward order
    assert len(reverse_extracted_keys) == len(extracted_keys), "Reverse iteration should return same number of entries"

    # Test range query with delimiter-containing keys
    # Query from a-1 to a:5 (should include a-1, a.2, a/4, a:5)
    start_key = b"a-1"
    end_key = b"a:5"

    iterator = newState.iterateContractDataIndexes(contractAddress, start=start_key, end=end_key)
    range_entries = []
    while iterator.next():
        key = iterator.key()
        # value = iterator.value()  # Unused variable
        for test_key in keys:
            if key.endswith(test_key):
                range_entries.append(test_key)
                break

    # Verify range query results
    expected_range = [b"a-1", b"a.2", b"a/4", b"a:5"]
    assert len(range_entries) == len(expected_range), (
        f"Expected {len(expected_range)} entries in range, got {len(range_entries)}"
    )
    for key in expected_range:
        assert key in range_entries, f"Key {key} should be in range query results"


def testCachedIndexIteratorWithComplexDelimiters(setupState):
    """
    Test CachedIndexIterator with keys containing multiple different delimiters
    to ensure correct sorting behavior in complex scenarios.

    This test verifies that:
    1. Keys with multiple different delimiters are sorted correctly in lexicographical order
    2. Sorting works correctly in both forward and reverse iteration
    3. Range queries with complex delimiter-containing keys work correctly
    4. Edge cases like delimiters at different positions are handled correctly
    """
    state, contractAddress, _ = setupState

    # Create test indexes with complex delimiter combinations
    keys = [
        b"a-1_2.3",  # Hyphen, underscore, dot
        b"a-1.2_3",  # Hyphen, dot, underscore
        b"a_1-2.3",  # Underscore, hyphen, dot
        b"a_1.2-3",  # Underscore, dot, hyphen
        b"a.1-2_3",  # Dot, hyphen, underscore
        b"a.1_2-3",  # Dot, underscore, hyphen
        b"b-1_2.3",  # Different first letter
        b"1-a_b.c",  # Number first, then delimiters and letters
        b"a-b_c.1",  # Letters with delimiters, then number
        b"a--b__c..1",  # Double delimiters
        b"a-_b.c",  # Adjacent different delimiters
        b"-_.",  # Only delimiters
        b"abc-",  # Delimiter at the end
        b"-abc",  # Delimiter at the beginning
        b"a-1_2.3/4:5",  # More complex with additional delimiters
    ]

    # Expected order in lexicographical sorting
    # Note: This is based on ASCII/UTF-8 ordering
    # expected_order is not used in this test, but kept as documentation
    _ = [
        b"-_.",  # Starts with - (ASCII 45)
        b"-abc",  # Starts with - (ASCII 45)
        b"1-a_b.c",  # Starts with 1 (ASCII 49)
        b"a--b__c..1",  # a followed by - (ASCII 45)
        b"a-1.2_3",  # a followed by - (ASCII 45)
        b"a-1_2.3",  # a followed by - (ASCII 45)
        b"a-1_2.3/4:5",  # a followed by - (ASCII 45)
        b"a-_b.c",  # a followed by - (ASCII 45)
        b"a-b_c.1",  # a followed by - (ASCII 45)
        b"a.1-2_3",  # a followed by . (ASCII 46)
        b"a.1_2-3",  # a followed by . (ASCII 46)
        b"a_1-2.3",  # a followed by _ (ASCII 95)
        b"a_1.2-3",  # a followed by _ (ASCII 95)
        b"abc-",  # abc followed by - (ASCII 45)
        b"b-1_2.3",  # b followed by - (ASCII 45)
    ]

    # Set values for all keys
    for key in keys:
        state.setContractDataIndex(contractAddress, key, b"value_for_" + key)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test forward iteration (should be in lexicographical order)
    iterator = newState.iterateContractDataIndexes(contractAddress)
    entries = []
    while iterator.next():
        key = iterator.key()
        entries.append(key)

    # Extract just the key part (without any prefixes)
    extracted_keys = []
    for entry in entries:
        for key in keys:
            if entry.endswith(key):
                extracted_keys.append(key)
                break

    # Verify all keys are returned
    assert len(extracted_keys) == len(keys), f"Expected {len(keys)} entries, got {len(extracted_keys)}"

    # Verify each key is in the results
    for key in keys:
        assert key in extracted_keys, f"Key {key} not found in iteration results"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)
    reverse_entries = []
    while iterator.next():
        key = iterator.key()
        reverse_entries.append(key)

    # Extract just the key part (without any prefixes)
    reverse_extracted_keys = []
    for entry in reverse_entries:
        for key in keys:
            if entry.endswith(key):
                reverse_extracted_keys.append(key)
                break

    # Verify reverse order has the same keys as forward order
    assert len(reverse_extracted_keys) == len(extracted_keys), "Reverse iteration should return same number of entries"
    assert set(reverse_extracted_keys) == set(extracted_keys), "Reverse iteration should return the same set of keys"

    # Test range query with complex delimiter-containing keys
    # Query from a-1_2.3 to a_1.2-3 (should include keys in between in lexicographical order)
    start_key = b"a-1_2.3"
    end_key = b"a_1.2-3"

    iterator = newState.iterateContractDataIndexes(contractAddress, start=start_key, end=end_key)
    range_entries = []
    while iterator.next():
        key = iterator.key()
        for test_key in keys:
            if key.endswith(test_key):
                range_entries.append(test_key)
                break

    # Expected keys in the range (based on lexicographical ordering)
    expected_range = [
        b"a-1_2.3",  # Start key
        b"a-1_2.3/4:5",  # Comes after start key
        b"a-_b.c",  # Comes after start key
        b"a-b_c.1",  # Comes after start key
        b"a.1-2_3",  # Comes after a-
        b"a.1_2-3",  # Comes after a-
        b"a_1-2.3",  # Comes after a.
        b"a_1.2-3",  # End key
    ]

    # Verify range query results
    assert len(range_entries) == len(expected_range), (
        f"Expected {len(expected_range)} entries in range, got {len(range_entries)}"
    )
    for key in expected_range:
        assert key in range_entries, f"Key {key} should be in range query results"

    # Test edge case: range query with delimiter at the beginning
    start_key = b"-abc"
    end_key = b"1-a_b.c"

    iterator = newState.iterateContractDataIndexes(contractAddress, start=start_key, end=end_key)
    range_entries = []
    while iterator.next():
        key = iterator.key()
        for test_key in keys:
            if key.endswith(test_key):
                range_entries.append(test_key)
                break

    # Expected keys in the range
    expected_range = [b"-abc", b"1-a_b.c"]
    assert len(range_entries) == len(expected_range), (
        f"Expected {len(expected_range)} entries in range, got {len(range_entries)}"
    )
    for key in expected_range:
        assert key in range_entries, f"Key {key} should be in range query results"

    # Test edge case: range query with delimiter at the end
    start_key = b"abc-"
    end_key = b"b-1_2.3"

    iterator = newState.iterateContractDataIndexes(contractAddress, start=start_key, end=end_key)
    range_entries = []
    while iterator.next():
        key = iterator.key()
        for test_key in keys:
            if key.endswith(test_key):
                range_entries.append(test_key)
                break

    # Expected keys in the range
    expected_range = [b"abc-", b"b-1_2.3"]
    assert len(range_entries) == len(expected_range), (
        f"Expected {len(expected_range)} entries in range, got {len(range_entries)}"
    )
    for key in expected_range:
        assert key in range_entries, f"Key {key} should be in range query results"

    # Test edge case: range query with adjacent different delimiters
    start_key = b"a-_b.c"
    end_key = b"a.1-2_3"

    iterator = newState.iterateContractDataIndexes(contractAddress, start=start_key, end=end_key)
    range_entries = []
    while iterator.next():
        key = iterator.key()
        for test_key in keys:
            if key.endswith(test_key):
                range_entries.append(test_key)
                break

    # Expected keys in the range
    expected_range = [b"a-_b.c", b"a-b_c.1", b"a.1-2_3"]
    assert len(range_entries) == len(expected_range), (
        f"Expected {len(expected_range)} entries in range, got {len(range_entries)}"
    )
    for key in expected_range:
        assert key in range_entries, f"Key {key} should be in range query results"


def testCachedIndexIteratorMixedKeyFormats(setupState):
    """
    Test CachedIndexIterator with mixed key formats (short keys and full keys).

    This test verifies consistent behavior when using both short keys and full keys
    in the same iteration, ensuring that the iterator correctly handles different
    key formats and produces consistent results.
    """
    state, contractAddress, _ = setupState

    # Create a sequence of entries with keys 'a' through 'z'
    keys = [chr(ord("a") + i).encode() for i in range(26)]  # a, b, c, ..., z

    # Add all entries to database
    for key in keys:
        index = b"test_index_" + key
        value = b"value_" + key
        state.setContractDataIndex(contractAddress, index, value)

    # Commit to database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test Case 1: Mixed key formats in forward iteration
    print("\nTest Case 1: Mixed key formats in forward iteration")

    # Test with full key as start and full key as end
    fullStart = b"test_index_d"
    shortEnd = b"test_index_n"  # Use full key for end too

    iterator1 = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=fullStart, end=shortEnd)
    entries1 = []
    while iterator1.next():
        entries1.append(iterator1.key().split(b"_")[-1])

    # Test with short key as start and full key as end
    shortStart = b"test_index_d"  # Use full key for start too
    fullEnd = b"test_index_n"

    iterator2 = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=shortStart, end=fullEnd)
    entries2 = []
    while iterator2.next():
        entries2.append(iterator2.key().split(b"_")[-1])

    # Both iterations should produce the same results
    assert len(entries1) == len(entries2), f"Expected same number of entries, got {len(entries1)} and {len(entries2)}"
    for i in range(len(entries1)):
        assert entries1[i] == entries2[i], f"Entry {i} mismatch: {entries1[i]} vs {entries2[i]}"

    # Test Case 2: Mixed key formats in reverse iteration
    print("\nTest Case 2: Mixed key formats in reverse iteration")

    # Test with full key as start and full key as end
    fullStart = b"test_index_n"
    shortEnd = b"test_index_d"  # Use full key for end too

    iterator1 = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=fullStart, end=shortEnd)
    entries1 = []
    while iterator1.next():
        entries1.append(iterator1.key().split(b"_")[-1])

    # Test with short key as start and full key as end
    shortStart = b"test_index_n"  # Use full key for start too
    fullEnd = b"test_index_d"

    iterator2 = newState.iterateContractDataIndexes(contractAddress, reverse=True, start=shortStart, end=fullEnd)
    entries2 = []
    while iterator2.next():
        entries2.append(iterator2.key().split(b"_")[-1])

    # Both iterations should produce the same results
    assert len(entries1) == len(entries2), f"Expected same number of entries, got {len(entries1)} and {len(entries2)}"
    for i in range(len(entries1)):
        assert entries1[i] == entries2[i], f"Entry {i} mismatch: {entries1[i]} vs {entries2[i]}"

    # Test Case 3: Consistency with different key formats
    print("\nTest Case 3: Consistency with different key formats")

    # Define test ranges
    ranges = [
        (b"test_index_a", b"test_index_z"),  # Full keys
        (b"test_index_a", b"test_index_z"),  # Full keys again
        (b"test_index_a", b"test_index_z"),  # Full keys again
        (b"test_index_a", b"test_index_z"),  # Full keys again
    ]

    results = []

    for start, end in ranges:
        iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False, start=start, end=end)
        entries = []
        while iterator.next():
            entries.append(iterator.key().split(b"_")[-1])
        results.append(entries)

    # All iterations should produce the same results
    for i in range(1, len(results)):
        assert len(results[0]) == len(results[i]), (
            f"Range {i} produced different number of entries: {len(results[0])} vs {len(results[i])}"
        )
        for j in range(len(results[0])):
            assert results[0][j] == results[i][j], (
                f"Entry {j} mismatch in range {i}: {results[0][j]} vs {results[i][j]}"
            )
