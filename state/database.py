from __future__ import annotations

import sys
from abc import ABC, abstractmethod

import eth_utils

import rawdb
import tree
import vgraphdb

# Import our cache management system
from cache_manager import Cache<PERSON>anager

# Number of code_hash->size associations to keep.
codeSizeCacheSize = 100000

# Cache size granted for caching clean code.
codeCacheSize = 64 * 1024 * 1024


class DatabaseABC(ABC):
    """
    Database wraps access to tries and contract code.
    """

    @abstractmethod
    def openTrie(self, root: bytes) -> tree.TrieABC:
        """
        OpenTrie opens the main account trie.
        """
        raise NotImplementedError

    @abstractmethod
    def openStorageTrie(self, stateRoot: bytes, address: bytes, root: bytes) -> tree.TrieABC:
        """
        OpenStorageTrie opens the storage trie of an account.
        """
        raise NotImplementedError

    @abstractmethod
    def copyTrie(self, t: tree.TrieABC) -> tree.TrieABC:
        """
        Copy<PERSON><PERSON> creates a copy of an existing trie.
        """
        raise NotImplementedError

    @abstractmethod
    def contractCode(self, address: bytes, codeHash: bytes) -> bytes:
        """
        ContractCode retrieves a particular contract's code.
        """
        raise NotImplementedError

    @abstractmethod
    def contractCodeSize(self, address: bytes, codeHash: bytes) -> int:
        """
        ContractCodeSize retrieves a particular contracts code's size.
        """
        raise NotImplementedError

    @abstractmethod
    def diskDB(self) -> vgraphdb.KeyValueStore:
        """
        DiskDB returns the underlying disk database.
        """
        raise NotImplementedError

    @abstractmethod
    def trieDB(self) -> tree.TrieDB:
        """
        TrieDB returns the underlying trie database for managing trie nodes.
        """
        raise NotImplementedError


class CachingDB(DatabaseABC):
    def __init__(self, disk: vgraphdb.KeyValueStore, triedb: tree.TrieDB):
        self.disk: vgraphdb.KeyValueStore = disk
        self.triedb: tree.TrieDB = triedb
        self.codeCache = CacheManager.createCache(
            name="code_cache", cacheType="sizeConstrained", maxSize=codeCacheSize, getSizeOf=lambda x: sys.getsizeof(x)
        )
        self.codeSizeCache = CacheManager.createCache(
            name="code_size_cache", cacheType="lru", maxSize=codeSizeCacheSize
        )

    def openTrie(self, root: bytes) -> tree.TrieABC:
        return tree.StateTrie(tree.stateTrieId(root), self.triedb)

    def openStorageTrie(self, stateRoot: bytes, address: bytes, root: bytes) -> tree.TrieABC:
        """
        OpenStorageTrie opens the storage trie of an account.
        """
        return tree.StateTrie(tree.storageTrieId(stateRoot, eth_utils.keccak(address), root), self.triedb)

    def copyTrie(self, t: tree.TrieABC) -> tree.TrieABC:
        """
        CopyTrie returns an independent copy of the given trie.
        """
        if not isinstance(t, tree.StateTrie):
            raise ValueError("unknown trie type %T", t)
        return t.copy()

    def contractCode(self, address: bytes, codeHash: bytes) -> bytes:
        """
        ContractCode retrieves a particular contract's code.
        """
        if self.codeCache.contains(codeHash):
            return self.codeCache.get(codeHash)
        code = rawdb.readCode(self.disk, codeHash)
        if code is not None:
            self.codeCache.put(codeHash, code)
            self.codeSizeCache.put(codeHash, len(code))
            return code
        raise ValueError("contract code not found")

    def contractCodeSize(self, address: bytes, codeHash: bytes) -> int:
        """
        ContractCodeSize retrieves a particular contracts code's size.
        """
        if self.codeSizeCache.contains(codeHash):
            return self.codeSizeCache.get(codeHash)
        return len(self.contractCode(address, codeHash))

    def diskDB(self) -> vgraphdb.KeyValueStore:
        """
        DiskDB returns the underlying key-value disk database.
        """
        return self.disk

    def trieDB(self) -> tree.TrieDB:
        """
        TrieDB retrieves any intermediate trie-node caching layer.
        """
        return self.triedb


def newDatabase(disk: vgraphdb.KeyValueStore) -> DatabaseABC:
    # TODO: with config
    return CachingDB(disk, tree.TrieDB(disk))


def newDatabaseWithTriedb(disk: vgraphdb.KeyValueStore, triedb: tree.TrieDB) -> DatabaseABC:
    return CachingDB(disk, triedb)
