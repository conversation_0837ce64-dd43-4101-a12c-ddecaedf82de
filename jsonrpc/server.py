import asyncio
import json
import os
import re
import time
from collections import defaultdict
from functools import partial
from ipaddress import IPv4Address, IPv6Address
from typing import Any, Callable, Dict, List, Optional

import attr
from aiorpcx import Service, ServicePart, TaskGroup, serve_rs, serve_ws
from kivy.logger import Logger

from config.config import CONFIG
from jsonrpc.peer import Peer
from jsonrpc.peers import PeerManager
from jsonrpc.utils import formatTime, getPublicIp, sessionsLines

SERVICES = [service for service in CONFIG.localAddress if service]
REPORT_SERVICES = [service for service in CONFIG.internetAddress if service]
MAX_SESSIONS = CONFIG.maxSessions

SESSION_TIMEOUT = CONFIG.sessionTimeout
LOG_SESSION_INTERVAL = CONFIG.logSessionInterval


class ServiceError(Exception):
    """
    raise when service error
    """

    pass


@attr.s(slots=True)
class SessionGroup:
    """
    A group of sessions
    """

    name: str = attr.ib()
    weight: float = attr.ib()
    sessions: set = attr.ib()
    retained_cost: float = attr.ib()

    def session_cost(self):
        return sum(session.cost for session in self.sessions)

    def cost(self):
        return self.retained_cost + self.session_cost()


class SessionManager:
    """
    SessionManager manages the global session state for the JSON-RPC server.

    It's responsible for:
        - managing local servers (start & stop)
        - managing session state
        - logging session state
    """

    PROTOCOL_VERSION = "0.0.1"
    SERVER_VERSION = "0.0.1"

    _LOGGER_TITLE = "SessionManager:"
    KNOWN_PROTOCOLS = ("tcp", "ws")

    def __init__(self, chain):
        self.chain = chain
        self.sessionClass = self.chain.SESSION_CLASS

        # set up peer manager
        self.reportServices: List[Service] = self._servicesToReport()
        self.peerManager = PeerManager()
        self.peerManager.myselves = [
            Peer(str(service.host), self._serverMetadata(), source="self") for service in self.reportServices
        ]

        # set up local servers
        self.localServices: List[Service] = self._servicesToRun()
        self.peerManager.services = self.localServices
        self.servers: Dict[Service, Any] = dict()
        # Event triggered when vgraph client is listening for incoming requests.
        self.serverListening = asyncio.Event()

        asyncio.create_task(self.serve())
        self.startTime = time.time()  # start time of the server

        # set up session state
        # session -> iterable of its SessionGroups
        self.sessions: Dict[self.sessionClass, tuple[SessionGroup]] = dict()
        # group name -> SessionGroup instance
        self.sessionGroups: Dict[str, SessionGroup] = dict()
        # event triggered when a new session is added
        self.sessionEvent: asyncio.Event = asyncio.Event()
        self.taskGroup: TaskGroup = TaskGroup()
        self.methodCounts: defaultdict[str, int] = defaultdict(int)  # record method counts

    # ----- server management -----

    async def serve(self) -> None:
        """
        this is an entrypoint where we start the whole service process
        """
        try:
            # start local servers
            await self._startServers(self.localServices)
            # set listening event
            self.serverListening.set()

            # set max sessions
            self.maxSessions = self.calculateMaxSessions()

            # group tasks
            #   1. discover peers
            #   2. manage servers
            #   3. fill peers if peers are not enough (less than 8)
            #   4. clear stale sessions
            #   5. log session management state
            #   6. manage server status
            async with self.taskGroup as group:
                await group.spawn(self.peerManager.discoverPeers())
                await group.spawn(self.peerManager.managePeerStatuses())
                await group.spawn(self.peerManager.fillPeers())
                await group.spawn(self.peerManager.logPeersInfo(interval=LOG_SESSION_INTERVAL))
                await group.spawn(self._clearStaleSessions())
                await group.spawn(self._logState())
                await group.spawn(self._manageServers())

                async for task in group:
                    if not task.cancelled():
                        task.result()
        finally:
            # stop servers
            Logger.info(f"{self._LOGGER_TITLE} Shutting down servers")
            await self._stopServers(self.servers.keys())
            Logger.info(f"{self._LOGGER_TITLE} All servers stopped")

    async def _startServers(self, services: List[Service]) -> None:
        """
        start the servers in the given service list
        """
        for service in services:
            try:
                Logger.info(f"{self._LOGGER_TITLE} Starting server on: {service}")
                sessionFactory = partial(self.sessionClass, self)
                host = None if service.host == "all_interfaces" else str(service.host)
                if service.protocol == "ws":
                    self.servers[service] = await serve_ws(
                        sessionFactory, host=host, port=service.port, reuse_address=True, max_size=CONFIG.maxSendRecv
                    )
                else:
                    self.servers[service] = await serve_rs(
                        sessionFactory, host=host, port=service.port, reuse_address=True
                    )
            except Exception as e:
                Logger.error(f"{self._LOGGER_TITLE} Cannot start server {service.address}, {e}")
            else:
                Logger.info(f"{self._LOGGER_TITLE} Server started, listening on {service.address}")

    async def _stopServers(self, services: List[Service]) -> None:
        """
        Stop the servers in the given service list.
        """
        serverToStop = {service: self.servers.pop(service) for service in set(services).intersection(self.servers)}

        # Close all before waiting
        for service, server in serverToStop.items():
            Logger.info(f"{self._LOGGER_TITLE} Closing down server for {service}")
            server.close()

        # No value in doing these concurrently
        for server in serverToStop.values():
            await server.wait_closed()

    async def _manageServers(self) -> None:
        """
        manage server status.
        when session count reaches the maximum, stop listening for incoming connections
        """
        paused = False
        lowWatermark = self.maxSessions * 19 // 20
        while True:
            await self.sessionEvent.wait()
            self.sessionEvent.clear()
            if not paused and len(self.sessions) >= self.maxSessions:
                Logger.info(
                    f"{self._LOGGER_TITLE} Maximum sessions {self.maxSessions:,d} "
                    f"reached, stopping new connections until "
                    f"count drops to {lowWatermark:,d}"
                )
                await self._stopServers(service for service in self.servers)
                paused = True
            # Start listening for incoming connections if paused and
            # session count has fallen
            if paused and len(self.sessions) <= lowWatermark:
                Logger.info("{self._LOGGER_TITLE} Resuming listening for incoming connections")
                await self._startServers(service for service in self.localServices)
                paused = False

    # utils to extract services
    def _servicesToRun(self) -> List[Service]:
        """
        return a list of Service objects that are going to be run locally

        Services are read from config file, e.g. tcp://0.0.0.0:8000
        """

        def default_part(protocol, part):
            return defaultServices.get(protocol, {}).get(part)

        defaultServices = {protocol: {ServicePart.HOST: "all_interfaces"} for protocol in self.KNOWN_PROTOCOLS}
        services = self._parseServices(",".join(SERVICES), default_part)

        return services

    def _servicesToReport(self) -> List[Service]:
        """
        return a list of Service objects that are going to be reported to the network

        Services are read from config file, e.g. tcp://*************:8080
        """

        def ip_exist(input_str) -> bool:
            pattern = r"\w+://([\w\.-]+):\d+"
            return re.match(pattern, input_str) is not None

        def default_part(protocol, part):
            return defaultServices.get(protocol, {}).get(part)

        publicIP = "127.0.0.1"  # if it has address, not set 127.0.0.1

        for service in REPORT_SERVICES:
            if not ip_exist(service):
                publicIP = getPublicIp()
                if not publicIP:
                    raise ServiceError("Failed to get public IP address")
                break

        defaultServices = {protocol: {ServicePart.HOST: publicIP} for protocol in self.KNOWN_PROTOCOLS}

        services = self._parseServices(",".join(REPORT_SERVICES), default_part)

        for service in services:
            if isinstance(service.host, (IPv4Address, IPv6Address)):
                ipAddr = service.host
                if ipAddr.is_multicast or ipAddr.is_unspecified:
                    raise ServiceError(f"Bad IP address for report service: multicast or unspecified host: {ipAddr}")
            elif isinstance(service.host, str) and service.host.lower() == "localhost":
                raise ServiceError("Bad IP address for report service: localhost")

        return services

    def _parseServices(self, servicesString: str, defaultFunction: Optional[Callable]) -> List[Service]:
        """
        parse service string to a list of Service objects, using `aiorpcx.Service.from_string`

        example service string: 'tcp://*************:8000,tcp://*************:8080'
        """
        result: List[Service] = []
        for serviceString in servicesString.split(","):
            if not serviceString:
                continue
            try:
                service = Service.from_string(serviceString, default_func=defaultFunction)
            except Exception as e:
                raise ServiceError(f'"{serviceString}" invalid: {e}') from None
            if service.protocol not in self.KNOWN_PROTOCOLS:
                raise ServiceError(f'"{serviceString}" invalid: unknown protocol')
            result.append(service)

        # Find duplicate addresses
        serviceMap = {service.address: [] for service in result}
        for service in result:
            serviceMap[service.address].append(service)
        for address, services in serviceMap.items():
            if len(services) > 1:
                raise ServiceError(f"address {address} has multiple services")

        return result

    # server metadata
    def _serverMetadata(self):
        """
        return the server metadata dictionary

        includes:
            hosts: a dict of hosts and their ports, e.g. {'*************': {'tcpPort': 8080}}
            services: a list of report services, e.g. ['tcp://*************:8080']
            protocolVersion: the protocol version
            serverVersion: the vgraph server version
            nodeId: the public key of the node
        """
        hostsDictionary = dict()
        for service in self.reportServices:
            portDictionary = hostsDictionary.setdefault(str(service.host), {})
            if service.protocol not in portDictionary:
                portDictionary[f"{service.protocol}Port"] = service.port

        # TODO: Add current blockchain status
        # e.g. current chain id, current block height, current block hash, etc.
        return {
            "hosts": hostsDictionary,
            "services": [str(service) for service in self.reportServices],
            "protocolVersion": self.PROTOCOL_VERSION,
            "serverVersion": self.SERVER_VERSION,
            "nodeId": self.chain.signatureManager.getPublicKey(),
        }

    # ----- session management -----

    def addSession(self, session) -> None:
        """
        add a session to the session manager

        the type of session is self.sessionClass
        """
        # set the session event
        self.sessionEvent.set()

        # return the session groups
        # SessionGroup.name <-> session's ip address
        groups = (self.buildSessionGroup(self._ipAddrGroupName(session), 1.0),)
        groups = tuple(group for group in groups if group is not None)
        self.sessions[session] = groups
        for group in groups:
            group.sessions.add(session)
        Logger.debug(f"{self._LOGGER_TITLE} session {session.remote_address()} added")

    def removeSession(self, session) -> None:
        """
        remove a session from the session dictionary if it exists

        the type of session is self.sessionClass
        """
        # set the session event
        self.sessionEvent.set()

        # remove the session from the session dictionary
        groups = self.sessions.pop(session)
        for group in groups:
            group.retained_cost += session.cost
            group.sessions.remove(session)
        Logger.debug(f"{self._LOGGER_TITLE} session {session.remote_address()} removed")

    async def _clearStaleSessions(self):
        """
        cut off sessions that haven't done anything for a while(defined in config file, `session_timeout`)
        """
        while True:
            interval = SESSION_TIMEOUT // 4
            await asyncio.sleep(interval)
            staleCutoff = time.time() - SESSION_TIMEOUT
            staleSessions = [session for session in self.sessions if session.last_recv < staleCutoff]
            await self._disconnectSessions(staleSessions, "stale")
            del staleSessions

    async def _disconnectSessions(self, sessions, reason: str, forceAfter=1.0) -> None:
        """
        Disconnect sessions for a reason.

        sessions: a list of sessions to disconnect
        """
        if sessions:
            sessionIds = ", ".join(str(session.sessionId) for session in sessions)
            Logger.info(f"{self._LOGGER_TITLE} disconnecting sessions for {reason}: {sessionIds}")
            for session in sessions:
                await self.taskGroup.spawn(session.close(force_after=forceAfter))

    # ------ logging ------

    async def _logState(self) -> None:
        """
        log sessions periodically
        """
        logInterval = LOG_SESSION_INTERVAL
        if logInterval > 0:
            while True:
                await asyncio.sleep(logInterval)
                data = self._sessionData()
                Logger.info(f"{self._LOGGER_TITLE} Connected sessions:")
                for line in sessionsLines(data):
                    print(line)
                print()
                Logger.info(f"{self._LOGGER_TITLE} Current server state: {self._getInfo()}")

    def _getInfo(self) -> str:
        """
        A summary of server state.
        """
        sessions = self.sessions
        return f"""
PID: {os.getpid()}
Server version: {self.SERVER_VERSION}
Up time: {formatTime(time.time() - self.startTime)}

Session groups: {len(self.sessionGroups)}
Request counts: {json.dumps(self.methodCounts)}
Request total: {sum(self.methodCounts.values())}
Sessions count: {len(sessions)}
Error sessions: {sum(session.errors for session in sessions)}
Pending requests: {sum(session.unanswered_request_count() for session in sessions)}
"""

    def _sessionData(self) -> List[tuple]:
        """
        return a list of session info
        """
        now = time.time()
        sessions = sorted(self.sessions, key=lambda s: s.start_time)
        return [
            (
                session.sessionId,
                str(session.remote_address()),
                session.cost,
                session.extra_cost(),
                session.unanswered_request_count(),
                session.recv_count,
                session.recv_size,
                session.send_count,
                session.send_size,
                now - session.start_time,
            )
            for session in sessions
        ]

    # ------ utility functions ------

    def buildSessionGroup(self, name: Optional[str], weight: float) -> Optional[SessionGroup]:
        """
        Return a SessionGroup instance for the given name, creating it if necessary.
        """
        if name is None:
            return None
        group = self.sessionGroups.get(name)
        if group is None:
            group = SessionGroup(name, weight, set(), 0)
            self.sessionGroups[name] = group
        return group

    @staticmethod
    def _ipAddrGroupName(session) -> Optional[str]:
        """
        Return the name of the session group for the given session, based on its IP address.
        """
        host = session.remote_address().host
        if isinstance(host, (IPv4Address, IPv6Address)):
            return str(host)
        return "unknown address"

    def calculateMaxSessions(self) -> int:
        """
        return the maximum number of sessions to permit.
        Normally this is MAX_SESSIONS.
        However, to prevent open file exhaustion, adjust downwards
        if running with a small open file rlimit.
        """
        try:
            import resource

            nofileLimit = resource.getrlimit(resource.RLIMIT_NOFILE)[0]
            value = max(0, min(MAX_SESSIONS, nofileLimit - 350))
            if value < MAX_SESSIONS:
                Logger.warning(
                    f"{self._LOGGER_TITLE} reduced max sessions to {value:,d}, "
                    f"because your open file limit is {nofileLimit:,d}"
                )
        except ImportError:
            value = 512  # that is what returned by stdio's _getmaxstdio()
        return value
