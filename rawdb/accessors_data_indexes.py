import vgraphdb
from rawdb.scheme import ContractDataIndexPrefix, contractDataIndexKey, contractDataIndexPrefix

# Maximum allowed length for index keys (total including prefix and contract address)
INDEX_KEY_LENGTH_LIMIT = 255


def calculateIndexKeyLength(contractAddress: bytes, index: bytes) -> tuple:
    """
    Calculate the full index key length and its components.

    Args:
        contractAddress: The contract address
        index: The index key

    Returns:
        A tuple of (prefixLen, contractAddressLen, indexLen, totalKeyLength)
    """
    prefixLen = len(ContractDataIndexPrefix)
    contractAddressLen = len(contractAddress)
    indexLen = len(index)
    totalKeyLength = prefixLen + contractAddressLen + indexLen

    return (prefixLen, contractAddressLen, indexLen, totalKeyLength)


def validateIndexKeyLength(contractAddress: bytes, index: bytes, keyType: str = "Index") -> None:
    """
    Validate that the index key length does not exceed the limit.
    Raises ValueError if the key is too long.

    Args:
        contractAddress: The contract address
        index: The index key
        keyType: The type of key for error messages (e.g., "Index", "Start index", "End index")
    """
    _, _, _, totalKeyLength = calculateIndexKeyLength(contractAddress, index)

    if totalKeyLength > INDEX_KEY_LENGTH_LIMIT:
        raise ValueError(f"{keyType} key exceeds limit: {totalKeyLength}/{INDEX_KEY_LENGTH_LIMIT} bytes")


def writeContractDataIndex(db: vgraphdb.KeyValueWriter, contractAddress: bytes, index: bytes, dataKey: bytes):
    validateIndexKeyLength(contractAddress, index)
    db.put(contractDataIndexKey(contractAddress, index), dataKey)


def readContractDataIndex(db: vgraphdb.KeyValueReader, contractAddress: bytes, index: bytes):
    validateIndexKeyLength(contractAddress, index)
    return db.get(contractDataIndexKey(contractAddress, index))


def deleteContractDataIndex(db: vgraphdb.KeyValueWriter, contractAddress: bytes, index: bytes):
    validateIndexKeyLength(contractAddress, index)
    db.delete(contractDataIndexKey(contractAddress, index))


def getContractDataIndexIterator(
    db: vgraphdb.Iteratee, contractAddress: bytes, reverse: bool, start: bytes = b"", end: bytes = b""
) -> vgraphdb.DBIterator:
    # If start or end are provided, check their lengths
    if start:
        validateIndexKeyLength(contractAddress, start, "Start index")

    if end:
        validateIndexKeyLength(contractAddress, end, "End index")

    if reverse:
        return db.newReverseIterator(contractDataIndexPrefix(contractAddress), start, end)
    else:
        return db.newIterator(contractDataIndexPrefix(contractAddress), start, end)
