import os
from logging import <PERSON><PERSON><PERSON><PERSON>, DEBUG, ERROR, INFO, <PERSON>R<PERSON>NG

import pytest
from kivy import Logger


# Convert string to log level
def get_level_from_str(level_str):
    level_str = str(level_str).upper()
    return {
        "CRITICAL": CRIT<PERSON><PERSON>,
        "ERROR": ERROR,
        "WARNING": WARNING,
        "INFO": INFO,
        "DEBUG": DEBUG,
    }.get(level_str, CRITICAL)


# Add pytest command line option
def pytest_addoption(parser):
    parser.addoption(
        "--kivy-logger-level",
        action="store",
        default=None,
        help="Set Kivy Logger level (CRITICAL, ERROR, WARNING, INFO, DEBUG)",
    )


# Auto fixture, prioritize command line argument, then environment variable
@pytest.fixture(scope="session", autouse=True)
def disable_kivy_logging(request):
    """
    Automatically set Kivy Logger level based on environment variable or command line argument
    """
    # Priority: command line argument
    level_str = request.config.getoption("--kivy-logger-level")
    if not level_str:
        # Then environment variable
        level_str = os.environ.get("KIVY_LOGGER_LEVEL", None)

    if level_str:
        level = get_level_from_str(level_str)
        Logger.setLevel(level)
        Logger.disabled = False
    else:
        Logger.setLevel(CRITICAL)
        Logger.disabled = True
    yield
