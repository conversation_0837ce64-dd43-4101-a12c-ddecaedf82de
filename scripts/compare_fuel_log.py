import os
import subprocess
from datetime import datetime


def extract_fuel_logs(log_file):
    """Extract lines containing 'consume fuel' from the log file,
    and only keep the part after '[Contract Executor]'"""
    fuel_logs = []
    marker = "[Contract Executor]"
    with open(log_file, "r", encoding="utf-8") as f:
        for line in f:
            if "consume fuel" in line.lower():
                index = line.find(marker)
                if index != -1:
                    fuel_logs.append(line[index:].strip())
    return fuel_logs


def save_logs(logs, filename):
    """Save logs to a file"""
    with open(filename, "w", encoding="utf-8") as f:
        for log in logs:
            f.write(f"{log}\n")


def run_pytest_and_collect_logs():
    """Run pytest and collect logs"""
    # Create temp directory
    temp_dir = "temp"
    os.makedirs(temp_dir, exist_ok=True)
    # Create log file name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(temp_dir, f"pytest_fuel_log_{timestamp}.log")

    print("run_pytest_and_collect_logs")
    # Run pytest and capture output, pass Logger level argument
    result = subprocess.run(
        ["pytest", "-s", "--kivy-logger-level=DEBUG"], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True
    )
    # Save full log
    with open(log_file, "w", encoding="utf-8") as f:
        f.write(result.stdout)

    # Extract fuel related logs
    fuel_logs = extract_fuel_logs(log_file)
    fuel_log_file = os.path.join(temp_dir, f"fuel_logs_{timestamp}.log")
    save_logs(fuel_logs, fuel_log_file)

    return fuel_log_file


def compare_logs(file1, file2):
    """Compare the contents of two log files. Return (is_equal, diff_lines)"""
    with open(file1, "r", encoding="utf-8") as f1, open(file2, "r", encoding="utf-8") as f2:
        logs1 = f1.readlines()
        logs2 = f2.readlines()

    if len(logs1) != len(logs2):
        diff = [f"Line count differs: {len(logs1)} vs {len(logs2)}"]
        # Show first 10 lines of each for reference
        diff.append("--- First file (first 10 lines) ---")
        diff.extend(logs1[:10])
        diff.append("--- Second file (first 10 lines) ---")
        diff.extend(logs2[:10])
        return False, diff

    diff_lines = []
    for idx, (l1, l2) in enumerate(zip(logs1, logs2, strict=False)):
        if l1.strip() != l2.strip():
            diff_lines.append(f"Line {idx + 1} differs:\n  file1: {l1.strip()}\n  file2: {l2.strip()}")
    return len(diff_lines) == 0, diff_lines


def main():
    print("test_fuel_logs_consistency")
    # First run
    first_run_log = run_pytest_and_collect_logs()
    print("first_run_log", first_run_log)
    # Second run
    second_run_log = run_pytest_and_collect_logs()
    print("second_run_log", second_run_log)

    # Compare results
    is_equal, diff_lines = compare_logs(first_run_log, second_run_log)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_log_file = os.path.join("temp", f"compare_result_{timestamp}.log")
    with open(result_log_file, "w", encoding="utf-8") as f:
        if is_equal:
            print("Fuel logs are consistent between runs.")
            f.write("Fuel logs are consistent between runs.\n")
        else:
            print("Fuel logs are NOT consistent between runs!")
            f.write("Fuel logs are NOT consistent between runs!\n")
            f.write("\nDiff details:\n")
            for line in diff_lines:
                print(line)
                f.write(line + "\n")
    print(f"Compare result written to: {result_log_file}")


if __name__ == "__main__":
    main()
